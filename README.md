# <PERSON><PERSON> Spot On APIs #

### What is this repository for? ###

* Quick summary : This repository maintains APIs for <PERSON><PERSON> Spot On clients. Contains authentication and authorization
  of spot on users.
* Version : v1

### How do I get set up? ###

* Dependencies : JDK 11, MySQL
* Database configuration : Create utf8 database named as `spoton`
* Deployment instructions : Simply run spring boot project and it will start API at : http://localhost:8080
* If run through Intellij (or other IDE), add the SPRING_PROFILES_ACTIVE=local env variable.
* If run through command line
  `
    $ mvn install -DskipTests (or mvn clean install -DskipTests)
    $ mvn spring-boot:run -Dspring-boot.run.profiles=local
  `

### Contribution guidelines ###

* Code review : Code cannot be pushed to master or develop branch, always create feature branch from develop and raise
  PR against develop once feature is completed.
* Other guidelines : Commented code should not be there and code should be formatted with standard google style code for
  Java

### Who do I talk to? ###

* Repo owner or admin : <EMAIL> / <EMAIL>

## trigger build again

## Checklists

| Points |                                                                   One Liner                                                                   |                        References                        |
|:------:|:---------------------------------------------------------------------------------------------------------------------------------------------:|:--------------------------------------------------------:|
|   1    |                                   Default timezone in RDS is UTC. <br/>Load timezone tables in the local DB                                   | https://repost.aws/knowledge-center/rds-change-time-zone |
|   2    | Add SLA logs for new API's in controller . <br/>Include 'LoggerUtil.logSLA' for all the api controller, it is used for performance monitoring |                   DashboardController                    |
|   3    |                                For local api development, set the origin, bearer token in the requests header                                 |                     SimpleCORSFilter                     |
|   4    |                                                                                                                                               |                                                          |


