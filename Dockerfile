# Start from an official Java 11 image
FROM public.ecr.aws/docker/library/openjdk:11.0.16-jre-slim

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    fontconfig \
    libfreetype6 && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory inside the container
WORKDIR /app

# Copy the JAR file from the target directory of the project to the container's /app directory
COPY target/spot-on-api-0.0.1-SNAPSHOT.jar /app/spot-on-api-0.0.1-SNAPSHOT.jar

# Expose the port that your Spring Boot application will run on
EXPOSE 8080

# Define the command to run your application
ENTRYPOINT ["java", "-jar", "/app/spot-on-api-0.0.1-SNAPSHOT.jar"]
