{"info": {"_postman_id": "83385ec5-fd4f-44c7-8370-d8e28bdb50e4", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "16425981"}, "item": [{"name": "Dashboard APIs", "item": [{"name": "Avg moves", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/avg-moves?clientId={{CLIENT_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "avg-moves"], "query": [{"key": "clientId", "value": "{{CLIENT_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Daily Hourly Avg Moves", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/daily-hourly-move-average?clientId={{CLIENT_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "daily-hourly-move-average"], "query": [{"key": "clientId", "value": "{{CLIENT_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "In transit spots", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/in-transit-spots?clientId={{CLIENT_ID}}&isRefresh=true", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "in-transit-spots"], "query": [{"key": "clientId", "value": "{{CLIENT_ID}}"}, {"key": "isRefresh", "value": "true"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Get all spots by location", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/spots-of-location?locationId={{LOCATION_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "spots-of-location"], "query": [{"key": "isRefresh", "value": "true", "disabled": true}, {"key": "locationId", "value": "{{LOCATION_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Weekly total spots", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/weekly-total-spots?clientId={{CLIENT_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "weekly-total-spots"], "query": [{"key": "weekStart", "value": "2025-06-01", "disabled": true}, {"key": "isRefresh", "value": "true", "disabled": true}, {"key": "clientId", "value": "{{CLIENT_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Spot Counts", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/dashboard/spot-counts?clientId={{CLIENT_ID}}&spotId={{SPOT_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "dashboard", "spot-counts"], "query": [{"key": "isRefresh", "value": "true", "disabled": true}, {"key": "clientId", "value": "{{CLIENT_ID}}"}, {"key": "spotId", "value": "{{SPOT_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}]}, {"name": "Jobs APIs", "item": [{"name": "Get all jobs", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/jobs?isActive=true&status=OPEN&page=0&size=50&isBoxTruckOrVan=true", "host": ["{{BACKEND}}"], "path": ["v1", "jobs"], "query": [{"key": "isActive", "value": "true"}, {"key": "status", "value": "OPEN"}, {"key": "page", "value": "0"}, {"key": "size", "value": "50"}, {"key": "isBoxTruckOrVan", "value": "true"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Update Status", "request": {"method": "PATCH", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"status\": \"IN_TRANSIT\",\r\n    \"notes\": \"Completed job\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/jobs/{{JOB_ID}}/status", "host": ["{{BACKEND}}"], "path": ["v1", "jobs", "{{JOB_ID}}", "status"]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Update Route Status", "request": {"method": "PATCH", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"status\": \"COMPLETED\",\r\n    \"notes\": \"Completed intermediate job\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/jobs/{{JOB_ID}}/{{ROUTE_ID}}/status?clientId={{CLIENT_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "jobs", "{{JOB_ID}}", "{{ROUTE_ID}}", "status"], "query": [{"key": "clientId", "value": "{{CLIENT_ID}}"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Delete job", "request": {"method": "DELETE", "header": [{"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/jobs/{{JOB_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "jobs", "{{JOB_ID}}"]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Create job", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"assignedToUserId\": \"\",\r\n    \"description\": \"\",\r\n    \"dropLocationId\": \"84d8597f-3267-4241-b31a-96d018897a0a\",\r\n    \"dropSpotId\": \"f4034f58-69b0-42a4-9040-11bd727bfa65\",\r\n    \"fleetId\": \"2f86ef5f-1f8a-4d90-9b6a-3d9be12fb69e\",\r\n    \"fleetStatus\": null,\r\n    \"pickupLocationId\": \"5b56183e-ef68-4211-b1b2-f981e9d5d99b\",\r\n    \"pickupSpotId\": \"87bf31b7-c22f-4027-ab7b-3fc8129c6bc9\",\r\n    \"priority\": \"LOW\",\r\n    \"scheduledOrAdd\": \"add\",\r\n    \"scheduledDateTime\": \"\",\r\n    \"clientId\": \"a5d18159-1b92-4b18-a141-f1dfb72a650e\",\r\n    \"isEdit\": \"\",\r\n    \"fleetAndHotTrailer\": \"2222222\",\r\n    \"sequenceAsn\": \"\",\r\n    \"trailerType\": \"\",\r\n    \"carrier\": \"\",\r\n    \"bucket\": \"BUCKET_DRIVER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/jobs", "host": ["{{BACKEND}}"], "path": ["v1", "jobs"]}}, "response": []}, {"name": "Create job boxtruck", "event": [{"listen": "test", "script": {"exec": ["// Get the Location header from the response\r", "var locationHeader = pm.response.headers.get(\"Location\");\r", "\r", "if (locationHeader) {\r", "    // Extract the last part of the URL after the last slash\r", "    var id = locationHeader.split(\"/\").pop();\r", "    \r", "    // Set it as an environment variable\r", "    pm.environment.set(\"JOB_ID\", id);\r", "    \r", "    console.log(\"Job ID:\", id);\r", "} else {\r", "    console.log(\"Location header not found\");\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"assignedToUserId\": \"9ac87f78-6345-45cb-a401-e15c6d96681f\",\r\n    \"description\": \"First boxtruck job\",\r\n    \"dropLocationId\": \"44e38a87-14b8-44ae-a583-743b742b9a14\",\r\n    \"dropSpotId\": \"e2845f6c-a8da-47c0-88f8-1de9c0f1772d\",\r\n    \"fleetId\": \"2f86ef5f-1f8a-4d90-9b6a-3d9be12fb69e\",\r\n    \"fleetStatus\": null,\r\n    \"pickupLocationId\": \"44e38a87-14b8-44ae-a583-743b742b9a14\",\r\n    \"pickupSpotId\": \"9a3c61b7-93c7-4cd9-a08e-b8a123a96bd3\",\r\n    \"priority\": \"LOW\",\r\n    \"clientIds\": \"3391170d-276a-436c-9dfb-47810f566c06\",\r\n    \"isEdit\": \"\",\r\n    \"fleetAndHotTrailer\": \"2222222\",\r\n    \"sequenceAsn\": \"\",\r\n    \"trailerType\": \"Container\", // for testing\r\n    \"carrier\": \"\",\r\n    \"bucket\": \"NIL\",\r\n    \"isBoxTruckOrVan\": true,\r\n    \"routeList\": [\r\n        {\r\n            \"locationId\": \"e1bae329-a938-44d6-97ba-8571f4bc8c54\",\r\n            \"spotId\": \"312e87df-d276-4d97-8c66-4386936e7109\",\r\n            \"type\": \"PICK_UP\",\r\n            \"notes\": \"One more pickup\" // optional\r\n        },\r\n        {\r\n            \"locationId\": \"e1bae329-a938-44d6-97ba-8571f4bc8c54\",\r\n            \"spotId\": \"786d4c3e-4838-452b-b550-4e43f8c65ea9\",\r\n            \"type\": \"DROP\",\r\n            \"notes\": \"first drop\"\r\n        },\r\n        {\r\n            \"locationId\": \"bcc61e79-71af-4618-b8d0-609dd6b8db6a\",\r\n            \"spotId\": \"ad3aba74-61ee-4679-ac9c-8b9eab821ce9\",\r\n            \"type\": \"PICK_UP\",\r\n            \"notes\": \"One more pickup\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/jobs?clientId=3391170d-276a-436c-9dfb-47810f566c06", "host": ["{{BACKEND}}"], "path": ["v1", "jobs"], "query": [{"key": "clientId", "value": "3391170d-276a-436c-9dfb-47810f566c06"}]}}, "response": []}]}, {"name": "Users", "item": [{"name": "Get User By ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/users/{{USER_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "users", "{{USER_ID}}"]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Users", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/users?isActive=true&page=0&size=10&sort=createdDate,desc&availableUsersOnly=true", "host": ["{{BACKEND}}"], "path": ["v1", "users"], "query": [{"key": "isActive", "value": "true"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "sort", "value": "createdDate,desc"}, {"key": "ignoreLoggedUser", "value": "true", "disabled": true}, {"key": "availableUsersOnly", "value": "true"}]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}, {"name": "Activate User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, /"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"userId\": \"f7609f5a-5dc7-4f3c-9a53-86ddfc1aada0\",\r\n    \"password\": \"admin\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/users/activate", "host": ["{{BACKEND}}"], "path": ["v1", "users", "activate"]}, "description": "Generated from cURL: curl --location 'http://api.ablair-spoton.com:8080/v1/dashboard/avg-moves?clientId=32eacfef-9302-4e3b-8912-dbde82277d2b' \\\n--header 'Accept: application/json, text/plain, /' \\\n--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\n--header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOhfscOlIlVcSYZQhN9S-2IweozXiejkPv3p2qqz_KGScJiz4O9pVxd_gdZP6xZ3jYXU0N_jX4em1FZdK1wTPjzv6ULZMKaxPogcBuppgTakpOZFP3pCqNFa36de9zxdu5GfVQVA_jqFKVGVlEUFZgEy1Ee05lt05gjcnoI4EE1AKLHrE2Oze4dg2CoL5elrB7gW7756KUclF58STkfycJ3OHuT9poS6kMHj8al3BrvAr4beKfEjbnTMdgbTEeqfQEzNY7-zq089UftNyl7klwU_zO1h8mp5wyknlFqbclBI8yBRNyGNcAF54hJIKywK4rDC0eTwy9JQl7gWng-_DQ' \\\n--header 'Connection: keep-alive' \\\n--header 'Origin: http://localhost:4200' \\\n--header 'Referer: http://localhost:4200/'"}, "response": []}]}, {"name": "Fleets", "item": []}, {"name": "Carrier APIs", "item": []}, {"name": "Clients APIs", "item": []}, {"name": "Location APIs", "item": [{"name": "Get Location (of client) by Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/clients/{{CLIENT_ID}}/locations/{{LOCATION_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "clients", "{{CLIENT_ID}}", "locations", "{{LOCATION_ID}}"]}}, "response": []}, {"name": "[wip] Get Locations of client", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/clients/{{CLIENT_ID}}/locations", "host": ["{{BACKEND}}"], "path": ["v1", "clients", "{{CLIENT_ID}}", "locations"]}}, "response": []}]}, {"name": "UserAvailability APIs", "item": [{"name": "Active/Deactive", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"active\" : true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BACKEND}}/v1/userAvailability/{{USER_AVAILABILITY_ID}}", "host": ["{{BACKEND}}"], "path": ["v1", "userAvailability", "{{USER_AVAILABILITY_ID}}"]}}, "response": []}]}, {"name": "Spot APIs", "item": [{"name": "[wip] Get Spots of client", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Origin", "value": "{{UI}}", "type": "text"}], "url": {"raw": "{{BACKEND}}/v1/clients/{{CLIENT_ID}}/spots?page=0&size=30", "host": ["{{BACKEND}}"], "path": ["v1", "clients", "{{CLIENT_ID}}", "spots"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "30"}]}}, "response": []}]}, {"name": "OAuth token", "event": [{"listen": "test", "script": {"exec": ["var body;\r", "try {\r", "    body = JSON.parse(responseBody);\r", "    pm.environment.set('refresh_token', body['refresh_token']);\r", "    pm.environment.set('access_token', body['access_token']);\r", "} catch (e) {\r", "    console.log(e);\r", "}"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7"}, {"key": "Authorization", "value": "Basic c3BvdG9uLWNsaWVudHM6c3BvdG9uLWNsaWVudHMtc2VjcmV0QDIwMjI="}, {"key": "Origin", "value": "{{UI}}"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "type": "text"}, {"key": "username", "value": "{{CLIENT}}", "type": "text"}, {"key": "password", "value": "{{PASSWORD}}", "type": "text"}]}, "url": {"raw": "{{BACKEND}}/oauth/token", "host": ["{{BACKEND}}"], "path": ["o<PERSON>h", "token"]}, "description": "Generated from cURL: curl --location 'http://localhost:8080/oauth/token' \\\r--header 'Accept: application/json, text/plain, */*' \\\r--header 'Accept-Language: en-US,en;q=0.9,en-IN;q=0.8,hi;q=0.7' \\\r--header 'Authorization: Basic c3BvdG9uLWNsaWVudHM6c3BvdG9uLWNsaWVudHMtc2VjcmV0QDIwMjI=' \\\r--header 'Connection: keep-alive' \\\r--header 'Content-Type: application/x-www-form-urlencoded' \\\r--header 'Origin: http://localhost:4200' \\\r--data-urlencode 'grant_type=password' \\\r--data-urlencode 'username=<EMAIL>' \\\r--data-urlencode 'password=admin123'"}, "response": []}]}