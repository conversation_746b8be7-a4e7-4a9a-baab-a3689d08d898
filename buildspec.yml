version: 0.2

phases:
  install:
    runtime-versions:
      java: corretto11
    commands:
      - echo "Installing Docker and Maven dependencies..."
      - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &
      - timeout 15 sh -c "until docker info; do echo .; sleep 1; done"
      - mvn install -DskipTests

  pre_build:
    commands:
      - echo log in to Amazon ECR...
      - aws --version
      - echo $AWS_DEFAULT_REGION
      - REPOSITORY_URI=$ECR_URI
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
      - echo "Forcing image tag to latest..."
      - IMAGE_TAG="latest"

  build:
    commands:
      - echo "Build started on $(date)"
      - echo "Building the Docker image..."
      - docker build -t $REPOSITORY_URI:latest .
      - echo "Tagging the Docker image with latest..."
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG

  post_build:
    commands:
      - echo "Build completed on $(date)"
      - echo "Pushing the Docker images to Amazon ECR..."
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo "Writing image definitions file..."
      - printf '[{"name":"%s","imageUri":"%s"}]' "$APP_NAME" "$REPOSITORY_URI:latest" > imageDefinitions.json

artifacts:
  files:
    - imageDefinitions.json
