spring:
  main:
    allow-bean-definition-overriding: true

  servlet:
    multipart:
      enabled: false

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************
    username: root
    password: fSuULjSNSUeVoL94
  flyway:
    enabled: false
  jpa:
    database-platform: org.hibernate.dialect.MySQL57Dialect
    generate-ddl: true
    hibernate:
      ddl-auto: update
    open-in-view: false
    show-sql: false
  security:
    basic:
      enabled: false
    saml2:
      relyingparty:
        registration:
          adfs:
            identityprovider:
              entity-id: https://sts.windows.net/fe4b66bc-c221-46a6-a298-2303336cca07/
              metadata-uri: https://login.microsoftonline.com/fe4b66bc-c221-46a6-a298-2303336cca07/federationmetadata/2007-06/federationmetadata.xml?appid=e32b2a05-2f1d-4f6c-8344-d891181f7ff7
              singlesignon:
                url: https://login.microsoftonline.com/fe4b66bc-c221-46a6-a298-2303336cca07/saml2
            serviceprovider:
              entity-id: ad_integration_test # application name in the Azure AD
              assertion-consumer-service-location: /login/saml2/sso/
              registration-id: azure

management:
  endpoints:
    beans:
      enabled: false
    web:
      exposure:
        include: mappings

application:
  # 2 Days
  access-token-validity-in-seconds: 25920000
  # 7 Days
  refresh-token-validity-in-seconds: 604800
  jwt-signing-key: -----BEGIN RSA PRIVATE KEY-----MIIEpAIBAAKCAQEAxnicFNoPqs3b/CjBLLB6BvbxpPvT03g6fpGwM6goo5aqFvgRL4R4mfYDXuDZzOzhGke6z1c+uxC4uLf4EWAKLIjWDW9xNXxvBWIMGCHxh/L3v2elJgqh7I5I75PtwUXlokzeMJbCNXTu0fKMVaEaqnD0LizHsU6e4w9Zwgw2Rhyj/7V+XcYJYDTf8rAroQ7rIp+XwLAlDle3/OOlAjjtHO9S9aPfQlTdb9sRTXcvCkkEI2L3xQ5iRuBBJC7kSbgdF5ZAlZ7qvsFokXPXTfIATd2zzvs7F/AmiOuWr8vTDT/y2IB9eS4A2wYdMYJwRT2ClMksVRpitUvaV09vSAJIpQIDAQABAoIBAFi+ZUwNlSPSmKLPtlC9OFMU6EfVMopEICOoERmH/2PmYneSxFxy51owXqF1+O1gneaaXDWJ1Urf1zkBSbK7NtFi/hNT2VMJKLtHdBD2XIFlatHzDi1MY8radFJr2suo7Vz88fzX7E7q0j6eLZG8T6gWiZh7/W6SsQjmPERAIaSHLvalrHbrBuAN4/oBHU5O4LC0NVDZN7uweXs/e/IGPw31c2iMI1PKa9NmmKly+l3Owltx55MqjhIZT5x6PjQBTBkrhbrdA+/jYtR2tLYK13uWQCShaYjUuRTSFyntlvV6JjJg5JIQMzg4So7xeaKevNS+vNjaeYXMbIP8ylQB+gECgYEA/W2/FSbJ+uCiSN6+GGMATTih8jmYxx19JfwtzBnAuG+Scl/HkBWM5bedIlVu82tP/ckPthP9z1mNpS3zZnXTzU/tOq9EtGOXjHNi6zRKarvum2AD71ijgWtZaTvBmdtXkPLwMBpHCYVR43kRqabvvCg2MbL3p7kQI/c1OoDGtvECgYEAyHwd8MHtMJOoo7qDqHVfNOA7y1zkRhDuO8xbEdqK7SUckX1VdhprlDfWHTwAX0HNV+SYsu+YkLhIdeRlDS712kbKOr/R8OoXLQyUpqjt1+hV3kLsRQS2pgK0zqZjQMk0qwetUKEvLyvzPYBJASYX04hbwOjMZdv2gMSFJk3wdPUCgYEApJ7Eb+3pZ/DBGgquetLcYEz4TYsCZd0O7Ty5VwoneObovyjeTgbhplq2VJYgYziDFnyjmwezaBxo+TI3GIhTU9umYud0/qRuzgop6FToPhrjrMQa6j+uviPISaZKZTHo2LwmL2jyWgnjHpsHUSeiNJv+UBxL6QQ6qtIrHlyGOFECgYEAhOvdgfFhS/KqPZt8jOT8sXb1zfgRlO7GtLjhrG2j7GTNkxxw2/PaXZPDjvBoIr/i4CI8p245TcIQsLEz2lDHSefjTp49GChsIz8TE4gu4RY2UD04nu3oFxr2O4iPh7WfCMH3Q90KBpFyHNWAEZXq+CGRC86NLf9vaKJi0Smdgj0CgYA2uRvS7UVaDFl6BrHFLxQS7UJoUiHhO5zha+hm6wKRPD41rgsHPyx/stxzsL1jUQSIA3LG/N2jDSYsGuiIzYB0Fp3js43bXOlLyJs6A4VeXyV/cFuj9b31/+r9OZOSp7zrHTXPYnHnlFYMKLuy+D4b70dBldPaEB1ScWvaHp6KHw==-----END RSA PRIVATE KEY-----
  jwt-public-key: AAAAB3NzaC1yc2EAAAADAQABAAABAQDGeJwU2g+qzdv8KMEssHoG9vGk+9PTeDp+kbAzqCijlqoW+BEvhHiZ9gNe4NnM7OEaR7rPVz67ELi4t/gRYAosiNYNb3E1fG8FYgwYIfGH8ve/Z6UmCqHsjkjvk+3BReWiTN4wlsI1dO7R8oxVoRqqcPQuLMexTp7jD1nCDDZGHKP/tX5dxglgNN/ysCuhDusin5fAsCUOV7f846UCOO0c71L1o99CVN1v2xFNdy8KSQQjYvfFDmJG4EEkLuRJuB0XlkCVnuq+wWiRc9dN8gBN3bPO+zsX8CaI65avy9MNP/LYgH15LgDbBh0xgnBFPYKUySxVGmK1S9pXT29IAkil

  admin-portal-base-url: https://app.spoton-northstar.com
  api-base-url: https://api.spoton-northstar.com/

  client-id: spoton-clients
  client-secret-value: spoton-clients-secret@2022
  client-secret: $2a$12$jEzjAtW63FrsfK4RntWvOuOmRjFuvLtEW3LpJBBUIGcL8Ould53eu
  weather-api-base-url: https://api.openweathermap.org/data/2.5/weather?
  weather-api-key: 032e2866b87e9ffd5b25902bbccb59e1
  context: ''
  version: v1
  cors-url: 'https://app.spoton-northstar.com'
  cors-url-alias: 'https://app.spoton-northstar.com'
  scopes:
    - read
    - write
    - trust
  grant-types:
    - password
    - authorization_code
    - refresh_token
    - implicit
  public-endpoints:
    - /v2/api-docs
    - /configuration/ui
    - /swagger-resources
    - /configuration/security
    - /swagger-ui/**
    - /webjars/**
    - /actuator/**
    - /public/**
    - /login
    - /oauth2/**
    - /oauth/**
    - /saml2/**
    - /saml2-success/**
  swagger-doc-version: 2
  email-from-address: <EMAIL>
  email-from-name: GoAblair
  reset-password-link-expired-in-hours: 1
  reset-password-url: /resetPassword
  job:
    auto-send-notification:
      time: 10000
  # File Props
  cdn1: https://d35esowdv27xq4.cloudfront.net
  cdn:
    base:
      url: https://ablair-spoton-resource-staging.s3.us-east-1.amazonaws.com
    bucket: ablair-spoton-resource-staging
  location:
    map-image:
      path: location-map-image
  #jwt-token-for-creating-new-user-through-email-validation
  jwt-secret: AAAAB3NzaC1yc2EAAAADAQABAAABAQDGeJwU2g+qzdv8KMEssHoG9vGk+9PTeDp+kbAzqCijlqoW+BEvhHiZ9gNe4NnM7OEaR7rPVz67ELi4t/gRYAosiNYNb3E1fG8FYgwYIfGH8ve/Z6UmCqHsjkjvk+3BReWiTN4wlsI1dO7R8oxVoRqqcPQuLMexTp7jD1nCDDZGHKP/tX5dxglgNN/ysCuhDusin5fAsCUOV7f846UCOO0c71L1o99CVN1v2xFNdy8KSQQjYvfFDmJG4EEkLuRJuB0XlkCVnuq+wWiRc9dN8gBN3bPO+zsX8CaI65avy9MNP/LYgH15LgDbBh0xgnBFPYKUySxVGmK1S9pXT29IAkil
  expiration-time: 864000000
  certificates:
    idp-certificate: classpath:/saml2-sso-idp/signature.cer
    sp-certificate: classpath:/credentials/rp-certificate.cer
    sp-key: classpath:/credentials/rp-key.key
