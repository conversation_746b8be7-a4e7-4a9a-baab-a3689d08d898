
<configuration>
	<property name="APP_NAME" value="spoton-api" />
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg [%X{OPERATION-NAME}, SLA: %X{SLA} ms]%n</pattern>
		</encoder>
		 <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
            <jsonFormatter
                    class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter">
                <prettyPrint>false</prettyPrint>
            </jsonFormatter>
            <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
            <appendLineSeparator>true</appendLineSeparator>
        </layout>
	</appender>
	
	<logger name="org.springframework" level="ERROR" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>

	<logger name="com.ma" level="DEBUG">
		<appender-ref ref="STDOUT" />
	</logger>

	<!--<springProfile name="local | stage | uat">

		<logger name="org.springframework" level="DEBUG" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>

		<logger name="com.ma" level="INFO">
			<appender-ref ref="STDOUT" />
		</logger>
	</springProfile>

	<springProfile name="prod">

		<logger name="org.springframework" level="INFO">
			<appender-ref ref="STDOUT" />
		</logger>

		<logger name="org.apache" level="ERROR">
			<appender-ref ref="STDOUT" />
		</logger>

		<logger name="com.ma" level="INFO">
			<appender-ref ref="STDOUT" />
		</logger>

		<logger name="com.ma" level="ERROR">
			<appender-ref ref="STDOUT" />
		</logger>
		
	</springProfile>-->
</configuration>