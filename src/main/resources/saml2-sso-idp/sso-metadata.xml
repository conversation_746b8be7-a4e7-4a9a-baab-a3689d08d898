<?xml version="1.0" encoding="utf-8"?>
<EntityDescriptor ID="_aac77932-3a7e-4b3b-9f5b-8c5a0cff255f"
                  entityID="https://sts.windows.net/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/"
                  xmlns="urn:oasis:names:tc:SAML:2.0:metadata">
    <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
        <SignedInfo>
            <CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" />
            <SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256" />
            <Reference URI="#_aac77932-3a7e-4b3b-9f5b-8c5a0cff255f">
                <Transforms>
                    <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" />
                    <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" />
                </Transforms>
                <DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" />
                <DigestValue>DMEOxqp9Q9fMiRZ4NNnOQxcE1VB6kng3OZulK4gAwR0=</DigestValue>
            </Reference>
        </SignedInfo>
        <SignatureValue>
            WwdNQKYqc8JSJEsWQlRQXt+JOcxfvXUO80/jjl0nkPQyyVOweZITVn2UFKELrhxNq+mH+j5Ldold1LWz1MHAbCP+t00V+wk5A1avYQmk65QVgqVKWPLJ3Yny0TyN5+4fUQLdDx9eJzLps2Lmg+35UdQW1p7EbcPGixn8Iq57SJMhgKwesvPnqsvxOj0ItpMFEP5b0frnKdcvx+Jo3/SkM/esYfDXswib4k9VXwaSnFl21DTmqvAUJK1pnOor3B39MVAtumOa/YUPG/LPxQ41arLpZW04+qfNRVM/2//tpbUwdL/kHl8OiNq6V/Z7EY4KCZ9NuHZhX5+rXZN+mjUZQQ==</SignatureValue>
        <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
            <ds:X509Data>
                <ds:X509Certificate>
                    MIIC8DCCAdigAwIBAgIQQM4ucCiSA6JEttpV6+4uJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTEwMDIxNTU0NTBaFw0yODEwMDIxNTU0NTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt5mUyrEgySAXARxb/doUh7fm9F48VLOpyLPySFoO4BGTHJjm1IFQoqHSeMimANvCORQGEevC3ni5YCuHHNxa4ySArqo7tp2Ue3spAc4oYjciUbuqCw5t7YxgKsdTcIlRV4/jWgDdeu/mlKNzXgNsU+FY9OlKQVYGle3u708WAE1iQ3f7Vd4ZmF+8lnmd3mm91zTAgg8uDncCZdm5QeslT1lHxDQhPSaF9RWdvQJNtIUk9YMDcNI8k1/v/3VEmTdcek2bmrNgp5ILV1tfIjpeXAP2Ngyh1empoD0ogu1sMjJX9YcE5R/oMn4+E3D7bpTQ9ZJz888fUrme9+k2MXfJcQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBTyrs2lSkthIxcqpwP0mrcC0nZvdQQe2Cis/+nqoGdp+sU0opBljWApd0zsBCxB9fZqsU2+hYxHiED1F8LAr1ikfXg8IBTU+YDoimCg63s6ZYu199QXpQo9lVDTKLArUQf896d3gDPZyS9dednCkKvBNUWf75HiEX6MTA91drCOjeJ8GB2x0Rjgokw/FdEDpIosm1wMx6xufoU3P1hJpdixLA8Pyg1VndE2eDODTThfV1+7l/YVqWurk89DTm5z6lJjyXgyJHG1wuGYN+1DYxebvJfXAa+QM0nz+gF1/XsKCnGKN3R5aeiZpzo9w9BJY4s9OR6e1v698XguW3+0xEQ</ds:X509Certificate>
            </ds:X509Data>
        </ds:KeyInfo>
    </Signature>
    <RoleDescriptor xsi:type="fed:SecurityTokenServiceType"
                    protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQQM4ucCiSA6JEttpV6+4uJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTEwMDIxNTU0NTBaFw0yODEwMDIxNTU0NTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt5mUyrEgySAXARxb/doUh7fm9F48VLOpyLPySFoO4BGTHJjm1IFQoqHSeMimANvCORQGEevC3ni5YCuHHNxa4ySArqo7tp2Ue3spAc4oYjciUbuqCw5t7YxgKsdTcIlRV4/jWgDdeu/mlKNzXgNsU+FY9OlKQVYGle3u708WAE1iQ3f7Vd4ZmF+8lnmd3mm91zTAgg8uDncCZdm5QeslT1lHxDQhPSaF9RWdvQJNtIUk9YMDcNI8k1/v/3VEmTdcek2bmrNgp5ILV1tfIjpeXAP2Ngyh1empoD0ogu1sMjJX9YcE5R/oMn4+E3D7bpTQ9ZJz888fUrme9+k2MXfJcQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBTyrs2lSkthIxcqpwP0mrcC0nZvdQQe2Cis/+nqoGdp+sU0opBljWApd0zsBCxB9fZqsU2+hYxHiED1F8LAr1ikfXg8IBTU+YDoimCg63s6ZYu199QXpQo9lVDTKLArUQf896d3gDPZyS9dednCkKvBNUWf75HiEX6MTA91drCOjeJ8GB2x0Rjgokw/FdEDpIosm1wMx6xufoU3P1hJpdixLA8Pyg1VndE2eDODTThfV1+7l/YVqWurk89DTm5z6lJjyXgyJHG1wuGYN+1DYxebvJfXAa+QM0nz+gF1/XsKCnGKN3R5aeiZpzo9w9BJY4s9OR6e1v698XguW3+0xEQ</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <fed:ClaimTypesOffered>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Name</auth:DisplayName>
                <auth:Description>The mutable display name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                    Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"
                    xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Subject</auth:DisplayName>
                <auth:Description>An immutable, globally unique, non-reusable identifier of the user
                    that is unique to the application for which a token is issued.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Given Name</auth:DisplayName>
                <auth:Description>First name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Surname</auth:DisplayName>
                <auth:Description>Last name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/displayname"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Display Name</auth:DisplayName>
                <auth:Description>Display name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/nickname"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Nick Name</auth:DisplayName>
                <auth:Description>Nick name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                    Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant"
                    xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Authentication Instant</auth:DisplayName>
                <auth:Description>The time (UTC) when the user is authenticated to Windows Azure
                    Active Directory.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                    Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod"
                    xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Authentication Method</auth:DisplayName>
                <auth:Description>The method that Windows Azure Active Directory uses to
                    authenticate users.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/objectidentifier"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>ObjectIdentifier</auth:DisplayName>
                <auth:Description>Primary identifier for the user in the directory. Immutable,
                    globally unique, non-reusable.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/tenantid"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>TenantId</auth:DisplayName>
                <auth:Description>Identifier for the user's tenant.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/identityprovider"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>IdentityProvider</auth:DisplayName>
                <auth:Description>Identity provider for the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Email</auth:DisplayName>
                <auth:Description>Email address of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Groups</auth:DisplayName>
                <auth:Description>Groups of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/accesstoken"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External Access Token</auth:DisplayName>
                <auth:Description>Access token issued by external identity provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External Access Token Expiration</auth:DisplayName>
                <auth:Description>UTC expiration time of access token issued by external identity
                    provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/openid2_id"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External OpenID 2.0 Identifier</auth:DisplayName>
                <auth:Description>OpenID 2.0 identifier issued by external identity provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/claims/groups.link"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>GroupsOverageClaim</auth:DisplayName>
                <auth:Description>Issued when number of user's group claims exceeds return limit.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Role Claim</auth:DisplayName>
                <auth:Description>Roles that the user or Service Principal is attached to</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/wids"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>RoleTemplate Id Claim</auth:DisplayName>
                <auth:Description>Role template id of the Built-in Directory Roles that the user is
                    a member of</auth:Description>
            </auth:ClaimType>
        </fed:ClaimTypesOffered>
        <fed:SecurityTokenServiceEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:SecurityTokenServiceEndpoint>
        <fed:PassiveRequestorEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:PassiveRequestorEndpoint>
    </RoleDescriptor>
    <RoleDescriptor xsi:type="fed:ApplicationServiceType"
                    protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQQM4ucCiSA6JEttpV6+4uJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTEwMDIxNTU0NTBaFw0yODEwMDIxNTU0NTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt5mUyrEgySAXARxb/doUh7fm9F48VLOpyLPySFoO4BGTHJjm1IFQoqHSeMimANvCORQGEevC3ni5YCuHHNxa4ySArqo7tp2Ue3spAc4oYjciUbuqCw5t7YxgKsdTcIlRV4/jWgDdeu/mlKNzXgNsU+FY9OlKQVYGle3u708WAE1iQ3f7Vd4ZmF+8lnmd3mm91zTAgg8uDncCZdm5QeslT1lHxDQhPSaF9RWdvQJNtIUk9YMDcNI8k1/v/3VEmTdcek2bmrNgp5ILV1tfIjpeXAP2Ngyh1empoD0ogu1sMjJX9YcE5R/oMn4+E3D7bpTQ9ZJz888fUrme9+k2MXfJcQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBTyrs2lSkthIxcqpwP0mrcC0nZvdQQe2Cis/+nqoGdp+sU0opBljWApd0zsBCxB9fZqsU2+hYxHiED1F8LAr1ikfXg8IBTU+YDoimCg63s6ZYu199QXpQo9lVDTKLArUQf896d3gDPZyS9dednCkKvBNUWf75HiEX6MTA91drCOjeJ8GB2x0Rjgokw/FdEDpIosm1wMx6xufoU3P1hJpdixLA8Pyg1VndE2eDODTThfV1+7l/YVqWurk89DTm5z6lJjyXgyJHG1wuGYN+1DYxebvJfXAa+QM0nz+gF1/XsKCnGKN3R5aeiZpzo9w9BJY4s9OR6e1v698XguW3+0xEQ</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <fed:TargetScopes>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>https://sts.windows.net/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/</wsa:Address>
            </wsa:EndpointReference>
        </fed:TargetScopes>
        <fed:ApplicationServiceEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:ApplicationServiceEndpoint>
        <fed:PassiveRequestorEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:PassiveRequestorEndpoint>
    </RoleDescriptor>
    <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQQM4ucCiSA6JEttpV6+4uJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTEwMDIxNTU0NTBaFw0yODEwMDIxNTU0NTBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt5mUyrEgySAXARxb/doUh7fm9F48VLOpyLPySFoO4BGTHJjm1IFQoqHSeMimANvCORQGEevC3ni5YCuHHNxa4ySArqo7tp2Ue3spAc4oYjciUbuqCw5t7YxgKsdTcIlRV4/jWgDdeu/mlKNzXgNsU+FY9OlKQVYGle3u708WAE1iQ3f7Vd4ZmF+8lnmd3mm91zTAgg8uDncCZdm5QeslT1lHxDQhPSaF9RWdvQJNtIUk9YMDcNI8k1/v/3VEmTdcek2bmrNgp5ILV1tfIjpeXAP2Ngyh1empoD0ogu1sMjJX9YcE5R/oMn4+E3D7bpTQ9ZJz888fUrme9+k2MXfJcQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBTyrs2lSkthIxcqpwP0mrcC0nZvdQQe2Cis/+nqoGdp+sU0opBljWApd0zsBCxB9fZqsU2+hYxHiED1F8LAr1ikfXg8IBTU+YDoimCg63s6ZYu199QXpQo9lVDTKLArUQf896d3gDPZyS9dednCkKvBNUWf75HiEX6MTA91drCOjeJ8GB2x0Rjgokw/FdEDpIosm1wMx6xufoU3P1hJpdixLA8Pyg1VndE2eDODTThfV1+7l/YVqWurk89DTm5z6lJjyXgyJHG1wuGYN+1DYxebvJfXAa+QM0nz+gF1/XsKCnGKN3R5aeiZpzo9w9BJY4s9OR6e1v698XguW3+0xEQ</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                             Location="https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/saml2" />
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                             Location="https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/saml2" />
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                             Location="https://login.microsoftonline.com/e201abf9-c5a3-43f8-8e29-135d4fe67e6b/saml2" />
    </IDPSSODescriptor>
</EntityDescriptor>