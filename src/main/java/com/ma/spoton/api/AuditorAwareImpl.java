package com.ma.spoton.api;

import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.security.AuthDetailsProvider;

public class AuditorAwareImpl implements AuditorAware<User> {

  @Autowired
  private UserRepository userRepository;

  @Override
  public Optional<User> getCurrentAuditor() {
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    return Objects.nonNull(user) ? userRepository.findById(user.getId()) : Optional.empty();
  }

}
