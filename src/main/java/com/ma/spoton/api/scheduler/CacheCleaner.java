package com.ma.spoton.api.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.services.DashboardServiceImpl;
import com.ma.spoton.api.utils.LoggerUtil;

/**
 * This class is temporary till we switch to some different cache like Guava cache, which will have eviction policies
 * handled by themselves.
 */
@Component
public class CacheCleaner {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheCleaner.class);

    @Scheduled(cron = "0 0 4 * * *") // every Day at 4 AM
    public void clearEveryDay() {
        long start = System.currentTimeMillis();
        DashboardServiceImpl.clearSpotCountsCache();
        LoggerUtil.logSLA(LOGGER, "daily4AMScheduler", start, "daily4AMScheduler completed");
    }

    @Scheduled(cron = "0 0 2 * * MON") // Runs every Monday at 2:00 AM
    public void clearEveryWeek() {
        long start = System.currentTimeMillis();
        DashboardServiceImpl.clearWeeklyTotalSpotsCache();
        LoggerUtil.logSLA(LOGGER, "weekly2AMMondayScheduler", start, "weekly2AMMondayScheduler completed");
    }
}
