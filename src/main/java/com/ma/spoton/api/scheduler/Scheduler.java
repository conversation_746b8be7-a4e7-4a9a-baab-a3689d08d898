package com.ma.spoton.api.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.services.JobService;
import com.ma.spoton.api.services.OverTimeService;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class Scheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(Scheduler.class);
    @Autowired
    private UserService userService;
    @Autowired
    private OverTimeService overTimeService;
    @Autowired
    private JobService jobService;

    @Scheduled(cron = "0 0 6 * * ?")
    public void fixedRateSch() {
        long start = System.currentTimeMillis();
        userService.sendInactiveAccountDeletionAlert();
        LoggerUtil.logSLA(LOGGER, "daily6amScheduler", start, "daily6amScheduler completed");
    }

    //	 @Scheduled(cron = "0 * * * * ?")
    //	 public void runEveryMinute() {
    //
    //	     trailerAuditService.clearAllTrailerAudit();
    //	 }

    @Scheduled(cron = "0 * * * * ?")
    public void runEveryMinute() {
        //  log.info("everyMinute");
        long start = System.currentTimeMillis();
        overTimeService.deleteExpiredOverTimeUsers();
        //	     jobService.assignJobFromQueue();
        jobService.queueScheduledJobs();
        jobService.reassignBucketJobs();
        userService.resetIdleTime();
        LoggerUtil.logSLA(LOGGER, "everyMinuteScheduler", start, "everyMinuteScheduler completed");
    }

    @Scheduled(cron = "0 */5 * * * ?")
    public void updateJobPriority() {

        long start = System.currentTimeMillis();
        jobService.updateJobPriority();
        LoggerUtil.logSLA(LOGGER, "updateJobPriorityScheduler", start, "updateJobPriorityScheduler completed");
    }

}
