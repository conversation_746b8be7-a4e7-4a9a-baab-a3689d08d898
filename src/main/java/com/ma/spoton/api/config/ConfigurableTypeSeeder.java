package com.ma.spoton.api.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.cache.configurabletype.ConfigurableCacheLoaderFactory;
import com.ma.spoton.api.cache.configurabletype.ConfigurableTypeCacheLoader;
import com.ma.spoton.api.constants.ConfigurableType;
import com.ma.spoton.api.services.ConfigurableTypeService;
import com.ma.spoton.api.services.ConfigurableTypesFactory;
import com.ma.spoton.api.utils.LoggerUtil;

@Component
@Order(1) // ✅ controls execution order if you have multiple seeders
public class ConfigurableTypeSeeder implements ApplicationRunner {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigurableTypeSeeder.class);

    private final ConfigurableTypesFactory factory;
    private final ConfigurableCacheLoaderFactory cacheLoaderFactory;

    @Value("${application.data.seed:false}")
    private boolean dataSeed;

    @Autowired
    public ConfigurableTypeSeeder(ConfigurableTypesFactory factory,
                                  ConfigurableCacheLoaderFactory cacheLoaderFactory) {
        this.factory = factory;
        this.cacheLoaderFactory = cacheLoaderFactory;
    }

    @Override
    public void run(ApplicationArguments args) {

        if (dataSeed) {
            long start = System.currentTimeMillis();
            for (ConfigurableType type : ConfigurableType.values()) {
                ConfigurableTypeService<?> service = factory.getService(type);
                // create default values if not present
                service.createIfNotExists(type.getInitialValues(), true);

                ConfigurableTypeCacheLoader<?> cacheLoader = cacheLoaderFactory.getCacheLoader(type.name());
                if (cacheLoader != null) {
                    cacheLoader.loadGlobalData();
                }
            }
            LoggerUtil.logSLA(LOGGER, "configurableTypeSeeder", start, "configurableTypeSeeder completed");
        }
    }
}
