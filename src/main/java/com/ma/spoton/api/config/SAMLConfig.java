// package com.ma.spoton.api.config;

// import com.fasterxml.jackson.core.JsonProcessingException;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import com.ma.spoton.api.constants.ApplicationConstants;
// import com.ma.spoton.api.repositories.UserRepository;
// import com.ma.spoton.api.services.UserService;
// import com.ma.spoton.api.services.UserServiceImpl;

// import lombok.extern.slf4j.Slf4j;

// import org.opensaml.xmlsec.signature.support.SignatureConstants;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.core.convert.converter.Converter;
// import org.springframework.core.io.Resource;
// import org.springframework.http.HttpEntity;
// import org.springframework.http.HttpHeaders;
// import org.springframework.http.MediaType;
// import org.springframework.security.converter.RsaKeyConverters;
// import org.springframework.security.core.Authentication;
// import org.springframework.security.core.GrantedAuthority;
// import org.springframework.security.core.authority.SimpleGrantedAuthority;
// import org.springframework.security.crypto.codec.Base64;
// import org.springframework.security.saml2.core.Saml2X509Credential;
// import org.springframework.security.saml2.provider.service.authentication.OpenSamlAuthenticationProvider;
// import org.springframework.security.saml2.provider.service.authentication.Saml2AuthenticatedPrincipal;
// import org.springframework.security.saml2.provider.service.authentication.Saml2Authentication;
// import org.springframework.security.saml2.provider.service.metadata.OpenSamlMetadataResolver;
// import org.springframework.security.saml2.provider.service.registration.*;
// import org.springframework.security.saml2.provider.service.web.DefaultRelyingPartyRegistrationResolver;
// import org.springframework.security.saml2.provider.service.web.Saml2MetadataFilter;
// import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
// import org.springframework.util.LinkedMultiValueMap;
// import org.springframework.util.MultiValueMap;
// import org.springframework.web.client.RestTemplate;

// import javax.servlet.http.HttpServletRequest;
// import javax.servlet.http.HttpServletResponse;
// import java.io.File;
// import java.io.FileInputStream;
// import java.io.IOException;
// import java.io.InputStream;
// import java.security.cert.CertificateFactory;
// import java.security.cert.X509Certificate;
// import java.security.interfaces.RSAPrivateKey;
// import java.util.*;

// import static com.ma.spoton.api.constants.SystemRoles.CLIENT;

// @Configuration
// @Slf4j
// public class SAMLConfig {

// 	private final String STRONG_DEFAULT_PASSWORD = "THIS IS A STRONG PASSWORD";

// 	private static final String X_509_TYPE_CERTIFICATE = "X.509";

// 	@Value("${application.certificates.idp-certificate}")
// 	private Resource identityProviderCertificate;

// 	@Value("${application.certificates.sp-key}")
// 	private Resource serviceProviderPrivateKey;

// 	@Value("${application.certificates.sp-certificate}")
// 	private Resource serviceProviderPublicCertificate;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.identityprovider.entity-id}")
// 	private String assertingPartyEntityId;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.identityprovider.metadata-uri}")
// 	private String assertingPartyMetadataUrl;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.identityprovider.singlesignon.url}")
// 	private String singleSignOnUrl;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.serviceprovider.entity-id}")
// 	private String relyingPartyEntityId;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.serviceprovider.assertion-consumer-service-location}")
// 	private String replyUrl;

// 	@Value("${spring.security.saml2.relyingparty.registration.adfs.serviceprovider.registration-id}")
// 	private String id;


// 	@Autowired
// 	private UserService userService;

// 	@Autowired
// 	private ApplicationConstants applicationConstants;

// 	@Autowired
// 	private UserServiceImpl userServiceImpl;

// 	@Autowired
// 	private UserRepository userRepository;


// 	private Converter<OpenSamlAuthenticationProvider.ResponseToken, Saml2Authentication> groupsConverter() {
// 		Converter<OpenSamlAuthenticationProvider.ResponseToken, Saml2Authentication> delegate = OpenSamlAuthenticationProvider
// 			.createDefaultResponseAuthenticationConverter();

// 		return (responseToken) -> {
// 			Saml2Authentication authentication = delegate.convert(responseToken);
// 			Saml2AuthenticatedPrincipal principal = (Saml2AuthenticatedPrincipal) authentication.getPrincipal();
// 			List<String> groups = List.of(CLIENT);
// 			Set<GrantedAuthority> authorities = new HashSet<>();
// 			groups.stream().map(SimpleGrantedAuthority::new).forEach(authorities::add);
// 			return new Saml2Authentication(principal, authentication.getSaml2Response(), authorities);
// 		};
// 	}

// 	@Bean
// 	public OpenSamlAuthenticationProvider authenticationProvider() {
// 		OpenSamlAuthenticationProvider authenticationProvider = new OpenSamlAuthenticationProvider();
// 		authenticationProvider.setAssertionValidator(OpenSamlAuthenticationProvider.createDefaultAssertionValidator());
// 		authenticationProvider.setResponseAuthenticationConverter(groupsConverter());
// 		return authenticationProvider;
// 	}

// 	public Saml2MetadataFilter generateServiceProviderMetadata() {
// 		DefaultRelyingPartyRegistrationResolver relyingPartyRegistrationResolver = new DefaultRelyingPartyRegistrationResolver(
// 			relyingPartyRegistrations());
// 		return new Saml2MetadataFilter(relyingPartyRegistrationResolver, new OpenSamlMetadataResolver());
// 	}

// 	@Bean
// 	public RelyingPartyRegistrationRepository relyingPartyRegistrations() {
// 		Saml2X509Credential assertingPartyVerifyingCredential = asVerificationCredential(identityProviderCertificate);
// 		Saml2X509Credential relyingPartySigningCredential = asSigningCredential(serviceProviderPrivateKey, serviceProviderPublicCertificate);
// 		Saml2X509Credential relyingPartyDecryptingCredential = asDecryptionCredential(serviceProviderPrivateKey, serviceProviderPublicCertificate);

// 		RelyingPartyRegistration registration = RelyingPartyRegistrations.fromMetadataLocation(assertingPartyMetadataUrl)
// 			.registrationId(id)
// 			.entityId(relyingPartyEntityId)
// 			.assertionConsumerServiceLocation(applicationConstants.getApiBaseUrl().concat(replyUrl).concat(id))
// 			.assertingPartyDetails(party -> party
// 				.entityId(assertingPartyEntityId)
// 				.signingAlgorithms(sign -> sign.add(SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA256))
// 				.singleSignOnServiceBinding(Saml2MessageBinding.POST)
// 				.singleSignOnServiceLocation(singleSignOnUrl)
// 				.wantAuthnRequestsSigned(false)
// 				.verificationX509Credentials(credentials -> credentials.add(assertingPartyVerifyingCredential)))
// 			.signingX509Credentials(credentials -> credentials.add(relyingPartySigningCredential))
// 			.decryptionX509Credentials(credentials -> credentials.add(relyingPartyDecryptingCredential))
// 			.build();

// 		return new InMemoryRelyingPartyRegistrationRepository(registration);
// 	}

// 	public AuthenticationSuccessHandler customAuthenticationSuccessHandler(String redirectUrl) {
// 		return new AuthenticationSuccessHandler() {
// 			@Override
// 			public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {
// 				// Extract authenticated user details
// 				String userEmail = userServiceImpl.getClaimValue(authentication, "emailaddress");
// 				String password = userEmail.concat("_").concat(STRONG_DEFAULT_PASSWORD);

// 				log.info("SAML authentication success for user: {}", userEmail);
				
// 				isUserPresentInDatabase(authentication, userEmail, password);

// 				// Extract the access token
// 				var accessToken = generateToken(applicationConstants.getClientId(), applicationConstants.getClientSecretValue(),
// 					applicationConstants.getApiBaseUrl().concat("/oauth/token"), userEmail, password);

// 				response.sendRedirect(redirectUrl + "?token=" + accessToken);
// 			}

// 		};
// 	}

// 	private Saml2X509Credential asVerificationCredential(Resource certificate) {
// 		X509Certificate publicCertificate = readCertificate(certificate);
// 		return Saml2X509Credential.verification(publicCertificate);
// 	}

// 	private Saml2X509Credential asSigningCredential(Resource privateKey, Resource certificate) {
// 		RSAPrivateKey rsaPrivateKey = readPrivateKey(privateKey);
// 		X509Certificate publicCertificate = readCertificate(certificate);
// 		return Saml2X509Credential.signing(rsaPrivateKey, publicCertificate);
// 	}

// 	private Saml2X509Credential asDecryptionCredential(Resource privateKey, Resource certificate) {
// 		RSAPrivateKey rsaPrivateKey = readPrivateKey(privateKey);
// 		X509Certificate publicCertificate = readCertificate(certificate);
// 		return Saml2X509Credential.decryption(rsaPrivateKey, publicCertificate);
// 	}

// 	private RSAPrivateKey readPrivateKey(Resource location) {
// 		//try (InputStream inputStream = new FileInputStream(location.getFile())) {
// 		try (InputStream inputStream = location.getInputStream()) {
// 			return RsaKeyConverters.pkcs8().convert(inputStream);
// 		} catch (Exception ex) {
// 			throw new IllegalArgumentException(ex);
// 		}
// 	}

// 	private X509Certificate readCertificate(Resource location) {
// 		//try (InputStream source = new FileInputStream(location.getFile())) {
// 		try (InputStream source = location.getInputStream()) {
// 			return (X509Certificate) CertificateFactory.getInstance(X_509_TYPE_CERTIFICATE).generateCertificate(source);
// 		} catch (Exception ex) {
// 			throw new IllegalArgumentException(ex);
// 		}
// 	}

// 	private void isUserPresentInDatabase(Authentication authentication, String userEmailId, String password) {
// 		boolean emailIdExists = userRepository.existsByEmail(userEmailId);
// 		if (!emailIdExists) {
// 			userService.addUserFromAzureAD(authentication, userEmailId, password, CLIENT);
// 		}
// 	}

// 	public String generateToken(String clientId, String clientSecret, String tokenUri, String userName, String password) throws JsonProcessingException {
// 		HttpHeaders headers = new HttpHeaders();
// 		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
// 		String credentials = clientId + ":" + clientSecret;

// 		String base64Credentials = new String(Base64.encode(credentials.getBytes()));
// 		headers.setBasicAuth(base64Credentials);

// 		// Construct request parameters
// 		MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
// 		requestBody.add("grant_type", Arrays.stream(applicationConstants.getGrantTypes()).filter(grantType -> grantType.equals("password")).findAny().get());
// 		requestBody.add("password", password);
// 		requestBody.add("username", userName);

// 		HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
// 		var restTemplate = new RestTemplate();

// 		// Make a POST request to the token endpoint
// 		var responseEntity = restTemplate.postForEntity(tokenUri, requestEntity, Map.class);

// 		// Parse the token response
// 		if (responseEntity.getStatusCode().is2xxSuccessful()) {
// 			Map<String, Object> responseBody = responseEntity.getBody();
// 			if (responseBody != null && responseBody.containsKey("access_token")) {
// 				ObjectMapper mapper = new ObjectMapper();
// 				return new String(Base64.encode(mapper.writeValueAsString(responseBody).getBytes()));
// 			}
// 		}

// 		return null;
// 	}

// }
