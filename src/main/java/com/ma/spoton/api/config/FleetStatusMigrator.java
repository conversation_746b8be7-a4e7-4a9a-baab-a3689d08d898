package com.ma.spoton.api.config;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.cache.configurabletype.FleetStatusCacheLoader;
import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.utils.LoggerUtil;

@Component
@Order(2) // Run after ConfigurableTypeSeeder.
public class FleetStatusMigrator implements ApplicationRunner {

    private static final int BATCH_SIZE = 1000;
    private static final Logger LOGGER = LoggerFactory.getLogger(FleetStatusMigrator.class);

    private final JobRepository jobRepository;
    private final FleetRepository fleetRepository;
    private final FleetStatusCacheLoader fleetStatusCacheLoader;

    @Value("${application.data.migrate:false}")
    private boolean dataMigrate;

    public FleetStatusMigrator(FleetRepository fleetRepository,
                               JobRepository jobRepository,
                               FleetStatusCacheLoader fleetStatusCacheLoader) {
        this.fleetRepository = fleetRepository;
        this.jobRepository = jobRepository;
        this.fleetStatusCacheLoader = fleetStatusCacheLoader;
    }

    @Override
    @Transactional
    public void run(ApplicationArguments args) {
        migrate(null);
    }

    public void migrate(String tableName) {
        if (dataMigrate) {
            Map<String, FleetStatus> statusCache = fleetStatusCacheLoader.getGlobalCache().asMap();

            if (StringUtils.isNotBlank(tableName)) {
                if (tableName.equalsIgnoreCase("fleets")) {
                    migrateFleetTableData(statusCache);
                } else if (tableName.equalsIgnoreCase("jobs")) {
                    migrateJobTableData(statusCache);
                } else {
                    throw new RuntimeException("No table found with name " + tableName);
                }
            } else { // al table migrate
                // Fleet table data
                migrateFleetTableData(statusCache);

                // Job table data
                migrateJobTableData(statusCache);
            }
        }
    }

    private void migrateFleetTableData(Map<String, FleetStatus> statusCache) {

        long start = System.currentTimeMillis();
        for (Map.Entry<String, FleetStatus> entry : statusCache.entrySet()) {
            int count = fleetRepository.updateFleetStatus(entry.getValue().getId(), entry.getKey());
            LOGGER.info("Updated {} fleets from {} to {}", count, entry.getKey(), entry.getValue().getId());
        }
        LoggerUtil.logSLA(LOGGER, "migrateFleetTableData", start, "migrateFleetTableData completed");
    }

    private void migrateJobTableData(Map<String, FleetStatus> statusCache) {

        long start = System.currentTimeMillis();
        for (Map.Entry<String, FleetStatus> entry : statusCache.entrySet()) {
            int count = jobRepository.updateFleetStatus(entry.getValue().getId(), entry.getKey());
            LOGGER.info("Updated {} jobs from {} to {}", count, entry.getKey(), entry.getValue().getId());
        }
        LoggerUtil.logSLA(LOGGER, "migrateJobTableData", start, "migrateJobTableData completed");
    }
}
