//package com.ma.spoton.api.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.Order;
//import org.springframework.security.authentication.ProviderManager;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
//
//@Configuration
//@Order(101)  // Keep this order higher than default security
//public class SamlSecurityConfig extends WebSecurityConfigurerAdapter {
//
//    private final SAMLConfig samlConfig;
//
//    public SamlSecurityConfig(SAMLConfig samlConfig) {
//        this.samlConfig = samlConfig;
//    }
//
//    @Override
//    protected void configure(HttpSecurity http) throws Exception {
//        http
//            .antMatcher("/saml2/**") // intercept all SAML URLs
//            .authorizeRequests()
//                .anyRequest().authenticated()
//            .and()
//            .saml2Login(saml2 -> saml2
//                .authenticationManager(new ProviderManager(samlConfig.authenticationProvider()))
//                .successHandler(
//                    samlConfig.customAuthenticationSuccessHandler("http://localhost:4200/#/saml2-success")
//                )
//            )
//            .csrf().disable();
//    }
//}
