package com.ma.spoton.api.config;

import org.springframework.stereotype.Component;

import com.ma.spoton.api.dtos.ConfigurableEntityDTO;
import com.ma.spoton.api.entities.ConfigurableTypeEntity;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.mappers.ConfigurableTypeMapper;
import com.ma.spoton.api.mappers.RoleMapper;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ConfigurableTypeMapperRegistry {

    private final ConfigurableTypeMapper baseMapper;
    private final RoleMapper roleMapper;

    // TODO : Add custom mapper condition for any new configurable Type, if needed.
    public ConfigurableEntityDTO mapToDto(Class type, ConfigurableTypeEntity entity) {
        if (type.equals(Role.class)) {
            return roleMapper.mapToDto((Role) entity);
        }
        return baseMapper.mapToDto(entity);
    }
}
