package com.ma.spoton.api.config;

import javax.sql.DataSource;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FlywayConfig {

  @Autowired
  public FlywayConfig(DataSource dataSource) {
    Flyway.configure().baselineOnMigrate(true).dataSource(dataSource).outOfOrder(true).load()
        .migrate();
  }
}
