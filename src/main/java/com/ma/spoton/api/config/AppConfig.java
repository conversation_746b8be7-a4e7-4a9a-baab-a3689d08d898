package com.ma.spoton.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

@Configuration
public class AppConfig {
	@Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        // Set maximum upload size in bytes
        resolver.setMaxUploadSize(10485760); // 10 MB
        return resolver;
    }
	
	 @Bean
	 public RestTemplate restTemplate() {
	        return new RestTemplate();
    }
}
