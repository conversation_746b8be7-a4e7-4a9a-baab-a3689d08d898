package com.ma.spoton.api.config;

import static com.ma.spoton.api.constants.ConfigurableType.FLEET_STATUS;
import static com.ma.spoton.api.constants.ConfigurableType.ROLES;

import java.util.function.Consumer;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.FleetStatusRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.RoleRepository;
import com.ma.spoton.api.services.ConfigurableTypeService;
import com.ma.spoton.api.services.ConfigurableTypeServiceImpl;

@Configuration
public class ConfigurableTypeServiceConfig {

    @Bean
    public ConfigurableTypeService<FleetStatus> fleetStatusConfigurableTypeService(
            FleetStatusRepository repo, FleetRepository fleetRepository, JobRepository jobRepository) {
        Consumer<FleetStatus> deleteFunction = (entity) -> {
            fleetRepository.updateFleetStatusToNull(entity.getId()); // fleet's fleetStatus to null
            jobRepository.updateFleetStatusToNull(entity.getId()); // job's fleetStatus to null
            repo.delete(entity);
        };
        return new ConfigurableTypeServiceImpl<>(repo, FleetStatus.class, FLEET_STATUS, deleteFunction) {
        };
    }

    @Bean
    public ConfigurableTypeService<Role> roleConfigurableTypeService(
            RoleRepository repo) {
        // TODO : Add all mappings removal code here.
        Consumer<Role> deleteFunction = repo::delete;
        return new ConfigurableTypeServiceImpl<>(repo, Role.class, ROLES, deleteFunction);
    }

    // TODO : add a new bean when a new type needs to be configured.
}
