package com.ma.spoton.api.config;

import com.ma.spoton.api.constants.ApplicationConstants;
import com.ma.spoton.api.security.CustomTokenEnhancer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import javax.annotation.Resource;

import static java.util.Arrays.asList;

@SuppressWarnings("deprecation")
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

//	@Autowired
//	private TokenStore tokenStore;
//
//	@Autowired
//	private JwtAccessTokenConverter accessTokenConverter;
//
//	@Autowired
//	private AuthenticationManager authenticationManager;
//
//	@Resource
//	private ApplicationConstants appConstants;
	
	private final TokenStore tokenStore;
    private final JwtAccessTokenConverter accessTokenConverter;
    private final AuthenticationManager authenticationManager;
    private final ApplicationConstants appConstants;
    
    @Autowired
    public AuthorizationServerConfig(
            @Lazy TokenStore tokenStore,
            @Lazy JwtAccessTokenConverter accessTokenConverter,
            @Lazy AuthenticationManager authenticationManager,
            ApplicationConstants appConstants) {
        this.tokenStore = tokenStore;
        this.accessTokenConverter = accessTokenConverter;
        this.authenticationManager = authenticationManager;
        this.appConstants = appConstants;
    }

	@Bean
	public TokenEnhancer tokenEnhancer() {
		return new CustomTokenEnhancer();
	}

	@Override
	public void configure(ClientDetailsServiceConfigurer configurer) throws Exception {
		configurer.inMemory().withClient(appConstants.getClientId())
			.secret(appConstants.getClientSecret()).authorizedGrantTypes(appConstants.getGrantTypes())
			.scopes(appConstants.getScopes())
			.accessTokenValiditySeconds(appConstants.getAccessTokenValidityInSeconds())
			.refreshTokenValiditySeconds(appConstants.getRefreshTokenValidityInSeconds());
	}

	@Override
	public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
		TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
		tokenEnhancerChain.setTokenEnhancers(asList(tokenEnhancer(), accessTokenConverter));
		endpoints.tokenStore(tokenStore).accessTokenConverter(accessTokenConverter)
			.tokenEnhancer(tokenEnhancerChain).authenticationManager(authenticationManager);
	}

	@Override
	public void configure(AuthorizationServerSecurityConfigurer oauthServer) throws Exception {
		oauthServer.tokenKeyAccess("permitAll()").checkTokenAccess("isAuthenticated()");
		oauthServer.passwordEncoder(new BCryptPasswordEncoder());
	}

	@Bean
	public ClientRegistrationRepository clientRegistrationRepository() {
		return new CustomClientRegistrationRepository();
	}

	private class CustomClientRegistrationRepository implements ClientRegistrationRepository {

		@Override
		public ClientRegistration findByRegistrationId(String registrationId) {
			if ("my-server".equals(registrationId)) {
				return myServerClientRegistration();
			}
			return null;
		}

		private ClientRegistration myServerClientRegistration() {
			return ClientRegistration
				.withRegistrationId("my-server")
				.build();
		}
	}

}