package com.ma.spoton.api.config;

import com.ma.spoton.api.constants.ApplicationConstants;
import com.ma.spoton.api.repositories.JwtBlacklistedTokenRepository;
import com.ma.spoton.api.security.JwtBlacklistFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.security.saml2.provider.service.servlet.filter.Saml2WebSsoAuthenticationFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.Resource;

import static org.springframework.security.config.http.SessionCreationPolicy.STATELESS;

@SuppressWarnings("deprecation")
@Configuration
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {

	private static final String RESOURCE_ID = "spoton-api-resource-id";
	
	private final ApplicationConstants appConstants;
    private final ResourceServerTokenServices tokenServices;
    private final JwtBlacklistedTokenRepository jwtBlacklistedTokenRepository;
    
    @Autowired
    public ResourceServerConfig(
            ApplicationConstants appConstants,
            @Lazy ResourceServerTokenServices tokenServices,
            JwtBlacklistedTokenRepository jwtBlacklistedTokenRepository,
            @Value("${application.admin-portal-base-url}") String adminPortalBaseUrl) {
        this.appConstants = appConstants;
        this.tokenServices = tokenServices;
        this.jwtBlacklistedTokenRepository = jwtBlacklistedTokenRepository;
    }

//	@Resource
//	private ApplicationConstants appConstants;
//
//	@Autowired
//	private ResourceServerTokenServices tokenServices;
//
//	@Autowired
//	private JwtBlacklistedTokenRepository jwtBlacklistedTokenRepository;

	@Autowired
	private SAMLConfig samlConfig;

	@Value("${application.admin-portal-base-url}")
	private String adminPortalBaseUrl;

	@Override
	public void configure(HttpSecurity httpSecurity) throws Exception {
		httpSecurity
			.addFilterBefore(new JwtBlacklistFilter(jwtBlacklistedTokenRepository), UsernamePasswordAuthenticationFilter.class)
			.addFilterBefore(samlConfig.generateServiceProviderMetadata(), Saml2WebSsoAuthenticationFilter.class)
			.authorizeRequests()
			.antMatchers(appConstants.getPublicEndpoints()).permitAll()
			.and()
			.csrf().disable()
			.sessionManagement().sessionCreationPolicy(STATELESS)
			.and()
			.oauth2Login()
			.and()
			.saml2Login(saml2 -> saml2
				.authenticationManager(new ProviderManager(samlConfig.authenticationProvider()))
				.successHandler(samlConfig.customAuthenticationSuccessHandler(adminPortalBaseUrl + "/#/saml2-success"))
			);

	}

	@Override
	public void configure(ResourceServerSecurityConfigurer config) {
		config.resourceId(RESOURCE_ID).tokenServices(tokenServices);
	}

}