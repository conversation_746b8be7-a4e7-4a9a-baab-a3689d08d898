package com.ma.spoton.api.jobs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.ma.spoton.api.services.NotificationService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@EnableScheduling
public class SendNotificationJob {

  @Autowired
  private NotificationService notificationService;

  @Scheduled(initialDelay = 1000,
      fixedDelayString = "${application.job.auto-send-notification.time}")
  public void autoSendNotification() {
    log.debug(">> Running send notifications");
    notificationService.autoSendNotification();
  }
}
