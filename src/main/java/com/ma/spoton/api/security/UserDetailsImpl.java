package com.ma.spoton.api.security;

import static java.util.stream.Collectors.toList;
import java.util.List;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import com.ma.spoton.api.dtos.UserAuthDto;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserDetailsImpl implements UserDetails {

  /**
   * 
   */
  private static final long serialVersionUID = -8947728233253532631L;

  private final transient UserAuthDto userAuthDto;
  private final List<GrantedAuthority> authorityList;

  public UserDetailsImpl(UserAuthDto userAuthDto) {
    // log.info(">> UserDetailsImpl({})", userAuthDto);
    this.userAuthDto = userAuthDto;
    this.authorityList =
        userAuthDto.getAuthorities().stream().map(SimpleGrantedAuthority::new).collect(toList());
    // log.info("authorityList : {}", authorityList);
  }

  @Override
  public List<GrantedAuthority> getAuthorities() {
    return authorityList;
  }

  @Override
  public String getPassword() {
    return userAuthDto.getPassword();
  }

  @Override
  public String getUsername() {
    return userAuthDto.getEmail();
  }

  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  @Override
  public boolean isAccountNonLocked() {
    return true;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  @Override
  public boolean isEnabled() {
    return true;
  }

  public UserAuthDto getUserAuthDto() {
    return userAuthDto;
  }

}
