package com.ma.spoton.api.security;

import java.util.HashMap;
import java.util.Map;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.utils.SecurityUtils;

@SuppressWarnings("deprecation")
public class CustomTokenEnhancer implements TokenEnhancer {

  @Override
  public OAuth2AccessToken enhance(OAuth2AccessToken accessToken,
      OAuth2Authentication authentication) {
    final Map<String, Object> additionalInfo = new HashMap<>();
    UserAuthDto userDto = ((UserDetailsImpl) authentication.getPrincipal()).getUserAuthDto();
    additionalInfo.put("id", SecurityUtils.encode(userDto.getId().toString(), 6));
    additionalInfo.put("userId", userDto.getUserId());
    additionalInfo.put("firstName", userDto.getFirstName());
    additionalInfo.put("lastName", userDto.getLastName());
    additionalInfo.put("email", userDto.getEmail());
    additionalInfo.put("phone", userDto.getPhone());
    additionalInfo.put("timeZone", userDto.getTimeZone());
    additionalInfo.put("roles", userDto.getAuthorities());
    additionalInfo.put("clientIds", userDto.getClientIds());
    ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
    return accessToken;
  }
}
