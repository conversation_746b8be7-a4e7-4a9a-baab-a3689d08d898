package com.ma.spoton.api.security;

import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.UserMapper;
import com.ma.spoton.api.repositories.UserRepository;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

  @Autowired
  private UserRepository userRepository;

  @Autowired
  private UserMapper userMapper;

  @Override
  @Transactional(readOnly = true)
  public UserDetails loadUserByUsername(String username) {
    // log.info(">> loadUserByUsername({})", username);
    try {
      // Finding user by email
      User user = userRepository.findActiveUserByEmail(username)
          .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND_BY_EMAIL, username));
      UserAuthDto userAuthDto = userMapper.mapToAuthDto(user);

      // Resolving User Authorities
      userAuthDto.setAuthorities(user.getRoles().stream().map(role -> "ROLE_" + role.getRoleName())
          .collect(Collectors.toList()));

      // Fetching User clients
      userAuthDto.setClientIds(CollectionUtils.isNotEmpty(user.getClients())
          ? user.getClients().stream().map(Client::getUuid).collect(Collectors.toList())
          : List.of());

      // log.info("Exit loadUserByUsername() -> {}", userAuthDto);
      return new UserDetailsImpl(userAuthDto);
    } catch (Exception e) {
      log.error("Exception {} happened in loadUserByUsername", e);
      throw new UsernameNotFoundException("User not found : " + e.getMessage());
    }
  }

}
