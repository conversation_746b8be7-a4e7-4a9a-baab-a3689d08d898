package com.ma.spoton.api.security;


import java.time.ZonedDateTime;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.stereotype.Component;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.UserRepository;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AuthenticationSuccessEventHandler
    implements ApplicationListener<AuthenticationSuccessEvent> {

  @Autowired
  private UserRepository userRepository;

  @Override
  @Transactional
  public void onApplicationEvent(AuthenticationSuccessEvent event) {
    if (event.getAuthentication() instanceof UsernamePasswordAuthenticationToken
        && event.getAuthentication().getPrincipal() instanceof UserDetailsImpl) {
      UserAuthDto userAuthDto =
          ((UserDetailsImpl) event.getAuthentication().getPrincipal()).getUserAuthDto();

      User user = userRepository.findByUuid(userAuthDto.getUserId()).orElseThrow(
          () -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAuthDto.getUserId()));
      user.setLastLoginTime(ZonedDateTime.now());

      // log.info(">> authenticationSuccessEvent : Updated Last login time of User : {}",
      //     userAuthDto.getUserId());
    }
  }

}
