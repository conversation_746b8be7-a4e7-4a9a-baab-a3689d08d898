package com.ma.spoton.api.security;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.entities.JwtBlacklistedToken;
import com.ma.spoton.api.repositories.JwtBlacklistedTokenRepository;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.utils.SecurityUtils;

@Service
public class JwtBlacklistedTokenService {

  @Autowired
  private JwtBlacklistedTokenRepository jwtBlacklistedTokenRepository;

  @Transactional
  public void createBlacklistedToken(String accessToken) {
    Map<String, Object> payload = SecurityUtils.getPayloadFromJwt(accessToken);
    JwtBlacklistedToken jwtBlacklistedToken = new JwtBlacklistedToken();
    jwtBlacklistedToken.setAccessToken(accessToken);
    jwtBlacklistedToken.setExpiryDate(
        DateTimeUtils.convertLongToZonedDateTime(((Integer) payload.get("exp")).longValue()));
    jwtBlacklistedTokenRepository.save(jwtBlacklistedToken);
  }

}
