package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.MATERIAL_HANDLER;
import static com.ma.spoton.api.constants.SystemRoles.PLANNING;
import static com.ma.spoton.api.constants.SystemRoles.SECURITY;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.TEAM_LEAD;
import static com.ma.spoton.api.constants.SystemRoles.TRAFFIC;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.LocationDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.FleetEntryExitRequest;
import com.ma.spoton.api.requests.LocationRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.LocationService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Locations APIs")
@RestController
@RequestMapping("/v1/clients/{clientId}/locations")
public class LocationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationController.class);
    @Autowired
    private LocationService locationService;

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + PLANNING + "', '" + TRAFFIC + "', '" + TEAM_LEAD
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to create new Location", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createLocation(@PathVariable String clientId,
                                               @Valid @RequestBody LocationRequest locationRequest) {

        long start = System.currentTimeMillis();
        locationService.createLocation(clientId, locationRequest);
        LoggerUtil.logSLA(LOGGER, "createLocation", start, "createLocation completed");
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }


    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + PLANNING + "', '" + TRAFFIC + "', '" + TEAM_LEAD
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to update Location", code = 204, consumes = "application/json")
    @PutMapping("/{locationId}")
    public ResponseEntity<Void> updateLocation(@PathVariable String clientId, @PathVariable String locationId,
                                               @Valid @RequestBody LocationRequest locationRequest) {
        log.info(">> LocationController >> updateLocation({}, {}, {})", clientId, locationId, locationRequest);
        long start = System.currentTimeMillis();
        locationService.updateLocation(clientId, locationId, locationRequest);
        LoggerUtil.logSLA(LOGGER, "updateLocation", start, "updateLocation completed");
        try {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating location! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_LOCATION_NAME);
        }
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + PLANNING + "', '" + TRAFFIC + "', '" + TEAM_LEAD
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to make Location Default", code = 204)
    @PatchMapping("/{locationId}/makeDefault")
    public ResponseEntity<Void> makeLocationDefault(@PathVariable String clientId,
                                                    @PathVariable String locationId) {

        long start = System.currentTimeMillis();
        locationService.makeLocationDefault(clientId, locationId);
        LoggerUtil.logSLA(LOGGER, "makeLocationDefault", start, "makeLocationDefault completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    @Secured({ADMIN, IT})
    @ApiOperation(value = "This API is used to make Location Default", code = 204)
    @DeleteMapping("/{locationId}/removeLocationImage")
    public ResponseEntity<Void> removeLocationImage(@PathVariable String clientId,
                                                    @PathVariable String locationId) {

        long start = System.currentTimeMillis();
        locationService.DeleteLocationImage(clientId, locationId);
        LoggerUtil.logSLA(LOGGER, "removeLocationImage", start, "removeLocationImage completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + PLANNING + "', '" + TRAFFIC + "', '" + TEAM_LEAD
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to delete Location", code = 204,
            consumes = "application/json")
    @DeleteMapping("/{locationId}")
    public ResponseEntity<Void> deleteLocation(@PathVariable String clientId,
                                               @PathVariable String locationId) {

        long start = System.currentTimeMillis();
        locationService.deleteLocation(clientId, locationId);
        LoggerUtil.logSLA(LOGGER, "deleteLocation", start, "deleteLocation completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation("This API is used to get Location by ID")
    @GetMapping("/{locationId}")
    public ResponseEntity<LocationDto> getLocation(@PathVariable String clientId,
                                                   @PathVariable String locationId) {

        long start = System.currentTimeMillis();
        LocationDto resource = locationService.getLocation(clientId, locationId, AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "getLocation", start, "getLocation completed");
        return ResponseEntity.ok(resource);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + TRAFFIC + "', '" + SECURITY + "', '" + TEAM_LEAD
                  + "', '" + PLANNING + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation("This API is used to get paginated list of Locations")
    @GetMapping
    public ResponseEntity<PagedResponse<LocationDto>> getLocations(@PathVariable String clientId,
                                                                   @QuerydslPredicate(root = Location.class) Predicate predicate,
                                                                   Pageable pageable,
                                                                   @RequestParam(required = false) String userId) {

        long start = System.currentTimeMillis();
        PagedResponse<LocationDto> resource = locationService.getLocations(clientId, predicate, pageable,
                                                                           AuthDetailsProvider.getTimeZone(), userId);
        LoggerUtil.logSLA(LOGGER, "getLocations", start, "getLocations completed");
        return ResponseEntity.ok(resource);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + GUARD + "', '" + SUPERVISOR + "', '" + SECURITY + "')")
    @ApiOperation("This API is used to create Fleet entry / exit record with notes")
    @PostMapping("/{locationId}/fleetEntryExit")
    public ResponseEntity<Void> fleetEntryExit(@PathVariable String clientId,
                                               @PathVariable String locationId,
                                               @Valid @RequestBody FleetEntryExitRequest fleetEntryExitRequest) {

        long start = System.currentTimeMillis();
        locationService.createFleetEntryExit(clientId, locationId, fleetEntryExitRequest,
                                             AuthDetailsProvider.getLoggedInUser().getUserId(),
                                             AuthDetailsProvider.isRolePresent(GUARD));
        LoggerUtil.logSLA(LOGGER, "fleetEntryExit", start, "fleetEntryExit completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + GUARD + "', '" + SUPERVISOR + "', '" + SECURITY + "')")
    @ApiOperation("This API is used to create Fleet entry / exit record with notes")
    @PostMapping(value = "/{locationId}/fleetEntryExitNew", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Void> fleetEntryExitNew(@PathVariable String clientId,
                                                  @PathVariable String locationId,
                                                  @Valid FleetEntryExitRequest fleetEntryExitRequest) {

        long start = System.currentTimeMillis();
        locationService.createFleetEntryExit(clientId, locationId, fleetEntryExitRequest,
                                             AuthDetailsProvider.getLoggedInUser().getUserId(),
                                             AuthDetailsProvider.isRolePresent(GUARD));
        LoggerUtil.logSLA(LOGGER, "fleetEntryExitNew", start, "fleetEntryExitNew completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @ApiOperation("This API is used to export list of Locations as CSV")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + TRAFFIC + "', '" + SECURITY + "', '" + TEAM_LEAD
                  + "', '" + PLANNING + "', '" + MATERIAL_HANDLER + "')")
    @GetMapping("/export/csv")
    public ResponseEntity<Resource> exportLocationsAsCsv(@PathVariable String clientId,
                                                         @QuerydslPredicate(root = Location.class) Predicate predicate) {

        long start = System.currentTimeMillis();
        Resource resource = locationService.exportLocationsAsCSV(clientId, predicate,
                                                                 AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportLocationsAsCsv", start, "exportLocationsAsCsv completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Locations.csv")
                .contentType(MediaType.parseMediaType("text/csv")).body(resource);
    }

    @ApiOperation("This API is used to export list of Locations as EXCEL")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + TRAFFIC + "', '" + SECURITY + "', '" + TEAM_LEAD
                  + "', '" + PLANNING + "', '" + MATERIAL_HANDLER + "')")
    @GetMapping("/export/excel")
    public ResponseEntity<Resource> exportLocationsAsExcel(@PathVariable String clientId,
                                                           @QuerydslPredicate(root = Location.class) Predicate predicate) {

        long start = System.currentTimeMillis();
        Resource resource = locationService.exportLocationsAsEXCEL(clientId, predicate,
                                                                   AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportLocationsAsExcel", start, "exportLocationsAsExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Locations_Report.xlsx")
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + SUPERVISOR + "')")
    @ApiOperation("This API is used to delete GuardEntryExit")
    @DeleteMapping("/entryExits/{guardEntryExitId}")
    public ResponseEntity<Void> deleteGuardEntryExit(@PathVariable String clientId,
                                                     @PathVariable String guardEntryExitId) {

        long start = System.currentTimeMillis();
        locationService.deleteGuardEntryExit(guardEntryExitId);
        LoggerUtil.logSLA(LOGGER, "deleteGuardEntryExit", start, "deleteGuardEntryExit completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
