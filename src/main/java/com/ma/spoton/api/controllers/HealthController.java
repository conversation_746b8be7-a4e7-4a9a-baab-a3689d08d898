package com.ma.spoton.api.controllers;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class HealthController {

    @GetMapping("/api/health")
    public ResponseEntity<Void> getFleets() {
        return ResponseEntity.status(HttpStatus.OK).build();
    }
}
