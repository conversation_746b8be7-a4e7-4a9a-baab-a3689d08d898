package com.ma.spoton.api.controllers;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.LocationAvgTimeDTO;
import com.ma.spoton.api.dtos.LocationDwellTimeDTO;
import com.ma.spoton.api.services.AvgTimeService;
import com.ma.spoton.api.services.DwellTimeService;
import com.ma.spoton.api.utils.LoggerUtil;

@RestController
@RequestMapping("/api/dwell-time")
public class DwellTimeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DwellTimeController.class);
    @Autowired
    private DwellTimeService dwellTimeService;
    @Autowired
    private AvgTimeService avgTimeService;

    @GetMapping("/client/{clientId}")
    public ResponseEntity<List<LocationDwellTimeDTO>> getAverageDwellTime(@PathVariable String clientId,
                                                                          @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {
        LOGGER.info(">> getAverageDwellTime : ClientId  , isRefresh : ({}, {})", clientId, isRefresh);
        long start = System.currentTimeMillis();
        List<LocationDwellTimeDTO> dwellTimes = dwellTimeService.calculateAverageDwellTime(clientId, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getAverageDwellTime", start, "getAverageDwellTime completed");
        return ResponseEntity.ok(dwellTimes);
    }

    private Long convertClientId(String clientId) {
        // Implement logic to convert UUID or string to Long if needed
        // For now, assume it should be parsed or mapped to a numeric ID
        try {
            return Long.parseLong(clientId); // If clientId is numeric
        } catch (NumberFormatException e) {
            // Handle case where clientId is a UUID or invalid
            throw new IllegalArgumentException("Invalid clientId format: " + clientId);
        }
    }

    @GetMapping("/client/{clientId}/avgEmptiedTime")
    public ResponseEntity<List<LocationAvgTimeDTO>> getAverageLastEmptiedAndLastOccupiedTime(
            @PathVariable String clientId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {
        LOGGER.info(">> getAverageLastEmptiedAndLastOccupiedTime : ClientId  , isRefresh : ({}, {})", clientId,
                    isRefresh);
        long start = System.currentTimeMillis();
        List<LocationAvgTimeDTO> averageEmptiedAndOccupiedTime =
                avgTimeService.computeAvgEmptiedAndOccupiedTime(clientId, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getAverageLastEmptiedAndLastOccupiedTime", start,
                          "getAverageLastEmptiedAndLastOccupiedTime completed");
        return ResponseEntity.ok(averageEmptiedAndOccupiedTime);
    }
}