package com.ma.spoton.api.controllers;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.constants.MessageMode;
import com.ma.spoton.api.constants.SystemRoles;
import com.ma.spoton.api.dtos.MessageDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.PushMessageRequest;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserDevice;
import com.ma.spoton.api.repositories.UserDeviceRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.ContactUsRequest;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.requests.MessageStatusRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.FCMPushNotificationServiceImpl;
import com.ma.spoton.api.services.MessageService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Message APIs")
@RestController
@RequestMapping("/v1/messages")
public class MessageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageController.class);
    @Autowired
    PushMessageRequest pushmessagerequest;
    @Autowired
    UserRepository userrepository;
    @Autowired
    UserDeviceRepository userdevicerepository;
    @Autowired
    private MessageService messageService;
    @Autowired
    private FCMPushNotificationServiceImpl fcmpushnotificationserviceimpl;

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to create new Message", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createMessage(@Valid @RequestBody MessageRequest messageRequest) {

        long start = System.currentTimeMillis();
        String messageBody = messageRequest.getMessageBody();
        List<String> userUuids = messageRequest.getToUserIds();
        List<User> users = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        List<UserDevice> totalUserDevices = new ArrayList<>();
        Set<String> deviceRegistrationIds = new HashSet<>();

        for (String userUuid : userUuids) {
            User user = userrepository.findByUserUuid(userUuid);
            users.add(user);
        }
        for (User user : users) {
            Long userId = user.getId();
            userIds.add(userId);
        }
        for (Long userId : userIds) {
            List<UserDevice> userdevices = userdevicerepository.findByUserId(userId);
            totalUserDevices.addAll(userdevices);
        }
        for (UserDevice userdevice : totalUserDevices) {
            String deviceRegistrationId = userdevice.getDeviceRegistrationId();
            deviceRegistrationIds.add(deviceRegistrationId);
        }
        for (String deviceRegistrationId1 : deviceRegistrationIds) {
            fcmpushnotificationserviceimpl.push(deviceRegistrationId1, messageBody);
        }

        messageService.createMessage(AuthDetailsProvider.getLoggedInUser().getUserId(), messageRequest);
        LoggerUtil.logSLA(LOGGER, "createMessage", start, "createMessage completed");
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    @ApiOperation("This API is used to get paginated list of Messages")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<PagedResponse<MessageDto>> getMessages(
            @RequestParam(required = false) MessageMode mode,
            @QuerydslPredicate(root = Message.class) Predicate predicate, Pageable pageable) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        PagedResponse<MessageDto> resource = messageService.getMessages(mode, predicate, pageable, user.getUserId(),
                                                                        user.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "getMessages", start, "getMessages completed");
        return ResponseEntity.ok(resource);
    }

    @ApiOperation("This API is used to get message by ID")
    @GetMapping("/{messageId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<MessageDto> getMessage(@PathVariable String messageId) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        MessageDto resource = messageService.getMessage(messageId, user.getUserId(), user.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "getMessage", start, "getMessage completed");
        return ResponseEntity.ok(resource);
    }

    @ApiOperation("This API is used to update message status")
    @PatchMapping("/{messageId}/status")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> updateMessageStatus(@PathVariable String messageId,
                                                    @Valid @RequestBody MessageStatusRequest messageStatusRequest) {

        long start = System.currentTimeMillis();
        messageService.updateMessageStatus(messageId, messageStatusRequest,
                                           AuthDetailsProvider.getLoggedInUser().getUserId());
        LoggerUtil.logSLA(LOGGER, "updateMessageStatus", start, "updateMessageStatus completed");
        return ResponseEntity.noContent().build();
    }

    @ApiOperation("This API is used to delete message")
    @DeleteMapping("/{messageId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> deleteMessage(@PathVariable String messageId) {

        long start = System.currentTimeMillis();
        Optional<String> fromUserId = Optional.of(AuthDetailsProvider.getLoggedInUser().getUserId());
        if (AuthDetailsProvider.isRolePresent(SystemRoles.IT)
            || AuthDetailsProvider.isRolePresent(SystemRoles.ADMIN)) {
            fromUserId = Optional.empty();
        }
        messageService.deleteMessage(messageId, fromUserId);
        LoggerUtil.logSLA(LOGGER, "deleteMessage", start, "deleteMessage completed");
        return ResponseEntity.noContent().build();
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to create new contact us email", code = 201,
            consumes = "application/json")
    @PostMapping("/contact-us")
    public ResponseEntity<Void> contactEmail(@Valid @RequestBody ContactUsRequest contactUsRequest) {

        long start = System.currentTimeMillis();
        messageService.contactEmail(AuthDetailsProvider.getLoggedInUser().getUserId(), contactUsRequest);
        LoggerUtil.logSLA(LOGGER, "createContactUs", start, "createContactUs completed");
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }
}