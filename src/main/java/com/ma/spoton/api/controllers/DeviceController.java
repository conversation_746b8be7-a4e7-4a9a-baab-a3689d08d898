package com.ma.spoton.api.controllers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.services.UserDeviceService;
import com.ma.spoton.api.utils.LoggerUtil;

@RestController
@RequestMapping
public class DeviceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceController.class);
    @Autowired
    UserDeviceService userdeviceservice;

    @PostMapping("/v1/deviceregister/{deviceUuid}/{deviceRegistrationId}")
    public String registerUpdateDevice(@PathVariable String deviceUuid, @PathVariable String deviceRegistrationId) {
        long start = System.currentTimeMillis();
        userdeviceservice.updateDeviceRegistrationId(deviceUuid, deviceRegistrationId);
        LoggerUtil.logSLA(LOGGER, "registerUpdateDevice", start, "registerUpdateDevice completed");
        return "device Updated";
    }
}