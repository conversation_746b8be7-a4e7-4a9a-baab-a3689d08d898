package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.entities.Job.Bucket;

import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.controllers.validators.BoxTruckJobValidator;
import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.RoleAverageTimeResponse;
import com.ma.spoton.api.dtos.RoleUpdateDTO;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserUpdateDTO;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.ChangeQueueOrderRequest;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.requests.JobStatusRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.JobService;
import com.ma.spoton.api.services.KpiService;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Jobs APIs")
@RestController
@RequestMapping("/v1/jobs")
public class JobController {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobController.class);
    @Autowired
    private JobService jobService;
    @Autowired
    private KpiService kpiService;
    @Autowired
    private UserService userService;
    @Autowired
    private BoxTruckJobValidator boxTruckJobValidator;
    @Value("${application.api-base-url}")
    private String apiBaseUrl;
    @Value("${application.version}")
    private String version;

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to create new Job", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createJob(@Valid @RequestBody JobRequest jobRequest,
                                          @RequestParam(required = false) Optional<String> scheduleDateTime,
                                          @RequestParam(required = false) String clientId) {

        long start = System.currentTimeMillis();
        if (AuthDetailsProvider.isRolePresent(DRIVER) && !AuthDetailsProvider.getLoggedInUser()
                .getUserId().equals(jobRequest.getAssignedToUserId())) {
            throw new ServiceException(ErrorCode.DRIVER_JOB_CREATE_UPDATE_ACCESS);
        }

        boxTruckJobValidator.validateCreateJob(jobRequest, clientId, scheduleDateTime);

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        Job job;

        String scheduleDate = null;

        if (scheduleDateTime.isPresent()) {
            String rawSchedule = scheduleDateTime.get();
            Pattern pattern = Pattern.compile("[\"']?(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2})[\"']?");
            Matcher matcher = pattern.matcher(rawSchedule.trim());

            if (matcher.find()) {
                scheduleDate = matcher.group(1); // Extract clean date-time (no quotes)
            } else {
                // Fallback to now if pattern not matched
                scheduleDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            }
            job = jobService.createScheduledJob(jobRequest, user.getTimeZone(), scheduleDate);
            LoggerUtil.logSLA(LOGGER, "createScheduledJob", start, "createScheduledJob completed");
        } else {
            job = jobService.createJobNow(jobRequest, user.getTimeZone());
            LoggerUtil.logSLA(LOGGER, "createJob", start, "createJob completed");
        }
        return ResponseEntity
                .created(URI.create(String.format("%s/%s/jobs/%s", apiBaseUrl, version, job.getUuid())))
                .build();
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to update Job", code = 204, consumes = "application/json")
    @PutMapping("/{jobId}")
    public ResponseEntity<Void> updateJob(@PathVariable String jobId,
                                          @Valid @RequestBody JobRequest jobRequest,
                                          @RequestParam(required = false) Optional<String> scheduleDateTime,
                                          @RequestParam(required = false) String clientId) {

        long start = System.currentTimeMillis();
        Optional<String> userId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER)) {
            userId = Optional.of(AuthDetailsProvider.getLoggedInUser().getUserId());
        }

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        boxTruckJobValidator.validateUpdateJob(jobRequest, clientId, scheduleDateTime);

        if (scheduleDateTime.isPresent()) {
            String scheduleDate = scheduleDateTime.orElse(LocalDateTime.now().toString());
            jobService.rescheduleJob(jobId, jobRequest, userId, user.getTimeZone(), scheduleDate);
            LoggerUtil.logSLA(LOGGER, "rescheduleJob", start, "rescheduleJob completed");
            ;
        } else {
            jobService.updateJob(jobId, jobRequest, userId, user.getTimeZone());
            LoggerUtil.logSLA(LOGGER, "updateJob", start, "updateJob completed");
        }

        return ResponseEntity.noContent().build();
    }

    /**
     * List / Get JOB
     * <p>
     * DRIVER can see jobs assigned to him/her
     * <p>
     * SPOTTER can see jobs assigned to him/her
     * <p>
     * GUARD can see jobs created by him/her and also jobs assigned to him/her
     * <p>
     * SUPERVISOR, CLIENT can see only jobs created for their client
     * <p>
     * IT, ADMIN can see all jobs
     */
    @ApiOperation("This API is used to get paginated list of Jobs")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<PagedResponse<JobDto>> getJobs(
            @RequestParam(required = false) String fromDate,
            @RequestParam(required = false) String toDate,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) String notesOrAsn,
            @RequestParam(required = false) String roleNames,
            @RequestParam(required = false) Boolean allClientJobs,
            @RequestParam(required = false) Boolean trailerHistory,
            @RequestParam(required = false) String userIds,
            @RequestParam(required = false) String jobstatus,
            @RequestParam(required = false) Bucket bucketType,
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if ((allClientJobs == null) && (AuthDetailsProvider.isRolePresent(DRIVER) || AuthDetailsProvider.isRolePresent(
                SPOTTER))) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        createdByUserId = Optional.ofNullable(createdBy);
        if (allClientJobs == null && AuthDetailsProvider.isRolePresent(GUARD)) {
            createdByUserId = Optional.of(user.getUserId());
            assignedToUserId = Optional.of(user.getUserId());
        }
        PagedResponse<JobDto> resource = jobService.getJobs(fromDate, toDate, predicate, pageable,
                                                            user.getTimeZone(), user.getClientIds(), assignedToUserId,
                                                            createdByUserId, notesOrAsn, roleNames, userIds,
                                                            trailerHistory, jobstatus, bucketType);
        LoggerUtil.logSLA(LOGGER, "getJobs", start, "getJobs completed");
        return ResponseEntity.ok(resource);
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to update Job status to pickup or drop", code = 204,
            consumes = "application/json")
    @PatchMapping("/{jobId}/status")
    public ResponseEntity<Void> updateJobStatus(@PathVariable String jobId,
                                                @Valid @RequestBody JobStatusRequest jobStatusRequest) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            createdByUserId = Optional.of(user.getUserId());
        }

        jobService.updateJobStatus(jobId, jobStatusRequest, AuthDetailsProvider.getTimeZone(),
                                   assignedToUserId, createdByUserId);
        LoggerUtil.logSLA(LOGGER, "updateJobStatus", start, "updateJobStatus completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    // Only Driver, Spotter should update this. Should we allow for IT, ADMIN (worst case)?
    @PreAuthorize("hasAnyClientRole(#clientId, '" + SPOTTER + "', '" + DRIVER + "')")
    @ApiOperation(value = "This API is used to modify status of each route/stop of BoxTruck/Van job", code = 204)
    @PatchMapping("/{jobId}/{routeId}/status")
    public ResponseEntity<Void> updateRouteStatus(
            @PathVariable String jobId,
            @PathVariable String routeId,
            // sending client so that it can be authorized only for the driver of same client.
            @RequestParam String clientId,
            @Valid @RequestBody JobStatusRequest jobStatusRequest) {

        long start = System.currentTimeMillis();

        boxTruckJobValidator.validatePatchJob(jobStatusRequest);

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        jobService.updateRouteStatus(jobId, routeId, jobStatusRequest, user.getUserId(), user.getTimeZone());

        LoggerUtil.logSLA(LOGGER, "patchJob", start, "patchJob completed");

        return ResponseEntity.accepted().build();
    }

    @ApiOperation("This API is used to export list of Jobs as CSV")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/export/csv")
    public ResponseEntity<Resource> exportJobsAsCsv(@RequestParam(required = false) String fromDate,
                                                    @RequestParam(required = false) String toDate,
                                                    @QuerydslPredicate(root = Job.class) Predicate predicate) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            createdByUserId = Optional.of(user.getUserId());
        }

        Boolean isSupervisorOrClient = false;
        if (AuthDetailsProvider.isRolePresent(CLIENT) || AuthDetailsProvider.isRolePresent(SUPERVISOR)) {
            isSupervisorOrClient = true;
        }

        Resource resource = jobService.exportJobsAsCSV(fromDate, toDate, predicate, user.getTimeZone(),
                                                       user.getClientIds(), assignedToUserId, createdByUserId,
                                                       isSupervisorOrClient);
        LoggerUtil.logSLA(LOGGER, "exportJobsAsCsv", start, "exportJobsAsCsv completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Jobs.csv")
                .contentType(MediaType.parseMediaType("text/csv")).body(resource);
    }

    @ApiOperation("This API is used to export list of Jobs as EXCEL")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/export/excel")
    public ResponseEntity<Resource> exportJobsAsExcel(@RequestParam(required = false) String fromDate,
                                                      @RequestParam(required = false) String toDate,
                                                      @QuerydslPredicate(root = Job.class) Predicate predicate) {

        LOGGER.info(">> exportJobsAsExcel ({}, {}, {})", fromDate, toDate, predicate);
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            createdByUserId = Optional.of(user.getUserId());
        }

        Boolean isSupervisorOrClient = false;
        if (AuthDetailsProvider.isRolePresent(CLIENT) || AuthDetailsProvider.isRolePresent(SUPERVISOR)) {
            isSupervisorOrClient = true;
        }

        Resource resource = jobService.exportJobsAsEXCEL(fromDate, toDate, predicate, user.getTimeZone(),
                                                         user.getClientIds(), assignedToUserId, createdByUserId,
                                                         isSupervisorOrClient);

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
        //    SimpleDateFormat outputFormat = new SimpleDateFormat("dd-MM-yyyy");

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                fromDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                toDate = outputFormat.format(date2);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        String fileName = "Spots_Report_";
        if (fromDate != null && toDate != null) {
            fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
        }
        fileName = fileName.concat(".xlsx");
        LoggerUtil.logSLA(LOGGER, "exportJobsAsExcel", start, "exportJobsAsExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }


    @ApiOperation("This API is used to export list of Moves as EXCEL")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/moves/export/excel")
    public ResponseEntity<Resource> exportMovesAsExcel(@RequestParam(required = false) String fromDate,
                                                       @RequestParam(required = false) String toDate,
                                                       @QuerydslPredicate(root = Job.class) Predicate predicate,
                                                       @RequestParam(required = false) String roleNames,
                                                       @RequestParam(required = false) String userIds,
                                                       @RequestParam(required = false) String jobstatus,
                                                       @RequestParam(required = false) Bucket bucketType) {

        LOGGER.info(">> exportMovesAsExcel ({}, {}, {})", fromDate, toDate, predicate);
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Resource resource =
                jobService.exportMovesAsEXCEL(fromDate, toDate, predicate, user.getTimeZone(), roleNames, userIds,
                                              jobstatus, bucketType);

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                fromDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                toDate = outputFormat.format(date2);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        String fileName = "Moves_Report_";
        if (fromDate != null && toDate != null) {
            fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
        }
        fileName = fileName.concat(".xlsx");
        LoggerUtil.logSLA(LOGGER, "exportMovesAsExcel", start, "exportMovesAsExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }


    @ApiOperation("This API is used to export list of Moves as Pdf")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/moves/export/pdf")
    public ResponseEntity<Resource> exportMovesAsPdf(@RequestParam(required = false) String fromDate,
                                                     @RequestParam(required = false) String toDate,
                                                     @RequestParam(required = true) String role,
                                                     @QuerydslPredicate(root = Job.class) Predicate predicate,
                                                     @RequestParam(required = false) String roleNames,
                                                     @RequestParam(required = false) String userIds,
                                                     @RequestParam(required = false) String jobstatus,
                                                     @RequestParam(required = false) Bucket bucketType) {

        LOGGER.info(">> exportMovesAsExcel ({}, {}, {}, {})", fromDate, toDate, role, predicate);
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Resource resource =
                jobService.exportMovesAsPDF(fromDate, toDate, predicate, user.getTimeZone(), role, roleNames, userIds,
                                            jobstatus, bucketType);

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                fromDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                toDate = outputFormat.format(date2);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        String fileName = "Moves_Report_";
        if (fromDate != null && toDate != null) {
            fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
        }
        fileName = fileName.concat(".pdf");
        LoggerUtil.logSLA(LOGGER, "exportMovesAsPdf", start, "exportMovesAsPdf completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.parseMediaType("text/pdf")).body(resource);
    }


    @ApiOperation("This API is used to get single Job for loggedin user")
    @GetMapping("/mine")
    @Secured({SPOTTER, DRIVER})
    public ResponseEntity<PagedResponse<JobDto>> getMyJobs() {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        var response = new PagedResponse<JobDto>();
        if (AuthDetailsProvider.isRolePresent(DRIVER)) {
            JobDto myJob = jobService.getDriverJob(user.getUserId(), user.getTimeZone());
            response.setList(Objects.nonNull(myJob) ? List.of(myJob) : List.of());
        }
        if (AuthDetailsProvider.isRolePresent(SPOTTER)) {
            response.setList(jobService.getSpotterJobs(user.getUserId(), user.getTimeZone()));
        }
        LoggerUtil.logSLA(LOGGER, "getMyJobs", start, "getMyJobs completed");
        return ResponseEntity.ok(response);
    }

    @ApiOperation("This API is used to delete Job")
    @DeleteMapping("/{jobId}")
    @Secured({IT, ADMIN, SUPERVISOR})
    public ResponseEntity<Void> deleteFleet(@PathVariable String jobId) {
        long start = System.currentTimeMillis();
        jobService.deleteFleet(jobId);
        LoggerUtil.logSLA(LOGGER, "deleteFleet", start, "deleteFleet completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @ApiOperation("This API is used to change queue order")
    @PostMapping("/changeQueueOrder")
    @Secured({IT, ADMIN, SUPERVISOR})
    public ResponseEntity<Void> changeQueueOrder(@Valid @RequestBody ChangeQueueOrderRequest changeQueueOrderRequest) {
        long start = System.currentTimeMillis();
        jobService.changeJobQueuePositions(changeQueueOrderRequest.getClientId(), changeQueueOrderRequest.getBucket(),
                                           changeQueueOrderRequest.getDragIndex(),
                                           changeQueueOrderRequest.getDropIndex());
        LoggerUtil.logSLA(LOGGER, "changeQueueOrder", start, "changeQueueOrder completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @ApiOperation("This API is used to get paginated list of Jobs and calculate average time per user")
    @GetMapping("/average-move-time")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Map<String, String>>> getAverageMoveTime(
            @RequestParam(required = false) String fromDate,
            @RequestParam(required = false) String toDate,
            @RequestParam(required = false) String clientId,
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        log.info(">> getAvgJobs({}, {}, {}, {})", fromDate, toDate, predicate, pageable);
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD)) {
            createdByUserId = Optional.of(user.getUserId());
            assignedToUserId = Optional.of(user.getUserId());
        }

        Map<String, Map<String, String>> jobDtos =
                kpiService.getJobsForAvgMovesByDriverAndSpotter(predicate, user.getTimeZone(), clientId, isRefresh);

        LoggerUtil.logSLA(LOGGER, "getAverageMoveTime", start, "getAverageMoveTime completed");
        return ResponseEntity.ok(jobDtos);
    }


    @ApiOperation("This API is used to get paginated list of Jobs and calculate average time per user in minutes")
    @GetMapping("/client/average-move-time")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<RoleAverageTimeResponse>> getClientAverageMoveTime(
            @RequestParam(required = false) String fromDate,
            @RequestParam(required = false) String toDate,
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable) {

        log.info(">> getAvgJobs({}, {}, {}, {})", fromDate, toDate, predicate, pageable);
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD)) {
            createdByUserId = Optional.of(user.getUserId());
            assignedToUserId = Optional.of(user.getUserId());

        }

        List<JobDto> jobDtos = jobService.getJobsForAvgMovesClient(fromDate, toDate, predicate, pageable,
                                                                   user.getTimeZone(), user.getClientIds(),
                                                                   assignedToUserId, createdByUserId, "").getList();

        Map<String, List<JobDto>> jobsByUser = jobDtos.stream()
                .filter(job -> job.getAssignedTo() != null && job.getStatus().equals(Job.Status.COMPLETED))
                .collect(Collectors.groupingBy(job -> job.getAssignedTo().getUserId()));

        Map<String, Long> totalTimeByRole = new HashMap<>();
        Map<String, Integer> countByRole = new HashMap<>();

        for (Map.Entry<String, List<JobDto>> entry : jobsByUser.entrySet()) {
            String userId = entry.getKey();
            List<JobDto> userJobs = entry.getValue();

            long totalTime = 0;
            int count = 0;

            for (JobDto job : userJobs) {
                if (job.getPickupDateTime() != null && job.getDropDateTime() != null) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm a");
                        LocalDateTime pickupTime = LocalDateTime.parse(job.getPickupDateTime(), formatter);
                        LocalDateTime dropTime = LocalDateTime.parse(job.getDropDateTime(), formatter);

                        long durationInMinutes = Duration.between(pickupTime, dropTime).toMinutes();
                        totalTime += durationInMinutes;
                        count++;
                    } catch (Exception e) {
                        log.error("Error calculating time for job: " + job.getJobId(), e);
                    }
                }
            }

            if (count > 0) {
                UserUpdateDTO user1 = userService.getUser(userJobs.get(0).getAssignedTo().getUserId());
                Set<RoleUpdateDTO> roleUpdateDTOSet = user1.getRoles();

                Optional<String> userRole = roleUpdateDTOSet.stream()
                        .map(RoleUpdateDTO::getRoleName)
                        .filter(role -> role.equalsIgnoreCase("DRIVER") || role.equalsIgnoreCase("SPOTTER"))
                        .findFirst();

                if (userRole.isPresent()) {
                    String role = userRole.get();
                    totalTimeByRole.put(role, totalTimeByRole.getOrDefault(role, 0L) + totalTime);
                    countByRole.put(role, countByRole.getOrDefault(role, 0) + count);
                }
            }
        }

        List<RoleAverageTimeResponse> response = totalTimeByRole.entrySet().stream()
                .map(entry -> {
                    String role = entry.getKey();
                    long totalMinutes = entry.getValue();
                    int count = countByRole.getOrDefault(role, 1);
                    long averageTimeInMinutes = totalMinutes / count;
                    return new RoleAverageTimeResponse(role, averageTimeInMinutes);
                })
                .collect(Collectors.toList());
        LoggerUtil.logSLA(LOGGER, "getClientAverageMoveTime", start, "getClientAverageMoveTime completed");
        return ResponseEntity.ok(response);
    }


    @ApiOperation("This API is used to get paginated list of Jobs and total number of moves made by all the driver/spotter")
    @GetMapping("/total-moves")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Map<String, Integer>>> getTotalNumMoves(
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable,
            @RequestParam(required = false) String clientId) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        ResponseEntity<Map<String, Map<String, Integer>>> resource =
                jobService.fetchAverageMoves(user.getTimeZone(), clientId);
        LoggerUtil.logSLA(LOGGER, "getTotalNumMoves", start, "getTotalNumMoves completed");
        return resource;
    }

    @ApiOperation("This API is used to get paginated list of Jobs and aggregated number of moves made by all the driver/spotter")
    @GetMapping("/client/total-moves")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Map<String, Integer>>> getTotalAggMoves(
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable,
            @RequestParam(required = false) String clientId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        return kpiService.fetchAverageMoves(user.getTimeZone(), clientId, isRefresh);
    }

    @ApiOperation("This API is used to get paginated list of Jobs and calculate average turn around time per user")
    @GetMapping("/average-move-turn-time")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Map<String, String>>> getAverageMoveTurnAroundTime(
            @RequestParam(required = false) String fromDate,
            @RequestParam(required = false) String toDate,
            @RequestParam(required = false) String clientId,
            @QuerydslPredicate(root = Job.class) Predicate predicate, Pageable pageable,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Optional<String> assignedToUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(DRIVER) || AuthDetailsProvider.isRolePresent(SPOTTER)) {
            assignedToUserId = Optional.of(user.getUserId());
        }

        Optional<String> createdByUserId = Optional.empty();
        if (AuthDetailsProvider.isRolePresent(GUARD)) {
            createdByUserId = Optional.of(user.getUserId());
            assignedToUserId = Optional.of(user.getUserId());
        }

        Map<String, Map<String, String>> jobDtos =
                kpiService.getJobsForAvgTurnMovesByDriverAndSpotter(predicate, user.getTimeZone(), clientId, isRefresh);

        return ResponseEntity.ok(jobDtos);
    }

    @ApiOperation("This API is used to export list of Moves as EXCEL")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/trailer-history/export/excel")
    public ResponseEntity<Resource> exportTrailerHistoryAsExcel(@RequestParam(required = false) String fromDate,
                                                                @RequestParam(required = false) String toDate,
                                                                @QuerydslPredicate(root = Job.class) Predicate predicate) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Resource resource = jobService.exportTrailerHistorysAsEXCEL(fromDate, toDate, predicate, user.getTimeZone());

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                fromDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                toDate = outputFormat.format(date2);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        String fileName = "Trailer_History_";
        if (fromDate != null && toDate != null) {
            fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
        }
        fileName = fileName.concat(".xlsx");
        LoggerUtil.logSLA(LOGGER, "exportTrailerHistoryAsExcel", start, "exportTrailerHistoryExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }

    @ApiOperation("This API is used to export list of Moves as Pdf")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/trailer-history/export/pdf")
    public ResponseEntity<Resource> exportTrailerHistoryAsPdf(@RequestParam(required = false) String fromDate,
                                                              @RequestParam(required = false) String toDate,
                                                              @QuerydslPredicate(root = Job.class) Predicate predicate) {

        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        Resource resource = jobService.exportTrailerHistoryAsPDF(fromDate, toDate, predicate, user.getTimeZone());

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                fromDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                toDate = outputFormat.format(date2);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        String fileName = "Trailer_History_";
        if (fromDate != null && toDate != null) {
            fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
        }
        fileName = fileName.concat(".pdf");
        LoggerUtil.logSLA(LOGGER, "trailerHistoryAsPdf", start, "trailerHistoryAsPdf completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                .contentType(MediaType.parseMediaType("text/pdf")).body(resource);
    }
}
