package com.ma.spoton.api.controllers;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.services.UserPreferencesService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Users APIs")
@RestController
@RequestMapping("/v1/users/preferences")
public class UserPreferencesController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserPreferencesController.class);
    @Autowired
    private UserPreferencesService userPreferencesService;

    /*
        API Endpoint
        URL: POST /v1/users/preferences/WIDGET
        Method: POST
        Content-Type: application/json

        Request Body
        {
          "userId": 123,
          "widgets": ["AVERAGE_MOVE_TIME", "TOTAL_NO_SPOTS", "KPI_METRICS"]
        }
     */
    @PostMapping("/widget")
    public ResponseEntity<Void> saveWidgetPreferences(@RequestParam String userId, @RequestBody List<String> widgets) {
        long start = System.currentTimeMillis();
        userPreferencesService.saveUserWidgetPreferences(userId, widgets);
        LoggerUtil.logSLA(LOGGER, "saveWidgetPreferences", start, "saveWidgetPreferences completed");
        return ResponseEntity.ok().build();
    }

    @PostMapping("/layout")
    public ResponseEntity<Void> saveLayout(@RequestParam String userId, @RequestBody String widgets) {
        long start = System.currentTimeMillis();
        userPreferencesService.saveLayout(userId, widgets);
        LoggerUtil.logSLA(LOGGER, "saveLayout", start, "saveLayout completed");
        return ResponseEntity.ok().build();
    }

    @GetMapping("/widget")
    public ResponseEntity<List<String>> getWidgetPreferences(@RequestParam String userId) {
        long start = System.currentTimeMillis();
        List<String> preferences = userPreferencesService.getUserWidgetPreferences(userId);
        LoggerUtil.logSLA(LOGGER, "getWidgetPreferences", start, "getWidgetPreferences completed");
        return ResponseEntity.ok(preferences);
    }

    @GetMapping("/layout")
    public ResponseEntity<String> getLayout(@RequestParam String userId) {
        long start = System.currentTimeMillis();
        String layout = userPreferencesService.getLayout(userId);
        LoggerUtil.logSLA(LOGGER, "getLayout", start, "getLayout completed");
        return ResponseEntity.ok(layout);
    }

    @DeleteMapping("/widget")
    public ResponseEntity<Void> removePreference(@RequestParam String userId) {
        long start = System.currentTimeMillis();
        userPreferencesService.removePreferences(userId);
        LoggerUtil.logSLA(LOGGER, "removePreference", start, "removePreference completed");
        return ResponseEntity.ok().build();
    }
}
