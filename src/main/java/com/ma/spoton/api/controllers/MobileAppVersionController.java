package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.IT;

import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.amazonaws.services.organizations.model.ConstraintViolationException;
import com.ma.spoton.api.dtos.MobileAppUpdateDto;
import com.ma.spoton.api.dtos.MobileAppVersionDto;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.MobileAppVersionRequest;
import com.ma.spoton.api.services.MobileAppVersionService;
import com.ma.spoton.api.utils.LoggerUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/v1/mobile/version")
public class MobileAppVersionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MobileAppVersionController.class);
    @Autowired
    private MobileAppVersionService mobileAppService;

    @GetMapping("/status")
    public ResponseEntity<MobileAppUpdateDto> getMobileAppUpdateStatus(
            @RequestParam(required = true) float currentVersion,
            @RequestParam(required = true) String OS) {
        try {
            long start = System.currentTimeMillis();
            MobileAppUpdateDto resource = mobileAppService.getMobileAppUpdatStatus(currentVersion, OS);
            LoggerUtil.logSLA(LOGGER, "getMobileAppUpdateStatus", start, "getMobileAppUpdateStatus completed");
            return ResponseEntity.ok(resource);
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating client! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.FORCE_UPDATION_ERROR);
        }
    }


    @PostMapping
    @Secured({IT, ADMIN})
    public ResponseEntity<Void> updateMobileAppVersion(
            @RequestBody @Valid MobileAppVersionRequest mobileAppVersionRequest) {
        try {
            long start = System.currentTimeMillis();
            mobileAppService.updateMobileVersion(mobileAppVersionRequest.getCurrentVersion(),
                                                 mobileAppVersionRequest.getOs());
            LoggerUtil.logSLA(LOGGER, "updateMobileAppVersion", start, "updateMobileAppVersion completed");
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating client! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.CONFIG_UPDATE_ERROR);
        }
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    @GetMapping
    @Secured({IT, ADMIN})
    public ResponseEntity<List<MobileAppVersionDto>> getLatestMobileAppVersion() {
        try {
            long start = System.currentTimeMillis();

            List<MobileAppVersionDto> resource = mobileAppService.getLatestMobileAppVersion();
            LoggerUtil.logSLA(LOGGER, "updateMobileAppVersion", start, "updateMobileAppVersion completed");
            return ResponseEntity.ok(resource);
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating client! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.FORCE_UPDATION_ERROR);
        }
    }
}
