package com.ma.spoton.api.controllers;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.util.List;
import java.util.Map;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.PropertyDto;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.PropertyRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.PropertyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(value = "Property APIs")
@RestController
@RequestMapping("/v1/properties")
public class PropertyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PropertyController.class);
    @Autowired
    private PropertyService propertyService;

    @PreAuthorize("isAuthenticated()")
    @GetMapping
    @ApiOperation(value = "This API is used to get list of property.")
    public List<PropertyDto> getProperties(@RequestParam(required = false) String timezone,
                                           @RequestParam(required = false) Boolean showArchived,
                                           @RequestParam(required = false) Boolean showAuditDetails) {
        log.info("This API is used to get list of property.");
        showArchived = nonNull(showArchived) ? showArchived.booleanValue() : Boolean.FALSE;
        showAuditDetails = nonNull(showAuditDetails) ? showAuditDetails.booleanValue() : Boolean.TRUE;
        timezone = isNotBlank(timezone) ? timezone : AuthDetailsProvider.getTimeZone();
        return propertyService.getProperties(timezone, showArchived, showAuditDetails);
    }

    @PreAuthorize("isAuthenticated()")
    @GetMapping("/{propertyId}")
    @ApiOperation(value = "This API is used to get property by id.")
    public PropertyDto getPropertyById(@PathVariable String propertyId,
                                       @RequestParam(required = false) String timezone) {

        log.info("This API is used to get property by id. - getPropertyById()");
        timezone = isNotBlank(timezone) ? timezone : AuthDetailsProvider.getTimeZone();
        return propertyService.getPropertyById(propertyId, timezone);
    }

    @PreAuthorize("isAuthenticated()")
    @GetMapping("/data")
    @ApiOperation(value = "This API is used to get Property data.")
    public Map<String, Object> getPropertyData() {

        log.info("This API is used to get Property data. - getPropertyData()");
        return propertyService.getPropertyData();
    }

    @PreAuthorize("isAuthenticated()")
    @PostMapping
    @ApiOperation(value = "This API is used to create new Property.", code = 201)
    public ResponseEntity<Void> createProperty(@Valid @RequestBody PropertyRequest propertyRequest) {
        log.info("This API is used to create new Property. - createProperty()");
        propertyService.createProperty(propertyRequest);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }


    @PreAuthorize("isAuthenticated()")
    @PutMapping("/{propertyId}")
    @ApiOperation(value = "This API is used to update existing property.")
    public ResponseEntity<Void> updateProperty(@PathVariable String propertyId,
                                               @Valid @RequestBody PropertyRequest propertyRequest) {
        log.info("This API is used to update existing property. - updateProperty()");
        try {
            propertyService.updateProperty(propertyId, propertyRequest);
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Property by this name already exists.", e);
            throw new ServiceException(ErrorCode.PROPERTY_ALREADY_EXISTS);
        }
    }

    @PreAuthorize("isAuthenticated()")
    @DeleteMapping("/{propertyId}")
    @ApiOperation(value = "This API is used to delete property.")
    public ResponseEntity<Void> deleteProperty(@PathVariable String propertyId) {
        log.info("This API is used to delete property. - deleteProperty()");
        propertyService.deleteProperty(propertyId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
