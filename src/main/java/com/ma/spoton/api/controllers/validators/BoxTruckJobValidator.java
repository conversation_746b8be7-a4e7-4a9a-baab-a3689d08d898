package com.ma.spoton.api.controllers.validators;

import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.requests.JobStatusRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;

/**
 * This class validates request/payload and allow only the valid access/payload for boxtruck/van job.
 */
@Component
public class BoxTruckJobValidator {

    private void validateAccess(JobRequest jobRequest, String clientId) {
        // only ADMIN and SUPERVISOR can create boxtruck/van
        if (jobRequest.isBoxTruckOrVan()) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String[] roles = {SUPERVISOR}; // IT and ADMIN are allowed by default.
            boolean isAuthorized = AuthDetailsProvider.hasAnyClientRole(authentication, clientId, roles);
            if (!isAuthorized) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_ACCESS);
            }
        }
    }

    /*private void validateCreateJobPayload(JobRequest jobRequest) {
        Set<JobRequest.IntermediateRoute> routes = jobRequest.getRouteList();
        if (routes.stream().anyMatch(r -> !PENDING.equals(r.getStatus()))) {
            throw new ServiceException(ErrorCode.BOXTRUCK_JOB_INVALID_STATE);
        }
    }*/

    private void validatePayload(JobRequest jobRequest, Optional<String> scheduleDateTime) {

        if (jobRequest.isBoxTruckOrVan()) {
            // Boxtruck cannot be scheduled
            if (scheduleDateTime.isPresent()) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_UPDATE_NO_SCHEDULE);
            }

            // Boxtruck cannot be assigned BUCKET
            if (jobRequest.getBucket() != null && !jobRequest.getBucket().equals(Job.Bucket.NIL)) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_UPDATE_NO_BUCKET);
            }

            // Boxtruck must have assigned driver
            if (StringUtils.isBlank(jobRequest.getAssignedToUserId())) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_UPDATE_MISSING_USER);
            }

            List<JobRequest.IntermediateRoute> routes = jobRequest.getRouteList();

            if (routes.stream()
                    .anyMatch(r -> StringUtils.isBlank(r.getLocationId()) ||
                                   StringUtils.isBlank(r.getSpotId()))) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_UPDATE_INVALID_SPOT_LOCATION);
            }

            // Including first and last stop, there must be least one additional stop.
            if (routes.size() < 3) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_CREATE_UPDATE_MIN_STOP);
            }
        }
    }

    /*private void validateUpdateJobPayload(JobRequest jobRequest) {
        Set<JobRequest.IntermediateRoute> routes = jobRequest.getRouteList();

        if (jobRequest.isBoxTruckOrVan() && !CollectionUtils.isEmpty(routes)) {
            boolean seenInTransit = false;
            int lastPhase = -1;

            for (JobRequest.IntermediateRoute route : routes) {
                Optional<JobRouteStep.StepStatus> statusOpt = route.getStatus();
                if (statusOpt.isEmpty()) {
                    throw new ServiceException(ErrorCode.BOXTRUCK_JOB_INVALID_STATE);
                }

                JobRouteStep.StepStatus status = statusOpt.get();
                int currentPhase = status.getPhaseOrder();

                // Ensure states are non-decreasing in phase order
                if (currentPhase < lastPhase) {
                    throw new ServiceException(ErrorCode.BOXTRUCK_JOB_UPDATE_STATE_ERROR);
                }

                // Allow only one IN_TRANSIT
                if (status == JobRouteStep.StepStatus.IN_TRANSIT) {
                    if (seenInTransit) {
                        throw new ServiceException(ErrorCode.BOXTRUCK_JOB_UPDATE_STATE_ERROR);
                    }
                    seenInTransit = true;
                }

                lastPhase = currentPhase;
            }
        }
    }*/

    public void validateCreateJob(JobRequest jobRequest, String clientId, Optional<String> scheduleDateTime) {
        validateAccess(jobRequest, clientId);
        validatePayload(jobRequest, scheduleDateTime);
    }

    public void validateUpdateJob(JobRequest jobRequest, String clientId, Optional<String> scheduleDateTime) {
        validateAccess(jobRequest, clientId);
        validatePayload(jobRequest, scheduleDateTime);
        //validateUpdateJobPayload(jobRequest);
    }

    public void validatePatchJob(JobStatusRequest jobStatusRequest) {
        // only ADMIN and SUPERVISOR can create boxtruck/van
        /*if (!(AuthDetailsProvider.isRolePresent(DRIVER) ||
              AuthDetailsProvider.isRolePresent(SPOTTER))) {
            throw new ServiceException(ErrorCode.BOXTRUCK_JOB_UPDATE_ACCESS);
        }*/
    }
}
