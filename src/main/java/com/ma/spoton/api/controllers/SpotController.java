package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.MATERIAL_HANDLER;
import static com.ma.spoton.api.constants.SystemRoles.PLANNING;
import static com.ma.spoton.api.constants.SystemRoles.SECURITY;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.TEAM_LEAD;
import static com.ma.spoton.api.constants.SystemRoles.TRAFFIC;

import java.util.List;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.FleetStatusCountDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.SpotRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.SpotService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Locations APIs")
@RestController
@RequestMapping("/v1/clients/{clientId}/spots")
public class SpotController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpotController.class);
    @Autowired
    private SpotService spotService;

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + TEAM_LEAD + "', '" + PLANNING + "', '" + TRAFFIC
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to create new Spot", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createSpot(@PathVariable String clientId,
                                           @Valid @RequestBody SpotRequest spotRequest) {
        long start = System.currentTimeMillis();
        spotService.createSpot(clientId, spotRequest);
        LoggerUtil.logSLA(LOGGER, "createSpot", start, "createSpot completed");
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + TEAM_LEAD + "', '" + PLANNING + "', '" + TRAFFIC
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to update Spot", code = 204,
            consumes = "application/json")
    @PutMapping("/{spotId}")
    public ResponseEntity<Void> updateSpot(@PathVariable String clientId, @PathVariable String spotId,
                                           @Valid @RequestBody SpotRequest spotRequest) {
        long start = System.currentTimeMillis();
        spotService.updateSpot(clientId, spotId, spotRequest);
        LoggerUtil.logSLA(LOGGER, "updateSpot", start, "updateSpot completed");
        try {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating spot! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_SPOT_NAME);
        }
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + TEAM_LEAD + "', '" + PLANNING + "', '" + TRAFFIC
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to delete Spot", code = 204,
            consumes = "application/json")
    @DeleteMapping("/{spotId}")
    public ResponseEntity<Void> deleteSpot(@PathVariable String clientId,
                                           @PathVariable String spotId) {
        long start = System.currentTimeMillis();
        spotService.deleteSpot(clientId, spotId);
        LoggerUtil.logSLA(LOGGER, "deleteSpot", start, "deleteSpot completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + TEAM_LEAD + "', '" + PLANNING + "', '" + TRAFFIC
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation("This API is used to get Spot by ID")
    @GetMapping("/{spotId}")
    public ResponseEntity<SpotDto> getSpot(@PathVariable String clientId,
                                           @PathVariable String spotId) {
        long start = System.currentTimeMillis();
        SpotDto resource = spotService.getSpot(clientId, spotId, AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "getSpot", start, "getSpot completed");
        return ResponseEntity
                .ok(resource);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + SECURITY + "', '" + TEAM_LEAD + "', '" + PLANNING
                  + "', '" + TRAFFIC + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation("This API is used to get paginated list of Spots")
    @GetMapping
    public ResponseEntity<PagedResponse<SpotDto>> getSpots(@PathVariable String clientId,
                                                           @RequestParam(required = false) String locationIds,
                                                           @RequestParam(required = false) String lastUpdated,
                                                           @RequestParam(required = false) String fleet_status,
                                                           @QuerydslPredicate(root = Spot.class) Predicate predicate,
                                                           Pageable pageable) {

        long start = System.currentTimeMillis();
        PagedResponse<SpotDto> response =
                spotService.getSpots(clientId, predicate, pageable, AuthDetailsProvider.getTimeZone(), locationIds,
                                     lastUpdated, fleet_status);

        LoggerUtil.logSLA(LOGGER, "getSpots", start, "getSpots completed");
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + SECURITY + "', '" + TEAM_LEAD + "', '" + PLANNING
                  + "', '" + TRAFFIC + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation("This API is used to get fleet status count")
    @GetMapping("/fleetStatus")
    public ResponseEntity<List<FleetStatusCountDto>> getFleetStatus(@PathVariable String clientId,
                                                                    @RequestParam(required = false) String locationId) {
        long start = System.currentTimeMillis();
        List<FleetStatusCountDto> response = spotService.countOfFleetStatus(clientId, locationId);
        LoggerUtil.logSLA(LOGGER, "getFleetStatus", start, "getFleetStatus completed");
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + SECURITY + "', '" + TEAM_LEAD + "', '" + PLANNING
                  + "', '" + TRAFFIC + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation("This API is used to get paginated list of Spots for dropdown")
    @GetMapping("/dropdown")
    public ResponseEntity<PagedResponse<SpotDto>> getDropdownSpots(@PathVariable String clientId,
                                                                   @RequestParam(required = false) String fleet_status,
                                                                   @QuerydslPredicate(root = Spot.class) Predicate predicate,
                                                                   Pageable pageable) {
        long start = System.currentTimeMillis();
        PagedResponse<SpotDto> resource =
                spotService.getDropdownSpots(clientId, predicate, pageable, AuthDetailsProvider.getTimeZone(),
                                             fleet_status);
        LoggerUtil.logSLA(LOGGER, "getDropdownSpots", start, "getDropdownSpots completed");
        return ResponseEntity.ok(resource);
    }

    @ApiOperation("This API is used to export list of Spots as CSV")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + SECURITY + "', '" + TEAM_LEAD + "', '" + PLANNING
                  + "', '" + TRAFFIC + "', '" + MATERIAL_HANDLER + "')")
    @GetMapping("/export/csv")
    public ResponseEntity<Resource> exportSpotsAsCsv(@PathVariable String clientId,
                                                     @QuerydslPredicate(root = Location.class) Predicate predicate) {
        long start = System.currentTimeMillis();
        Resource resource =
                spotService.exportSpotsAsCSV(clientId, predicate, AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportSpotsAsCsv", start, "exportSpotsAsCsv completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Spots.csv")
                .contentType(MediaType.parseMediaType("text/csv")).body(resource);
    }

    @ApiOperation("This API is used to export list of Spots as EXCEL")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + DRIVER + "', '" + SUPERVISOR
                  + "', '" + SPOTTER + "', '" + GUARD + "', '" + SECURITY + "', '" + TEAM_LEAD + "', '" + PLANNING
                  + "', '" + TRAFFIC + "', '" + MATERIAL_HANDLER + "')")
    @GetMapping("/export/excel")
    public ResponseEntity<Resource> exportSpotsAsExcel(@PathVariable String clientId,
                                                       @QuerydslPredicate(root = Location.class) Predicate predicate) {
        long start = System.currentTimeMillis();
        Resource resource =
                spotService.exportSpotsAsEXCEL(clientId, predicate, AuthDetailsProvider.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportSpotsAsExcel", start, "exportSpotsAsExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Spots_Report.xlsx")
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }


    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + TEAM_LEAD + "', '" + PLANNING + "', '" + TRAFFIC
                  + "', '" + MATERIAL_HANDLER + "')")
    @ApiOperation(value = "This API is used to activate Spot status", code = 204,
            consumes = "application/json")
    @PutMapping("/{spotId}/activate")
    public ResponseEntity<Void> activateSpot(@PathVariable String clientId, @PathVariable String spotId) {
        long start = System.currentTimeMillis();
        spotService.activateSpot(clientId, spotId);
        LoggerUtil.logSLA(LOGGER, "activateSpot", start, "activateSpot completed");
        try {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating spot! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_SPOT_NAME);
        }
    }
}
