package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.MATERIAL_HANDLER;
import static com.ma.spoton.api.constants.SystemRoles.PLANNING;
import static com.ma.spoton.api.constants.SystemRoles.SECURITY;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.TEAM_LEAD;
import static com.ma.spoton.api.constants.SystemRoles.TRAFFIC;

import java.net.URI;
import java.util.List;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SuppliersDto;
import com.ma.spoton.api.entities.Suppliers;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.SuppliersRequest;
import com.ma.spoton.api.services.SuppliersService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Supplier APIs")
@RestController
@RequestMapping("/v1/supplier")
public class SuppliersController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SuppliersController.class);
    @Value("${application.api-base-url}")
    private String apiBaseUrl;
    @Value("${application.version}")
    private String version;
    @Autowired
    private SuppliersService supplierService;

    @Secured({IT, ADMIN, CLIENT, SUPERVISOR, TEAM_LEAD, PLANNING, MATERIAL_HANDLER, TRAFFIC, GUARD, SECURITY})
    @ApiOperation(value = "This API is used to create new Carrier", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<SuppliersDto> createSupplier(@Valid @RequestBody SuppliersRequest supplierRequest) {
        long start = System.currentTimeMillis();
        SuppliersDto supplier = supplierService.createSupplier(supplierRequest);
        LoggerUtil.logSLA(LOGGER, "createSupplier", start, "createSupplier completed");
        return ResponseEntity
                .created(
                        URI.create(String.format("%s/%s/suppliers/%s", apiBaseUrl, version, supplier.getSupplierId())))
                .body(supplier);
    }

    @ApiOperation("This API is used to get suppliers")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<PagedResponse<SuppliersDto>> getSuppliers(
            @QuerydslPredicate(root = Suppliers.class) Predicate predicate, Pageable pageable,
            @RequestParam(required = false) String clientId) {
        long start = System.currentTimeMillis();
        PagedResponse<SuppliersDto> resource = supplierService.getSuppliers(predicate, pageable, clientId);
        LoggerUtil.logSLA(LOGGER, "getSuppliers", start, "getSuppliers completed");
        return ResponseEntity.ok(resource);
    }

    @ApiOperation("This API is used to get suppliers")
    @GetMapping("/supplierList")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<SuppliersDto>> getAllSuppliers(
            @QuerydslPredicate(root = Suppliers.class) Predicate predicate,
            @RequestParam(required = false) String suppliers, @RequestParam(required = false) String clientId) {
        long start = System.currentTimeMillis();
        List<SuppliersDto> resource = supplierService.getAllSuppliersList(predicate, suppliers, clientId);
        LoggerUtil.logSLA(LOGGER, "getSuppliers", start, "getSuppliers completed");
        return ResponseEntity.ok(resource);
    }

    @ApiOperation("This API is used to delete Supplier")
    @DeleteMapping("/{supplierId}")
    @Secured({IT, ADMIN, CLIENT, SUPERVISOR, TEAM_LEAD, PLANNING, MATERIAL_HANDLER, TRAFFIC})
    public ResponseEntity<Void> deleteFleet(@PathVariable String supplierId) {
        long start = System.currentTimeMillis();
        supplierService.deleteSupplier(supplierId);
        LoggerUtil.logSLA(LOGGER, "deleteSupplier", start, "deleteSupplier completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @ApiOperation("This API is used to update Supplier")
    @PutMapping("/{supplierId}")
    public ResponseEntity<Void> updateCarrier(@PathVariable String supplierId,
                                              @Valid @RequestBody SuppliersRequest supplierRequest) {
        try {
            long start = System.currentTimeMillis();
            supplierService.updateSupplier(supplierId, supplierRequest);
            LoggerUtil.logSLA(LOGGER, "updateSupplier", start, "updateSupplier completed");
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while updating supplier! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_SUPPLIER);
        }
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
