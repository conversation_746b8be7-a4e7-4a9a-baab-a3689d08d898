package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.MATERIAL_HANDLER;
import static com.ma.spoton.api.constants.SystemRoles.PLANNING;
import static com.ma.spoton.api.constants.SystemRoles.SECURITY;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.TEAM_LEAD;
import static com.ma.spoton.api.constants.SystemRoles.TRAFFIC;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.constants.ConfigurableType;
import com.ma.spoton.api.dtos.ConfigurableEntityDTO;
import com.ma.spoton.api.dtos.ConfigurationResponseDTO;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.requests.ConfigurationSaveRequest;
import com.ma.spoton.api.services.ConfigurableTypeService;
import com.ma.spoton.api.services.ConfigurableTypesFactory;
import com.ma.spoton.api.services.FleetStatusMigrateService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@Api("Configurations APIs")
@RequestMapping("v1/configuration")
public class ConfigurationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigurationController.class);

    @Autowired
    private ConfigurableTypesFactory factory;

    @Autowired
    private FleetStatusMigrateService fleetStatusMigratorService;

    @ApiOperation("This API is used to get configurable types")
    @Secured({IT, ADMIN})
    @GetMapping("/types")
    public ResponseEntity<List<String>> getConfigurableTypes() {
        return ResponseEntity.ok(Arrays.stream(ConfigurableType.values())
                                         .map(Enum::name)
                                         .collect(Collectors.toList()));
    }

    @ApiOperation("This API is used to get all values of configurable Types for a given client and given type")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "', '" + TRAFFIC + "', '" + SECURITY + "', '" + TEAM_LEAD
                  + "', '" + PLANNING + "', '" + MATERIAL_HANDLER + "')")
    @GetMapping
    public ResponseEntity<ConfigurationResponseDTO<?>> getValues(
            @RequestParam ConfigurableType type, @RequestParam(required = false) String clientId) {
        long start = System.currentTimeMillis();

        ConfigurableTypeService<?> service = factory.getService(type);

        ConfigurationResponseDTO<?> responseDTO = service.getValues(clientId);

        LoggerUtil.logSLA(LOGGER, "getValues", start, "getValues completed");
        return ResponseEntity.ok().body(responseDTO);
    }

    @ApiOperation("This API is used to get all values of configurable Types of all clients and given type")
    @Secured({IT, ADMIN})
    @GetMapping("/clients")
    public ResponseEntity<PagedResponse<ConfigurationResponseDTO<ConfigurableEntityDTO>>> getAllClientValues(
            @RequestParam ConfigurableType type, Pageable pageable) {
        long start = System.currentTimeMillis();

        ConfigurableTypeService<?> service = factory.getService(type);

        PagedResponse<ConfigurationResponseDTO<ConfigurableEntityDTO>> responseDTO =
                service.getAllClientValues(pageable);

        LoggerUtil.logSLA(LOGGER, "getAllClientValues", start, "getAllClientValues completed");
        return ResponseEntity.ok().body(responseDTO);
    }

    @ApiOperation("This API is used to delete all values of configurable Types for a given client and given type")
    @Secured({IT, ADMIN})
    @DeleteMapping
    public ResponseEntity<Void> clearValues(@RequestParam ConfigurableType type, @RequestParam String clientId) {
        long start = System.currentTimeMillis();

        ConfigurableTypeService<?> service = factory.getService(type);
        service.clearValues(clientId);

        LoggerUtil.logSLA(LOGGER, "clearValues", start, "clearValues completed");
        return ResponseEntity.noContent().build();
    }

    @ApiOperation("This API is used to save values of configurable Types for a given client and given type")
    @Secured({IT, ADMIN})
    @PostMapping
    public ResponseEntity<Void> saveValues(@RequestBody ConfigurationSaveRequest saveRequest) {
        long start = System.currentTimeMillis();

        ConfigurableTypeService<?> service = factory.getService(saveRequest.getType());
        service.saveValues(saveRequest);

        LoggerUtil.logSLA(LOGGER, "updateValues", start, "updateValues completed");
        return ResponseEntity.accepted().build();
    }

    @ApiOperation("This API is used to save values of configurable Types for a given client and given type")
    @Secured({IT, ADMIN})
    @PostMapping("/migrate")
    public ResponseEntity<String> runMigrationFor(@RequestParam String tableName) {
        long start = System.currentTimeMillis();

        try {
            fleetStatusMigratorService.migrate(tableName);

            LoggerUtil.logSLA(LOGGER, "runMigrationFor", start, "runMigrationFor completed");
            return ResponseEntity.ok("Migration completed successfully.");
        } catch (Exception e) {
            LoggerUtil.logSLA(LOGGER, "runMigrationFor", start, "runMigrationFor failed");
            return ResponseEntity.internalServerError().body("Migration failed because of " + e.getMessage());
        }
    }

}
