package com.ma.spoton.api.controllers;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.DockTurnAroundTimeDTO;
import com.ma.spoton.api.services.DockTurnAroundTimeService;
import com.ma.spoton.api.utils.LoggerUtil;

@RestController
@RequestMapping("/api/turn-around-time")
public class TurnAroundTimeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TurnAroundTimeController.class);
    @Autowired
    private DockTurnAroundTimeService dockTurnAroundTimeService;

    @GetMapping("/client/{clientId}")
    public ResponseEntity<List<DockTurnAroundTimeDTO>> getAverageTurnAroundTime(@PathVariable String clientId,
                                                                                @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {
        LOGGER.info(">> getAverageTurnAroundTime : ClientId  , isRefresh : ({}, {})", clientId, isRefresh);
        long start = System.currentTimeMillis();
        List<DockTurnAroundTimeDTO> turnAroundTimes =
                dockTurnAroundTimeService.calculateAverageTurnAroundTime(clientId, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getAverageTurnAroundTime", start, "getAverageTurnAroundTime completed");
        return ResponseEntity.ok(turnAroundTimes);
    }
}
