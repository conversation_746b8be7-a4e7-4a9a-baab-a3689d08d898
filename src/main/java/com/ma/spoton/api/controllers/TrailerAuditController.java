package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.MATERIAL_HANDLER;
import static com.ma.spoton.api.constants.SystemRoles.PLANNING;
import static com.ma.spoton.api.constants.SystemRoles.SECURITY;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.TEAM_LEAD;
import static com.ma.spoton.api.constants.SystemRoles.TRAFFIC;

import java.net.URI;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.requests.TrailerAuditReportExportRequest;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.TrailerAuditService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Trailer Audit api")
@RestController
@RequestMapping("/v1/trailerAudit")
public class TrailerAuditController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TrailerAuditController.class);
    @Value("${application.api-base-url}")
    private String apiBaseUrl;
    @Value("${application.version}")
    private String version;
    @Autowired
    private TrailerAuditService trailerAuditService;

    @Secured({SUPERVISOR, CLIENT, ADMIN, IT, DRIVER, SPOTTER, GUARD, SECURITY, TEAM_LEAD, PLANNING, TRAFFIC,
              MATERIAL_HANDLER})
    @ApiOperation(value = "This API is used to create new Trailer Audit", code = 201,
            consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createTrailerAudit(@Valid @RequestBody TrailerAuditRequest[] trailerAuditRequests) {

        long start = System.currentTimeMillis();
        trailerAuditService.createTrailerAudit(trailerAuditRequests);
        LoggerUtil.logSLA(LOGGER, "createTrailerAudit", start, "createTrailerAudit completed");
        return ResponseEntity
                .created(URI.create(String.format("%s/%s/jobs", apiBaseUrl, version)))
                .build();
    }

    @Secured({SUPERVISOR, CLIENT, ADMIN, IT, DRIVER, SPOTTER, GUARD, SECURITY, TEAM_LEAD, PLANNING, TRAFFIC,
              MATERIAL_HANDLER})
    @PatchMapping("/{spotId}")
    public ResponseEntity<Void> clearSpot(@PathVariable String spotId) {

        trailerAuditService.clearSpot(spotId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    @ApiOperation("This API is used to export list of Trailer Audit as EXCEL")
    @Secured({CLIENT, SUPERVISOR, IT, ADMIN, TRAFFIC, TEAM_LEAD, PLANNING, MATERIAL_HANDLER})
    @PostMapping("/export/excel")
    public ResponseEntity<Resource> exportTrailerAuditsAsExcel(
            @QuerydslPredicate(root = Spot.class) Predicate predicate,
            @RequestBody TrailerAuditReportExportRequest exportRequest) {

        long start = System.currentTimeMillis();
        UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
        Resource resource = trailerAuditService.exportTrailerAuditAsEXCEL(exportRequest, predicate,
                                                                          loggedInUser.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportTrailerAuditAsExcel", start, "exportTrailerAuditAsExcel completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Trailer_Audit_Report.xlsx")
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }

    @ApiOperation("This API is used to export list of Trailer Audit as EXCEL")
    @Secured({CLIENT, SUPERVISOR, IT, ADMIN, TRAFFIC, TEAM_LEAD, PLANNING, MATERIAL_HANDLER})
    @PostMapping("/export/pdf")
    public ResponseEntity<Resource> exportTrailerAuditsAsPdf(@QuerydslPredicate(root = Spot.class) Predicate predicate,
    		@RequestBody TrailerAuditReportExportRequest exportRequest) {

        long start = System.currentTimeMillis();
        UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
        Resource resource = trailerAuditService.exportTrailerAuditAsPdf(exportRequest, predicate,
                                                                        loggedInUser.getTimeZone());
        LoggerUtil.logSLA(LOGGER, "exportTrailerAuditAsPdf", start, "exportTrailerAuditAsPdf completed");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Trailer_Audit_Report.pdf")
                .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
    }
}
