package com.ma.spoton.api.controllers;

import java.net.URI;
import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.UserAvailabilityExceptionDto;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.requests.UserAvailbilityExceptionRequest;
import com.ma.spoton.api.services.UserAvailabilityExceptionService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("UserAvailabilityException api")
@RestController
@RequestMapping("/v1/userAvailabilityException")
public class UserAvailabilityExceptionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAvailabilityExceptionController.class);
    @Value("${application.api-base-url}")
    private String apiBaseUrl;
    @Value("${application.version}")
    private String version;
    @Autowired
    private UserAvailabilityExceptionService userAvailabilityExceptionService;

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to create new User Availability Exception", code = 201, consumes = "application/json")
    @PostMapping
    public ResponseEntity<Void> createUserAvailabilityException(
            @Valid @RequestBody UserAvailbilityExceptionRequest userAvailabilityExceptionRequest) {
        long start = System.currentTimeMillis();
        UserAvailabilityException userAvailabilityException =
                userAvailabilityExceptionService.addUserAvailabilityException(userAvailabilityExceptionRequest);
        LoggerUtil.logSLA(LOGGER, "createUserAvailabilityException", start,
                          "createUserAvailabilityException completed");
        return ResponseEntity
                .created(URI.create(
                        String.format("%s/%s/jobs/%s", apiBaseUrl, version, userAvailabilityException.getUuid())))
                .build();
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation("This API is used to get User Availability Exception by ID")
    @GetMapping("/{userId}")
    public ResponseEntity<List<UserAvailabilityExceptionDto>> getUserAvailabilityException(@PathVariable String userId,
                                                                                           @QuerydslPredicate(root = UserAvailability.class) Predicate predicate,
                                                                                           Pageable pageable) {
        long start = System.currentTimeMillis();
        List<UserAvailabilityExceptionDto> resource = userAvailabilityExceptionService
                .getUserAvailabilityException(userId);
        LoggerUtil.logSLA(LOGGER, "getUserAvailabilityException", start, "getUserAvailabilityException completed");
        return ResponseEntity.ok(resource);
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "This API is used to delete user availability", code = 204)
    @DeleteMapping("/{userAvailabilityExceptionId}")
    public ResponseEntity<Void> deleteUserAvailabilityException(@PathVariable String userAvailabilityExceptionId) {
        long start = System.currentTimeMillis();
        userAvailabilityExceptionService.deleteUserAvailabilityException(userAvailabilityExceptionId);
        LoggerUtil.logSLA(LOGGER, "deleteUserAvailabilityException", start,
                          "deleteUserAvailabilityException completed");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}




