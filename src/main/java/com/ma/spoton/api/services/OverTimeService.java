package com.ma.spoton.api.services;

import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.dtos.OverTimeDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.querydsl.core.types.Predicate;

public interface OverTimeService {

void addOvertimeForUser(String userId);
	
	void deleteOverTimeForUser(String userId);
	
	PagedResponse<OverTimeDto> getOverTimeUsers(String timeZone,Predicate predicate, Pageable pageable);
	
	void deleteExpiredOverTimeUsers();
}
