package com.ma.spoton.api.services;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import com.ma.spoton.api.dtos.LocationDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.requests.FleetEntryExitRequest;
import com.ma.spoton.api.requests.LocationRequest;
import com.querydsl.core.types.Predicate;

public interface LocationService {

  void createLocation(String clientId, LocationRequest locationRequest);

  void updateLocation(String clientId, String locationId, LocationRequest locationRequest);

  void deleteLocation(String clientId, String locationId);

  LocationDto getLocation(String clientId, String locationId, String timeZone);

  PagedResponse<LocationDto> getLocations(String clientId, Predicate predicate, Pageable pageable,
      String timeZone, String assignedUserId);

  void createFleetEntryExit(String clientId, String locationId,
      FleetEntryExitRequest fleetEntryExitRequest, String userId, boolean sendNotification);

  Resource exportLocationsAsCSV(String clientId, Predicate predicate, String timeZone);

  Resource exportLocationsAsEXCEL(String clientId, Predicate predicate, String timeZone);
  
  
  void makeLocationDefault(String clientId, String locationId);

  void deleteGuardEntryExit(String guardEntryExitId);
  
  void DeleteLocationImage(String clientId, String locationId);

}
