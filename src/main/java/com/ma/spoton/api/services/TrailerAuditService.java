package com.ma.spoton.api.services;

import org.springframework.core.io.Resource;

import com.ma.spoton.api.requests.TrailerAuditReportExportRequest;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.querydsl.core.types.Predicate;

public interface TrailerAuditService {

    void createTrailerAudit(TrailerAuditRequest[] trailerAuditRequests);

    void clearSpot(String spotId);

    Resource exportTrailerAuditAsEXCEL(TrailerAuditReportExportRequest exportRequest, Predicate predicate,
                                       String timeZone);

    Resource exportTrailerAuditAsPdf(TrailerAuditReportExportRequest exportRequest, Predicate predicate,
                                     String timeZone);

}
