package com.ma.spoton.api.services;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ma.spoton.api.constants.ConfigurableType;

@Component
public class ConfigurableTypesFactory {

    private final Map<ConfigurableType, ConfigurableTypeService<?>> services = new HashMap<>();

    // register
    public ConfigurableTypesFactory(List<ConfigurableTypeService<?>> serviceList) {
        serviceList.forEach(s -> services.put(s.getType(), s));
    }

    public ConfigurableTypeService<?> getService(ConfigurableType type) {
        return services.get(type);
    }
}
