package com.ma.spoton.api.services;

import static com.ma.spoton.api.repositories.DashboardRepository.InTransitSpot;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.cache.dashboard.AverageMovesCache;
import com.ma.spoton.api.cache.dashboard.HourlyAverageMovesCache;
import com.ma.spoton.api.cache.dashboard.InTransitSpotsCache;
import com.ma.spoton.api.cache.dashboard.SpotCountsCache;
import com.ma.spoton.api.cache.dashboard.WeeklyTotalSpotsCache;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.DayStats;
import com.ma.spoton.api.dtos.HourStats;
import com.ma.spoton.api.dtos.InTransitSpots;
import com.ma.spoton.api.dtos.MoveStats;
import com.ma.spoton.api.dtos.SpotCountsDTO;
import com.ma.spoton.api.dtos.SpotIdNameDTO;
import com.ma.spoton.api.dtos.WeeklyTotalSpotsDTO;
import com.ma.spoton.api.mappers.SpotMapper;
import com.ma.spoton.api.mappers.WeeklyTotalSpotsMapper;
import com.ma.spoton.api.repositories.DashboardRepository;
import com.ma.spoton.api.utils.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    // Thread-safe cache using ConcurrentHashMap
    private static final ConcurrentHashMap<String, AverageMovesCache> avgMoveCacheMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, HourlyAverageMovesCache> hourlyMoveCacheMap =
            new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, InTransitSpotsCache> inTransitSpotsCacheMap =
            new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, WeeklyTotalSpotsCache> weeklyTotalSpotsCacheMap =
            new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, List<SpotIdNameDTO>> allSpotsOfLocationCacheMap =
            new ConcurrentHashMap<String, List<com.ma.spoton.api.dtos.SpotIdNameDTO>>();
    private static final ConcurrentHashMap<String, SpotCountsCache> spotCountsCacheMap = new ConcurrentHashMap<>();

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(BusinessConstants.FORM_DATE_FORMAT);

    @Autowired
    private DashboardRepository dashboardRepository;
    @Autowired
    private SpotMapper spotMapper;
    @Autowired
    private WeeklyTotalSpotsMapper weeklyTotalSpotsMapper;

    public static void clearSpotCountsCache() {
        spotCountsCacheMap.clear();
    }

    public static void clearWeeklyTotalSpotsCache() {
        weeklyTotalSpotsCacheMap.clear();
    }

    @Override
    @Transactional(readOnly = true)
    public DayStats getHourlyAvgMoves(String clientId,
                                      String timeZone,
                                      Boolean refreshCache) {
        return getHourlyAvgForWeek(clientId, timeZone, 7, refreshCache);
    }

    public DayStats getHourlyAvgForWeek(String clientId,
                                        String timeZone,
                                        int numberOfDays,
                                        boolean refreshCache) {
        DayStats dashboardStats = new DayStats();
        Map<String, HourStats> dayMap = new LinkedHashMap<>();  //day -> {hour -> {value -> color code}}
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDateTime today = LocalDateTime.now(zoneId);
        String userDate = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.debug("Executing hourly average moves for the client:{}", clientId);
        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, timeZone);
        if (!refreshCache) {
            HourlyAverageMovesCache cache = hourlyMoveCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId, timeZone)) {
                log.debug("{} loading from cache", this.getClass().getName());
                return cache.getStats();
            }
        }
        List<DashboardRepository.HourlyAvgMoves> avgStats =
                dashboardRepository.getDailyHourlyAvgMoves(clientId, userDate, numberOfDays, timeZone);
        for (DashboardRepository.HourlyAvgMoves moves : avgStats) {
            int movePercent = 0;
            String day = moves.getDropDay();
            String hour = moves.getDropHour();
            int dailyJobCount = moves.getDailyJobCount();
            int totalJobsPerDay = moves.getTotalJobsPerDay();
            // Compute the move percentage
            if (totalJobsPerDay > 0) {
                double percent = (double) dailyJobCount / totalJobsPerDay * 100;
                movePercent = (int) Math.round(percent);
            }
            String colorCode = getColorCode(movePercent);

            // Create or retrieve the DayStats object for the current day
            HourStats hourStats = dayMap.computeIfAbsent(day, k -> new HourStats());

            // Retrieve or initialize the hourly map for this day
            Map<String, MoveStats> hourMap = hourStats.getStatsByHour();
            if (hourMap == null) {
                hourMap = new LinkedHashMap<>();
                hourStats.setStatsByHour(hourMap);
            }
            // Create MoveStats and set color mapping
            Map<Integer, String> stats = new LinkedHashMap<>();
            stats.put(moves.getDailyJobCount(), colorCode);
            MoveStats moveStats = new MoveStats();
            moveStats.setAvgMoves(stats);
            hourMap.put(hour, moveStats);
        }
        dashboardStats.setStatsByDay(dayMap);
        log.debug("Success in identifying the hourly average moves for 7 days");
        hourlyMoveCacheMap.put(cacheKey, new HourlyAverageMovesCache(clientId, timeZone, dashboardStats));
        return dashboardStats;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Integer> getAvgMovesOfClient(String clientId,
                                                    String timeZone,
                                                    Boolean refreshCache) {
        return getThreeWeeksAvg(clientId, timeZone, 3, refreshCache);
    }

    public Map<String, Integer> getThreeWeeksAvg(String clientId,
                                                 String timeZone,
                                                 int numberOfWeeks,
                                                 boolean refreshCache) {
        Map<String, Integer> result = new HashMap<>();
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate today = LocalDate.now(zoneId);
        String day = today.getDayOfWeek().toString();

        //Initializing the map
        result.put(day, 0);

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, timeZone);
        if (!refreshCache) {
            AverageMovesCache cache = avgMoveCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId, timeZone)) {
                log.debug("{} loading from cache- {}", this.getClass().getName(), cache.getAvgMoves());
                return cache.getAvgMoves();
            }
        }

        List<DashboardRepository.DailyAvgMoves> dailyAverage =
                dashboardRepository.getDailyAverage(numberOfWeeks, clientId);
        for (DashboardRepository.DailyAvgMoves moves : dailyAverage) {
            if (moves.getWeekDay().equalsIgnoreCase(today.getDayOfWeek().toString())) {
                result.put(day, moves.getAvgMoves());
                break;
            }
        }

        log.debug("Success in identifying the average moves - {}", result);
        avgMoveCacheMap.put(cacheKey, new AverageMovesCache(clientId, timeZone, result));

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public InTransitSpots getInTransitSpots(String clientId, String timeZone, Boolean refreshCache) {

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, timeZone);
        if (!refreshCache) {
            InTransitSpotsCache cache = inTransitSpotsCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId, timeZone)) {
                log.debug("{} loading from cache- {}", this.getClass().getName(), cache.getSpots());
                return cache.getSpots();
            }
        }

        List<InTransitSpot> spots = dashboardRepository.getInTransitSpots(clientId, timeZone);
        InTransitSpots inTransitSpots = new InTransitSpots();
        inTransitSpots.getInTransitSpots().addAll(spotMapper.mapToInTransitSpotDtos(spots, timeZone));
        inTransitSpotsCacheMap.put(cacheKey, new InTransitSpotsCache(clientId, timeZone, inTransitSpots));

        return inTransitSpots;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public WeeklyTotalSpotsDTO getWeeklyTotalSpots(String clientId, String timeZone, String weekStart,
                                                   Boolean refreshCache) {
        Map<String, String> range;
        if (StringUtils.isBlank(weekStart)) {
            range = DateTimeUtils.getPreviousWeekUtcRange(timeZone);
        } else {
            range = DateTimeUtils.getProvidedWeekUtcRange(weekStart, timeZone);
        }
        return getTotalCompletedSpotsWithDateRange(clientId, timeZone, range, refreshCache);
    }

    public WeeklyTotalSpotsDTO getTotalCompletedSpotsWithDateRange(String clientId, String timeZone,
                                                                   Map<String, String> range, Boolean refreshCache) {

        if (MapUtils.isEmpty(range)) {
            return new WeeklyTotalSpotsDTO(); // better approach to throw exception. but this is a safe check.
        }
        String weekStartKey = range.get("startUtc");

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, timeZone);
        WeeklyTotalSpotsCache cache = weeklyTotalSpotsCacheMap.get(cacheKey);
        if (!refreshCache && cache != null && cache.isValid(clientId, timeZone) &&
            cache.getWeeklyTotalSpotsMap().containsKey(weekStartKey)) {
            WeeklyTotalSpotsDTO weeklyTotalSpotsDTO = cache.getWeeklyTotalSpotsMap().get(weekStartKey);
            log.debug("{} loading from cache- {}", this.getClass().getName(), weeklyTotalSpotsDTO);
            return weeklyTotalSpotsDTO;
        }

        WeeklyTotalSpotsDTO weeklyTotalSpotsDTO = weeklyTotalSpotsMapper.mapToDTO(
                dashboardRepository.getWeeklyTotalSpots(clientId, timeZone,
                                                        range.get("startUtc"), range.get("endUtc")));
        // first time for a client.
        if (cache == null) {
            weeklyTotalSpotsCacheMap.put(cacheKey,
                                         new WeeklyTotalSpotsCache(clientId, timeZone, weekStartKey,
                                                                   weeklyTotalSpotsDTO));
        } else {
            // update map of existing cache.
            cache.getWeeklyTotalSpotsMap().put(weekStartKey, weeklyTotalSpotsDTO);
        }

        return weeklyTotalSpotsDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<SpotIdNameDTO> getAllSpotsOfLocation(String locationId, Boolean refreshCache) {

        if (!refreshCache) {
            List<SpotIdNameDTO> allSpots = allSpotsOfLocationCacheMap.get(locationId);
            if (allSpots != null) {
                log.debug("{} loading from cache- {}", this.getClass().getName(), allSpots);
                return allSpots;
            }
        }

        List<SpotIdNameDTO> allSpots = spotMapper.mapToDto(dashboardRepository.getSpotsByLocation(locationId));
        allSpotsOfLocationCacheMap.put(locationId, allSpots);

        return allSpots;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public SpotCountsDTO getSpotCountsOfDays(String clientId, String timeZone, String spotId,
                                             Integer daysBefore, Boolean refreshCache) {

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, timeZone);
        // Add date as key as the days wont be enough as it wont pick from DB in future dates.
        String date = LocalDateTime.now(ZoneOffset.UTC)
                .atZone(ZoneId.of(timeZone))
                .toLocalDateTime().format(FORMATTER);
        String mapKey = spotId + ":" + date + ":" + daysBefore;
        SpotCountsCache cache = spotCountsCacheMap.get(cacheKey);
        if (!refreshCache && cache != null && cache.isValid(clientId, timeZone) &&
            cache.getSpotCountsMap().containsKey(mapKey)) {
            SpotCountsDTO spotCountsDTO = cache.getSpotCountsMap().get(mapKey);
            log.debug("{} loading from cache- {}", this.getClass().getName(), spotCountsDTO);
            return spotCountsDTO;
        }

        SpotCountsDTO spotCountsDTO = spotMapper.mapToDto(
                dashboardRepository.getSpotCounts(clientId, spotId, timeZone, daysBefore));
        // first time for a client.
        if (cache == null) {
            spotCountsCacheMap.put(cacheKey, new SpotCountsCache(clientId, timeZone, mapKey, spotCountsDTO));
        } else {
            // update map of existing cache.
            cache.getSpotCountsMap().put(mapKey, spotCountsDTO);
        }

        return spotCountsDTO;
    }

    private String getColorCode(int value) {
        if (value <= 1) {
            return "#FF0000";       // red
        }
        if (value <= 3) {
            return "#FF6666";       // light red
        }
        if (value <= 5) {
            return "#FFA500";       // orange
        }
        if (value <= 8) {
            return "#FFFF00";       // yellow
        }
        if (value <= 12) {
            return "#90EE90";      // light green
        }
        return "#008000";                       // green
    }

    private String generateCacheKey(String clientId, String timeZone) {
        return timeZone + "::" + clientId;
    }
}