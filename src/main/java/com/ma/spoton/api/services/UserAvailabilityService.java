package com.ma.spoton.api.services;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAvailabilityDto;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.requests.UserAvailabilityRequest;
import com.querydsl.core.types.Predicate;

import java.util.List;

import org.springframework.data.domain.Pageable;


public interface UserAvailabilityService {

	
	UserAvailability addUserAvailability(UserAvailabilityRequest userAvailabilityRequest);
	
	List<UserAvailabilityDto> getUserAvailability(String userId);
	
	void deleteUserAvailability(String userAvailabilityId);
	
	void activateDeactivateUserAvailability(String userAvailabiltyId, Boolean active);
}
