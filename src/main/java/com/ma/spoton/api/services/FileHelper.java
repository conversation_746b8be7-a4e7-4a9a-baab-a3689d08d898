package com.ma.spoton.api.services;

import static com.ma.spoton.api.utils.FileUtils.convertBase64DataIntoByteArray;
import static org.slf4j.LoggerFactory.getLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import com.ma.spoton.api.utils.FileUtils;

@Component
public class FileHelper {

	private static final Logger LOGGER = getLogger(FileHelper.class);

	@Autowired
	@Qualifier("awsS3FileServiceImpl")
	private FileService fileService;

	public boolean uploadFile(String fileName, String path, String base64Data, String oldFileName) {
		try {
			if (StringUtils.isNotBlank(oldFileName)) {
				fileService.delete(path, oldFileName);
			}
			byte[] fileData = convertBase64DataIntoByteArray(base64Data);
			String contentType = FileUtils.getContentTypeFromBase64(base64Data);
			fileService.upload(fileData, path, fileName, contentType);
			return true;
		} catch (Exception e) {
			LOGGER.error("Error occurred while uploading file to S3", e);
			return false;
		}
	}

	public boolean deleteFile(String path, String fileName) {
		try {
			fileService.delete(path, fileName);
			return true;
		} catch (Exception e) {
			LOGGER.error("Error occurred while deleting file from S3", e);
			return false;
		}
	}

}
