package com.ma.spoton.api.services;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.JobStatisticsDto;
import com.ma.spoton.api.dtos.MessageStatisticsDto;
import com.ma.spoton.api.dtos.TotalStatisticsDto;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.entities.QClient;
import com.ma.spoton.api.entities.QFleet;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.QMessage;
import com.ma.spoton.api.entities.QUser;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.MessageRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

  @Autowired
  private ClientRepository clientRepository;

  @Autowired
  private FleetRepository fleetRepository;

  @Autowired
  private UserRepository userRepository;

  @Autowired
  private JobRepository jobRepository;

  @Autowired
  private MessageRepository messageRepository;

  @Override
  public TotalStatisticsDto getTotals(List<String> clientIds) {
//    log.info(">> getTotals({})", clientIds);
    BooleanExpression truckPredicate =
        QFleet.fleet.type.eq(Type.TRUCK).and(QFleet.fleet.isActive.eq(true));
    BooleanExpression trailerPredicate =
        QFleet.fleet.type.eq(Type.TRAILER).and(QFleet.fleet.isActive.eq(true));
    BooleanExpression userPredicate = QUser.user.isActive.eq(true);
    if (CollectionUtils.isNotEmpty(clientIds)) {
      truckPredicate = truckPredicate.and(QFleet.fleet.clients.any().uuid.in(clientIds));
      trailerPredicate = trailerPredicate.and(QFleet.fleet.clients.any().uuid.in(clientIds));
      userPredicate = userPredicate.and(QUser.user.clients.any().uuid.in(clientIds));
    }
    return TotalStatisticsDto.builder()
        .totalClients(CollectionUtils.isEmpty(clientIds)
            ? clientRepository.count(QClient.client.isActive.eq(true))
            : clientIds.size())
        .totalTrucks(fleetRepository.count(truckPredicate))
        .totalTrailers(fleetRepository.count(trailerPredicate))
        .totalUsers(userRepository.count(userPredicate)).build();
  }

  @Override
  public JobStatisticsDto getJobStatistics(Optional<String> fromDateTimeOptional,
      Optional<String> toDateTimeOptional, String timeZone, List<String> clientIds) {
//    log.info(">> getJobStatistics({}, {}, {}, {})", fromDateTimeOptional, toDateTimeOptional,
//        timeZone, clientIds);
    return JobStatisticsDto.builder()
        .pendingCount(jobRepository.count(createJobStatisticsPredicate(Status.OPEN,
            fromDateTimeOptional, toDateTimeOptional, timeZone, clientIds)))
        .activeCount(jobRepository.count(createJobStatisticsPredicate(Status.IN_TRANSIT,
            fromDateTimeOptional, toDateTimeOptional, timeZone, clientIds)))
        .completedCount(jobRepository.count(createJobStatisticsPredicate(Status.COMPLETED,
            fromDateTimeOptional, toDateTimeOptional, timeZone, clientIds)))
        .build();
  }

  @Override
  public MessageStatisticsDto getMessageStatistics(Optional<String> fromDateTimeOptional,
      Optional<String> toDateTimeOptional, String timeZone, String userId) {
//    log.info(">> getMessageStatistics({}, {}, {}, {})", fromDateTimeOptional, toDateTimeOptional,
//        timeZone, userId);
    return MessageStatisticsDto.builder()
        .newCount(messageRepository
            .count(createMessageStatisticsPredicate(com.ma.spoton.api.entities.Message.Status.NEW,
                fromDateTimeOptional, toDateTimeOptional, timeZone, userId)))
        .readCount(messageRepository
            .count(createMessageStatisticsPredicate(com.ma.spoton.api.entities.Message.Status.READ,
                fromDateTimeOptional, toDateTimeOptional, timeZone, userId)))
        .build();
  }

  private Predicate createJobStatisticsPredicate(Status jobStatus,
      Optional<String> fromDateTimeOptional, Optional<String> toDateTimeOptional, String timeZone,
      List<String> clientIds) {
    BooleanExpression predicate = QJob.job.status.eq(jobStatus);
    if (fromDateTimeOptional.isPresent() && toDateTimeOptional.isPresent()) {
      
    	
    	
      ZonedDateTime fromDateTime = DateTimeUtils.convertStringToZonedDateTime(
          fromDateTimeOptional.get(), BusinessConstants.FORM_DATE_TIME_FORMAT, timeZone);
      ZonedDateTime toDateTime = DateTimeUtils.convertStringToZonedDateTime(
          toDateTimeOptional.get(), BusinessConstants.FORM_DATE_TIME_FORMAT, timeZone);
      predicate = predicate.and(QJob.job.createdDate.between(fromDateTime, toDateTime));
      
    }
    if (!clientIds.isEmpty()) {
      predicate = predicate.and(QJob.job.pickupLocation.client.uuid.in(clientIds));
    }
    return predicate;
  }

  private Predicate createMessageStatisticsPredicate(
      com.ma.spoton.api.entities.Message.Status messageStatus,
      Optional<String> fromDateTimeOptional, Optional<String> toDateTimeOptional, String timeZone,
      String userId) {
    BooleanExpression predicate = QMessage.message.status.eq(messageStatus);
    if (fromDateTimeOptional.isPresent() && toDateTimeOptional.isPresent()) {
      ZonedDateTime fromDateTime = DateTimeUtils.convertStringToZonedDateTime(
          fromDateTimeOptional.get(), BusinessConstants.FORM_DATE_TIME_FORMAT, timeZone);
      ZonedDateTime toDateTime = DateTimeUtils.convertStringToZonedDateTime(
          toDateTimeOptional.get(), BusinessConstants.FORM_DATE_TIME_FORMAT, timeZone);
      predicate = predicate.and(QMessage.message.createdDate.between(fromDateTime, toDateTime));
    }
    if (StringUtils.isNotBlank(userId)) {
      predicate = predicate.and(QMessage.message.toUser.uuid.eq(userId));
    }
    return predicate;
  }

}
