package com.ma.spoton.api.services;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import javax.validation.ConstraintViolationException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.ClientConfigDto;
import com.ma.spoton.api.dtos.ClientDto;
import com.ma.spoton.api.dtos.ClientExportDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.QClient;
import com.ma.spoton.api.entities.QLocation;
import com.ma.spoton.api.entities.QUser;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.ClientMapper;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.ClientConfigRequest;
import com.ma.spoton.api.requests.ClientRequest;
import com.ma.spoton.api.utils.CSVUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientMapper clientMapper;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private LocationRepository locationRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public void createClient(ClientRequest clientRequest) {
        Client client = clientMapper.mapToEntity(clientRequest);
        client.setBol(false);
        client.setDvir(false);
        client.setTrailerAudit(false);
        client.setAccountDeactivation(false);

        try {
            clientRepository.save(client);
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while creating client! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_CLIENT_NAME);
        }
    }

    @Override
    @Transactional
    public void updateClient(String clientId, ClientRequest clientRequest) {
        Client client = clientRepository.findByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        client = clientMapper.updateEntity(clientRequest, client);
        clientRepository.save(client);
        BooleanExpression mandatoryPredicate = QUser.user.clients.any().uuid.in(client.getUuid());
        Iterable<User> usersIterable = userRepository.findAll(mandatoryPredicate);
        List<User> users = StreamSupport.stream(usersIterable.spliterator(), false)
                .collect(Collectors.toList());
        for (User user : users) {
            user.setTimeZone(client.getTimeZone());
            userRepository.save(user);
        }

    }

    @Override
    @Transactional
    public void deleteClient(String clientId) {
        Client client = clientRepository.findByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        BooleanExpression locationBooleanExpression = QLocation.location.client.eq(client);
        long locationCount = locationRepository.count(locationBooleanExpression);

        if (locationCount > 0 || !client.getUsers().isEmpty() || !client.getFleets().isEmpty()) {
            client.setIsActive(false);
        } else {
            clientRepository.delete(client);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ClientDto getClient(String clientId, String timeZone, List<String> clientIds) {
        if (CollectionUtils.isNotEmpty(clientIds) && !clientIds.contains(clientId)) {
            throw new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId);
        }
        Client client = clientRepository.findByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        return clientMapper.mapToDto(client, timeZone);
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<ClientDto> getClients(Predicate predicate, Pageable pageable,
                                               String timeZone, List<String> clientIds) {
        //    log.info(">> getClients({}, {}, {}, {})", predicate, pageable, timeZone, clientIds);

        BooleanExpression mandatoryPredicate = QClient.client.uuid.in(clientIds);

        if (CollectionUtils.isNotEmpty(clientIds)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        Page<Client> clientsPage = clientRepository.findAll(predicate, pageable);
        return PagedResponse.<ClientDto>builder()
                .list(clientsPage.stream().map(client -> clientMapper.mapToDto(client, timeZone))
                              .collect(Collectors.toList()))
                .page(clientsPage.getNumber()).size(clientsPage.getSize())
                .totalElements(clientsPage.getTotalElements()).build();
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportClientsAsCSV(Predicate predicate, String timeZone, List<String> clientIds) {
        //    log.info(">> exportClientsAsCSV({}, {}, {})", predicate, timeZone, clientIds);
        BooleanExpression mandatoryPredicate = QClient.client.uuid.in(clientIds);

        if (CollectionUtils.isNotEmpty(clientIds)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        var clients = clientRepository.findAll(predicate);

        List<ClientExportDto> clientExportDtos = new ArrayList<>();
        clients.forEach(client -> clientExportDtos.add(clientMapper.mapToExportDto(client, timeZone)));

        try {
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName =
                    tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
            try (OutputStream outputStream = new FileOutputStream(fileName);) {
                String csvData = CSVUtils.toCSV(clientExportDtos, ',', true);
                outputStream.write(csvData.getBytes());
                outputStream.flush();
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting CSV!", e);
            throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportClientsAsExcel(Predicate predicate, String timeZone, List<String> clientIds) {
        // TODO Auto-generated method stub

        //  	  log.info(">> exportClientsAsExcel({}, {}, {})", predicate, timeZone, clientIds);
        BooleanExpression mandatoryPredicate = QClient.client.uuid.in(clientIds);

        if (CollectionUtils.isNotEmpty(clientIds)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        var clients = clientRepository.findAll(predicate);

        List<ClientExportDto> clientExportDtos = new ArrayList<>();
        clients.forEach(client -> clientExportDtos.add(clientMapper.mapToExportDto(client, timeZone)));

        try {
            // Create data rows
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Clients");

            // Create header row
            Row headerRow = sheet.createRow(0);

            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // Create cells with bold values
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("Client Id");
            cell1.setCellStyle(cellStyle);

            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("Client Name");
            cell2.setCellStyle(cellStyle);

            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("Street");
            cell3.setCellStyle(cellStyle);

            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("City");
            cell4.setCellStyle(cellStyle);

            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("State");
            cell5.setCellStyle(cellStyle);

            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("Zip");
            cell6.setCellStyle(cellStyle);

            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("Country");
            cell7.setCellStyle(cellStyle);

            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("Contact Person");
            cell8.setCellStyle(cellStyle);

            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("Contact Email");
            cell9.setCellStyle(cellStyle);

            Cell cell10 = headerRow.createCell(9);
            cell10.setCellValue("Contact Phone");
            cell10.setCellStyle(cellStyle);

            Cell cell11 = headerRow.createCell(10);
            cell11.setCellValue("Remarks");
            cell11.setCellStyle(cellStyle);

            Cell cell12 = headerRow.createCell(11);
            cell12.setCellValue("Is Active");
            cell12.setCellStyle(cellStyle);

            Cell cell13 = headerRow.createCell(12);
            cell13.setCellValue("Created Date");
            cell13.setCellStyle(cellStyle);

            Cell cell14 = headerRow.createCell(13);
            cell14.setCellValue("Last Modified Date");
            cell14.setCellStyle(cellStyle);

            Cell cell15 = headerRow.createCell(14);
            cell15.setCellValue("Created By");
            cell15.setCellStyle(cellStyle);

            Cell cell16 = headerRow.createCell(15);
            cell16.setCellValue("Last Modified By");
            cell16.setCellStyle(cellStyle);
            int rowNum = 1;
            for (ClientExportDto clientExportDto : clientExportDtos) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(clientExportDto.getClientId());
                row.createCell(1).setCellValue(clientExportDto.getClientName());
                row.createCell(2).setCellValue(clientExportDto.getStreet());
                row.createCell(3).setCellValue(clientExportDto.getCity());
                row.createCell(4).setCellValue(clientExportDto.getState());
                row.createCell(5).setCellValue(clientExportDto.getZip());
                row.createCell(6).setCellValue(clientExportDto.getCountry());
                row.createCell(7).setCellValue(clientExportDto.getContactPerson());
                row.createCell(8).setCellValue(clientExportDto.getContactEmail());
                row.createCell(9).setCellValue(clientExportDto.getContactPhone());
                row.createCell(10).setCellValue(clientExportDto.getRemarks());
                if (clientExportDto.getIsActive() == true) {
                    row.createCell(11).setCellValue("true");
                } else {
                    row.createCell(11).setCellValue("false");
                }
                row.createCell(12).setCellValue(clientExportDto.getCreatedDate());
                row.createCell(13).setCellValue(clientExportDto.getLastModifiedDate());
                row.createCell(14).setCellValue(clientExportDto.getCreatedBy());
                row.createCell(15).setCellValue(clientExportDto.getLastModifiedBy());

                // Add more cells as needed for other fields
            }

            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
        }
    }

    @Override
    public void updateClientConfig(ClientConfigRequest clientConfigRequest) {
        // TODO Auto-generated method stub
        Client client = clientRepository.findByUuid(clientConfigRequest.getClientId())
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientConfigRequest.getClientId()));
        if (clientConfigRequest.getBol() != null) {
            client.setBol(clientConfigRequest.getBol());
        }
        if (clientConfigRequest.getTrailerAudit() != null) {
            client.setTrailerAudit(clientConfigRequest.getTrailerAudit());
        }
        if (clientConfigRequest.getDvir() != null) {
            client.setDvir(clientConfigRequest.getDvir());
        }
        if (clientConfigRequest.getAccountDeactivation() != null) {
            client.setAccountDeactivation(clientConfigRequest.getAccountDeactivation());
        }
        if (clientConfigRequest.getJobReAssign() != null) {
            client.setJobReAssign(clientConfigRequest.getJobReAssign());
        }
        if (clientConfigRequest.getCustomConfigurations() != null) {
            client.setCustomConfigurations(clientConfigRequest.getCustomConfigurations());
        }

        clientRepository.save(client);
    }

    @Override
    public ClientConfigDto getClientConfig(String uuid) {
        // TODO Auto-generated method stub
        User user1 = userRepository.findByUserUuid(uuid);
        List<String> clientIds = new ArrayList<>();
        List<Client> clients = clientRepository.findAllByUserId(user1.getId());
        Client client = clients.get(0);
        ClientConfigDto clientConfigDto = clientMapper.maptoConfig(client);
        return clientConfigDto;
    }

    @Override
    public ClientConfigDto getConfigOfClient(String uuid) {
        // TODO Auto-generated method stub
        Client client = clientRepository.findUsingUuid(uuid);
        ClientConfigDto clientConfigDto = clientMapper.maptoConfig(client);
        return clientConfigDto;
    }
}
