package com.ma.spoton.api.services;

import com.ma.spoton.api.entities.ContactUs;
import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.requests.MessageRequest;

public interface NotificationService {

  void autoSendNotification();

  void createAndSendForgotPasswordNotification(User user);

  void createHotTrailerAlert(User fromUser, User toUser, String fleetUnitNumber, String clientName);

  void createNewUserNotification(User user);

  void createNewJobNotification(Job job);

  void createUpdateJobNotification(Job job);

  void createFleetEntryExitNotification(GuardEntryExit guardEntryExit, User user);
  
  void createAndSendDeleteAccountAlertNotification(User user);
  
  void createNewMessageNotification(Message message);
  
  void createJobFromMessageNotification(Job job, User user, String timeZone);
  
  void createContactUsEmailNotification(ContactUs contact);

}
