package com.ma.spoton.api.services;

import static org.springframework.http.HttpMethod.POST;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.MulticastMessage;
import com.google.firebase.messaging.Notification;
import com.ma.spoton.api.dtos.PushMessageRequest;
import com.ma.spoton.api.dtos.PushMessageResponse;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


@Slf4j
@AllArgsConstructor
public class FCMPushNotificationServiceImpl implements PushNotificationService {

  private static final String FCM_API_URL = "https://fcm.googleapis.com/fcm/send";

  private String apiKey="AAAAmEY1JH8:APA91bH5ktPuuPHb6jP58eFGqpQJMHGV7f0DhJcQ2UCwDR3-OEB98ZJ1vuROy3WUra_IefsjWh5XencukpCRatBDrrEm_xMQonSpLH2wIGUINfRWCBCmHt9GkTFPvHNqpsztqmPoJcaE";
  

  
/*  public void push(String deviceRegistrationId,String messageBody)
  {try {
	       
	        OkHttpClient client = new OkHttpClient().newBuilder().build();
			MediaType mediaType = MediaType.parse(" application/json");
            RequestBody body = RequestBody.create(mediaType, "{\n    \"to\" : \"chobLBz9TnyWjeXAmjsfVI:APA91bFTc-kti8y_aK7-_wDDwCXKidrobCPid3Q37GlenT-6zCyht1tbWuuxnad6ytSk11cl_RTtGy27rFkpXVnn_L4bHswH8CSdYar96XF66kxZ0uFjG9LK8ckSbP3qSOpL7wpfEQUw\",\n    \"notification\" : {\n      \"body\" : \"hello\"\n      \n    }\n  }");
			
			Request request = new Request.Builder()
			  .url("https://fcm.googleapis.com/fcm/send")
			  .method("POST", body)
			  .addHeader("Content-Type", " application/json")
			  .addHeader("Authorization", " key=AAAAmEY1JH8:APA91bH5ktPuuPHb6jP58eFGqpQJMHGV7f0DhJcQ2UCwDR3-OEB98ZJ1vuROy3WUra_IefsjWh5XencukpCRatBDrrEm_xMQonSpLH2wIGUINfRWCBCmHt9GkTFPvHNqpsztqmPoJcaE")
			  .build();
			
				Response response = client.newCall(request).execute();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
  }
  */
  public void push(String deviceRegistrationId, String messageBody) {
	    try {
	        OkHttpClient client = new OkHttpClient().newBuilder().build();
	        MediaType mediaType = MediaType.parse("application/json");
	        JSONObject requestBody = new JSONObject();
	        requestBody.put("to", deviceRegistrationId);
	        JSONObject notification = new JSONObject();
	        notification.put("body", messageBody);
	        requestBody.put("notification", notification);
	        RequestBody body = RequestBody.create(mediaType, requestBody.toString());
	        Request request = new Request.Builder()
	          .url("https://fcm.googleapis.com/fcm/send")
	          .method("POST", body)
	          .addHeader("Content-Type", "application/json")
	          .addHeader("Authorization", "key=AAAAmEY1JH8:APA91bH5ktPuuPHb6jP58eFGqpQJMHGV7f0DhJcQ2UCwDR3-OEB98ZJ1vuROy3WUra_IefsjWh5XencukpCRatBDrrEm_xMQonSpLH2wIGUINfRWCBCmHt9GkTFPvHNqpsztqmPoJcaE")
	          .build();
	        Response response = client.newCall(request).execute();
	    } catch (IOException | JSONException e) {
	        e.printStackTrace();
	    }
	}

  
  
  
 
  public PushMessageResponse pushMessage(PushMessageRequest pushMessageRequest) {
      try {
          // Build the notification
          Notification notification = Notification.builder()
              .setTitle(pushMessageRequest.getMessageTitle())
              .setBody(pushMessageRequest.getMessageBody())
              .build();

          // Build the data payload
          Map<String, String> data = new HashMap<>();
          data.put("messageBody", pushMessageRequest.getMessageBody());
          data.put("id", pushMessageRequest.getMessageId());
          data.put("click_action", "FLUTTER_NOTIFICATION_CLICK");
          data.put("message", pushMessageRequest.getMessageTitle());
          data.put("icon", "ic_launcher");
          data.put("sound", "default");
          
          for(String userDeviceId : pushMessageRequest.getUserDeviceIds())
          {
        	  if(userDeviceId != null)
        	  { 
        		  String to = userDeviceId; 
        		  try {
        			  Message message = Message.builder()
                              .setToken(to)
                              .setNotification(notification)
                              .putAllData(data)
                              .build();

                          FirebaseMessaging.getInstance().send(message); 
        		  }
        		  catch(Exception e)
        		  {
        			  log.info("error({})",e);
        		  }
        		 
        	  }
          }
//          log.info("FCM Message sent to User : {} for Notification : {}",
//                  pushMessageRequest.getUserDeviceIds(), pushMessageRequest.getMessageId());

          return PushMessageResponse.builder()
              .statusCode(200)
              .responseMessage("Message pushed successfully!")
              .build();
      } catch (Exception e) {
          log.error("Error occurred while publishing message to FCM to User : {} for Notification : {}!",
                  pushMessageRequest.getUserDeviceIds(), pushMessageRequest.getMessageId(), e);

          Map<String, Object> errorResponse = new HashMap<>();
          errorResponse.put("errorMessage", e.getMessage());
          errorResponse.put("stackTrace", e.getStackTrace());

          return PushMessageResponse.builder()
              .statusCode(500)
              .responseMessage("Error occurred while pushing message! Reason : " + e.getMessage())
              .customData(errorResponse)
              .build();
      }
  }


	 
	
	
	
  
	  
	  
	
  
  
  
//
//  @Override
//  public PushMessageResponse pushMessage(PushMessageRequest pushMessageRequest) {
//    //log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> publishMessage({})", pushMessageRequest);
//    
//    try {
//    	//List<String> UserUuid=pushMessageRequest.get
//    	//push();
//    	
//      var uri = fromHttpUrl(FCM_API_URL).build().encode().toUri();
//      var restTemplate = new RestTemplate();
//      ResponseEntity<Map<String, Object>> subscriptionResponse =
//          restTemplate
//              .exchange(uri, POST,
//                  new HttpEntity<Map<String, Object>>(getFCMPublishRequestBody(pushMessageRequest)
//                      ,getFCMHeader(apiKey)),
//                  new ParameterizedTypeReference<Map<String, Object>>() {});
//      log.info("FCM Message Response : {}", subscriptionResponse.toString());
//      if (subscriptionResponse.getStatusCodeValue() >= 200
//          && subscriptionResponse.getStatusCodeValue() < 300) {
//        log.info("FCM Message sent to User : {} for Notification : {}",
//            pushMessageRequest.getUserDeviceIds(), pushMessageRequest.getMessageId());
//        return PushMessageResponse.builder().statusCode(subscriptionResponse.getStatusCodeValue())
//            .responseMessage("Message pushed successfully!")
//            .customData(subscriptionResponse.getBody()).build();
//      } else {
//        String errorMessage = "Unknown";
//        if (subscriptionResponse.getBody() != null) {
//          errorMessage = subscriptionResponse.getBody().containsKey("error")
//              ? (String) subscriptionResponse.getBody().get("error")
//              : (String) subscriptionResponse.getBody().get("Error");
//        }
//        return PushMessageResponse.builder().statusCode(subscriptionResponse.getStatusCodeValue())
//            .responseMessage("Error occurred while pushing message! Reason : " + errorMessage)
//            .customData(subscriptionResponse.getBody()).build();
//      }
//    } catch (Exception e) {
//      log.error(
//          "Error occurred while publishing message to FCM to User : {} for Notification : {}!",
//          pushMessageRequest.getUserDeviceIds(), pushMessageRequest.getMessageId(), e);
//      Map<String, Object> errorResponse = new HashMap<>();
//      errorResponse.put("errorMessage", e.getMessage());
//      errorResponse.put("stackTrace", e.getStackTrace());
//      return PushMessageResponse.builder().statusCode(500)
//          .responseMessage("Error occurred while pushing message! Reason : " + e.getMessage())
//          .customData(errorResponse).build();
//    }
//  }

  private Map<String, Object> getFCMPublishRequestBody(PushMessageRequest pushMessageRequest) {
    Map<String, Object> publishRequest = new HashMap<>();
    if (CollectionUtils.isNotEmpty(pushMessageRequest.getUserDeviceIds())
        && pushMessageRequest.getUserDeviceIds().size() > 1) {
      publishRequest.put("registration_ids", pushMessageRequest.getUserDeviceIds());
    } else {
      String to = CollectionUtils.isNotEmpty(pushMessageRequest.getUserDeviceIds())
          ? pushMessageRequest.getUserDeviceIds().iterator().next()
          : "";
      publishRequest.put("to",
          StringUtils.isNotBlank(pushMessageRequest.getTopicId()) ? pushMessageRequest.getTopicId()
              : to);
    }

    Map<String, String> notificationBody = new HashMap<>();
    notificationBody.put("body", pushMessageRequest.getMessageBody());
    notificationBody.put("title", pushMessageRequest.getMessageTitle());
    notificationBody.put("icon", "ic_launcher");
    notificationBody.put("sound", "default");
    publishRequest.put("notification", notificationBody);

    Map<String, Object> data = new HashMap<>();
    data.put("messageBody", pushMessageRequest.getMessageBody());
    data.put("id", pushMessageRequest.getMessageId());
    data.put("click_action", "FLUTTER_NOTIFICATION_CLICK");
    data.put("message", pushMessageRequest.getMessageTitle());
    publishRequest.put("data", data);
   // log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> body({})", publishRequest);
    return publishRequest;
  }

  private static HttpHeaders getFCMHeader(String apiKey) {
    var headers = new HttpHeaders();
    String authHeader = "key=" + apiKey;
    headers.set("Authorization", authHeader);
    headers.setContentType(APPLICATION_JSON);
   // log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> header({})", headers);
    return headers;
  }

}

