package com.ma.spoton.api.services;

import static com.amazonaws.regions.Regions.US_EAST_1;
import static com.amazonaws.services.s3.AmazonS3ClientBuilder.standard;
import static com.ma.spoton.api.constants.BusinessConstants.DEFAULT_CONTENT_CACHE_TIME;
import static java.util.Calendar.getInstance;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.security.Security;
import java.time.Duration;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.AmazonClientException;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.kinesisanalyticsv2.model.CreateApplicationPresignedUrlRequest;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.internal.ServiceUtils;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.ma.spoton.api.entities.Bol;
import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.BolRepository;
import com.ma.spoton.api.repositories.GuardEntryExitRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

import com.amazonaws.services.ecs.AmazonECSClientBuilder;
import com.amazonaws.services.ecs.model.ListClustersRequest;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClientBuilder;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;

@Slf4j
@Service
public class AwsS3FileServiceImpl implements FileService {
	private AmazonS3 s3client = null;

	@Value("${application.cdn.bucket}")
	private String cdnBucket;

	@Autowired
	private JobRepository jobRepository;

	@Autowired
	private BolRepository bolRepository;

	@Autowired
	private GuardEntryExitRepository guardEntryExitRepository;

	@Override
	public void upload(byte[] fileData, String path, String fileName, String contentType) {
		log.info(">> uploadToS3CDN({}, {}, {})", path, fileName, contentType);
		// upload file to folder and set it to public
		ObjectMetadata meta = new ObjectMetadata();
		meta.setContentLength(fileData.length);
		meta.setContentType(contentType);

		// Adding 1 week expiration time
		Calendar oneWeekLaterDate = Calendar.getInstance();
		oneWeekLaterDate.add(Calendar.DAY_OF_MONTH, 7);
		meta.setHttpExpiresDate(oneWeekLaterDate.getTime());
		meta.setCacheControl("max-age=" + DEFAULT_CONTENT_CACHE_TIME);

		try {
			AmazonS3 s3client = getS3Client();
			if (s3client == null) {
				throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
			}

			PutObjectRequest putRequest = new PutObjectRequest(cdnBucket, path + "/" + fileName,
					new ByteArrayInputStream(fileData), meta);

			s3client.putObject(putRequest);
		} catch (Exception e) {
			log.info(">> fail to upload ({})", e);
		}
	}

	@Override
	public void delete(String path, String fileName) {
		// delete file from S3 CDN
		try {
			AmazonS3 s3client = getS3Client();
			if (s3client == null) {
				throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
			}

			s3client.deleteObject(cdnBucket, path + "/" + fileName);
		} catch (Exception e) {
			log.info(">> fail to delete ({})", e);
		}
	}

	@Override
	public InputStream download(String path, String fileName) {
		// log.info(">> download({}, {})", path, fileName);
		try {
			AmazonS3 s3client = getS3Client();
			if (s3client == null) {
				throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
			}
			S3Object s3Object = s3client.getObject(cdnBucket, path + "/" + fileName);

			return s3Object.getObjectContent();
		} catch (Exception e) {
			log.info(">> fail to download ({})", e);
		}
		return null;
	}

	@Override
	public void copy(String sourcePath, String destinationPath) {
		// log.info(">> copy({}, {})", sourcePath, destinationPath);
		// Copy the object into a new object in the same bucket.
		try {
			AmazonS3 s3client = getS3Client();
			if (s3client == null) {
				throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
			}
			s3client.copyObject(new CopyObjectRequest(cdnBucket, sourcePath, cdnBucket, destinationPath));
		} catch (Exception e) {
			log.info(">> fail to copy ({})", e);
		}
		// log.info("EXIT copy <<");
	}

	@Override
	public void upload(InputStream fileData, String path, String fileName, String contentType) throws IOException {
		// log.info(">> uploadToS3CDN({}, {}, {})", path, fileName, contentType);
		ObjectMetadata meta = new ObjectMetadata();
		meta.setContentType(contentType);

		// Adding 1 week expiration time
		Calendar oneWeekLaterDate = getInstance();
		oneWeekLaterDate.add(Calendar.DAY_OF_MONTH, 7);
		meta.setHttpExpiresDate(oneWeekLaterDate.getTime());
		meta.setCacheControl("max-age=" + DEFAULT_CONTENT_CACHE_TIME);

		try {
			AmazonS3 s3client = getS3Client();
			if (s3client == null) {
				throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
			}

			PutObjectRequest putRequest = new PutObjectRequest(cdnBucket, path + "/" + fileName, fileData, meta);

			s3client.putObject(putRequest);
		} catch (AmazonClientException ace) {
			log.info("Error Message << {}", ace.getMessage());
		} finally {
			if (fileData != null) {
				fileData.close();
			}

			// log.info("EXIT uploadToS3CDN <<");
		}
	}

	@Override
	public void createFolderAndSaveBol(String folderName, List<MultipartFile> files, String jobId, String type) {
		log.info(">> createFolderAndSaveBol ({}, {}, {}, {})", folderName, files, jobId, type);
		try {
			Set<Bol> bols = new HashSet<>();
			if (!folderName.endsWith("/")) {
				folderName += "/";
			}

			Job job = jobRepository.findByUuid(jobId)
					.orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, jobId));

			if (type.equals("signed"))
				job.setSignedBol(true);
			else
				job.setUnsignedBol(true);
			for (MultipartFile file : files) {

				//byte[] fileData = FileUtils.convertMultipartFileToByteArray(file);
				byte[] fileData = file.getBytes();
				log.info(">> createFolderAndSaveBol fileData : ({})", fileData);
				ObjectMetadata meta = new ObjectMetadata();
				meta.setContentLength(fileData.length);
				String contentType = file.getContentType();
				//meta.setContentType(contentType);
				meta.setContentType(contentType != null ? contentType : "application/octet-stream");
				String extension = FilenameUtils.getExtension(file.getOriginalFilename());
				String uuid = UUID.randomUUID().toString();
				String fileName = uuid + "." + extension;
				Calendar oneWeekLaterDate = Calendar.getInstance();
				oneWeekLaterDate.add(Calendar.DAY_OF_MONTH, 7);
				meta.setHttpExpiresDate(oneWeekLaterDate.getTime());
				meta.setCacheControl("max-age=" + DEFAULT_CONTENT_CACHE_TIME);
				try {
					AmazonS3 s3client = getS3Client();
					if (s3client == null) {
						throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
					}
					log.info(">> createFolderAndSaveBol cdnBucket : {}, folderName : {}, fileName: {}", cdnBucket, folderName, fileName);
					PutObjectRequest putRequest = new PutObjectRequest(cdnBucket, folderName + fileName,
							new ByteArrayInputStream(fileData), meta);

					//s3client.putObject(putRequest);
					 PutObjectResult result = s3client.putObject(putRequest);

					 log.info(">> createFolderAndSaveBol S3 upload successful. ETag: {}, VersionId: {}, Metadata: {}",
					            result.getETag(),
					            result.getVersionId() != null ? result.getVersionId() : "null",
					            result.getMetadata() != null ? result.getMetadata().getRawMetadata() : "null");
					    
					 AmazonS3 s3Client = getS3Client();
					 ListObjectsV2Request request = new ListObjectsV2Request()
					     .withBucketName(cdnBucket)
					     .withPrefix(folderName);
					  
					 ListObjectsV2Result result2 = s3Client.listObjectsV2(request);
					 List<S3ObjectSummary> objects = result2.getObjectSummaries();
					  
					 if (objects.isEmpty()) {
						 log.error("No objects found in the S3 bucket: {}", cdnBucket);
					 } else {
						 log.info("Objects found in the S3 bucket:");
					     for (S3ObjectSummary obj : objects) {
					    	 log.info("- {}", obj.getKey());
					     }
					 }
				} catch (Exception e) {
					log.error("Failed to upload file in createFolderAndSaveBol : {}: {}", file.getOriginalFilename(), e.getMessage());
				}
				Bol bol = new Bol();
				bol.setJob(job);
				bol.setImagePath(fileName);
				bolRepository.save(bol);
			}
			jobRepository.save(job);
		} catch (Exception e) {
			log.error("Error in createFolderAndSaveBol: {}", e.getMessage(), e);
		}
	}

	@Override
	public void entryExitBillOfLandingImage(String folderName, Long guardEntryExitId, MultipartFile file) {

		try {
			// Set<Bol> bols=new HashSet<>();
			if (!folderName.endsWith("/")) {
				folderName += "/";
			}

			byte[] fileData = FileUtils.convertMultipartFileToByteArray(file);
			ObjectMetadata meta = new ObjectMetadata();
			meta.setContentLength(fileData.length);
			String contentType = file.getContentType();
			meta.setContentType(contentType);
			String extension = FilenameUtils.getExtension(file.getOriginalFilename());
			String uuid = UUID.randomUUID().toString();
			String fileName = uuid + "." + extension;
			Calendar oneWeekLaterDate = Calendar.getInstance();
			oneWeekLaterDate.add(Calendar.DAY_OF_MONTH, 7);
			meta.setHttpExpiresDate(oneWeekLaterDate.getTime());
			meta.setCacheControl("max-age=" + DEFAULT_CONTENT_CACHE_TIME);
			try {
				AmazonS3 s3client = getS3Client();
				if (s3client == null) {
					throw new ServiceException(ErrorCode.AWS_CREDENTIALS_NOT_FOUND);
				}

				PutObjectRequest putRequest = new PutObjectRequest(cdnBucket, folderName + fileName,
						new ByteArrayInputStream(fileData), meta);

				s3client.putObject(putRequest);
			} catch (Exception e) {
				log.info(">> fail to entryExitBillOfLandingImage ({})", e);
			}
			GuardEntryExit entryExit = guardEntryExitRepository.findById(guardEntryExitId)
					.orElseThrow(() -> new ServiceException(ErrorCode.GUARD_ENTRY_EXIT_NOT_FOUND, guardEntryExitId));
			// GuardEntryExit entryExit = new GuardEntryExit();
			entryExit.setBillOfLandingImage(fileName);
			guardEntryExitRepository.save(entryExit);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	private AmazonS3 getS3Client() {
		try {
			if (s3client != null) {
				return s3client;
			}

			s3client = AmazonS3ClientBuilder.standard().withCredentials(new DefaultAWSCredentialsProviderChain())
					.withRegion(US_EAST_1).build();

			return s3client;
		} catch (Exception e) {
			log.error("Error initializing S3 client: {}", e.getMessage(), e);
			return null;
		}
	}
}
