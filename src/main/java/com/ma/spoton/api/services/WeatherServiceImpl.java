package com.ma.spoton.api.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.ma.spoton.api.requests.JobStatusRequest;

import lombok.AllArgsConstructor;

@Service
public class WeatherServiceImpl {

	 @Value("${application.weather-api-base-url}")
	 private String weatherApiBaseUrl;
	 
	 @Value("${application.weather-api-key}")
	 private String weatherApiKey;
	
	@Autowired
	private RestTemplate restTemplate;
	
	public String getWeather(JobStatusRequest jobStatusRequest)
	{
        String lat = jobStatusRequest.getLat();
        String lng = jobStatusRequest.getLng();
		String response = restTemplate.getForObject(weatherApiBaseUrl+"lat="+lat+"&lon="+lng+"&units=imperial&appid="+weatherApiKey, String.class);
        return response;

	}
}
