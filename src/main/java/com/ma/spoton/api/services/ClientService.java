package com.ma.spoton.api.services;

import java.util.List;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.dtos.ClientConfigDto;
import com.ma.spoton.api.dtos.ClientDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.requests.ClientConfigRequest;
import com.ma.spoton.api.requests.ClientRequest;
import com.querydsl.core.types.Predicate;

public interface ClientService {
	
	void updateClientConfig(ClientConfigRequest clientConfigRequest);	
	
	ClientConfigDto getClientConfig(String uuid);
	
	ClientConfigDto getConfigOfClient(String uuid);
  
	void createClient(ClientRequest clientRequest);

  void updateClient(String clientId, ClientRequest clientRequest);

  void deleteClient(String clientId);

  ClientDto getClient(String clientId, String timeZone, List<String> clientIds);

  PagedResponse<ClientDto> getClients(Predicate predicate, Pageable pageable, String timeZone,
      List<String> clientIds);

  Resource exportClientsAsCSV(Predicate predicate, String timeZone, List<String> clientIds);
  
  Resource exportClientsAsExcel(Predicate predicate, String timeZone, List<String> clientIds);
}
