package com.ma.spoton.api.services;


import com.ma.spoton.api.dtos.LocationAvgTimeDTO;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class AvgTimeService {

    @Autowired
    private SpotRepository spotRepository;

    @Autowired
    private LocationRepository locationRepository;

    // Cache to store dwell time results
    private static class AvgTimeCache {
        String clientId;
        LocalDate startDate;
        LocalDate endDate;
        List<LocationAvgTimeDTO> avgTimes;

        AvgTimeCache(String clientId, LocalDate startDate, LocalDate endDate, List<LocationAvgTimeDTO> avgTimes) {
            this.clientId = clientId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.avgTimes = avgTimes;
        }

        boolean isValid(String clientId, LocalDate startDate, LocalDate endDate) {
            return this.clientId.equals(clientId) &&
                    this.startDate.equals(startDate) &&
                    this.endDate.equals(endDate);
        }
    }

    // Thread-safe cache using ConcurrentHashMap
    private final ConcurrentHashMap<String, AvgTimeCache> avgTimeCacheMap = new ConcurrentHashMap<>();


    public List<LocationAvgTimeDTO> computeAvgEmptiedAndOccupiedTime(String clientId, boolean refreshCache) {
        ZonedDateTime endTime = ZonedDateTime.now();
        ZonedDateTime startTime = endTime.minusDays(7);
        LocalDate endDate = endTime.toLocalDate();
        LocalDate startDate = startTime.toLocalDate();

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, startDate, endDate);
        if (!refreshCache) {
            AvgTimeCache cache = avgTimeCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId, startDate, endDate)) {
                return cache.avgTimes;
            }
        }

        // Fetch all locations for the client
        List<Location> activeLocations = locationRepository.findActiveLocationsByClientId(clientId);
        List<LocationAvgTimeDTO> result = new ArrayList<>();

        for (Location location : activeLocations) {
            LocationAvgTimeDTO dto = new LocationAvgTimeDTO();
            dto.setLocationId(location.getId());
            dto.setLocationName(location.getLocationName());
            result.add(dto);
        }

        //Processing Last Emptied Time

        // Fetch last emptied time data for the last 7 days
        List<Spot> fetchedSpotsByLastEmptiedTime = spotRepository.findSpotsByClientIdAndLastEmptiedTimeBetween(clientId, startTime, endTime);
        // Calculate total last emptied time and count per location
        Map<Long, Double> locationIdToTotalLastEmptiedHours = new HashMap<>();
        Map<Long, Integer> locationIdToLastEmptiedRowCounts = new HashMap<>();

        for (Spot spot : fetchedSpotsByLastEmptiedTime) {
            long lastEmptiedMinutes = ChronoUnit.MINUTES.between(spot.getLastEmptiedTime(), endTime);
            double lastEmptiedHours = lastEmptiedMinutes / 60.0;
            Long locationId = spot.getLocation().getId();

            locationIdToTotalLastEmptiedHours.merge(locationId, lastEmptiedHours, Double::sum);
            locationIdToLastEmptiedRowCounts.merge(locationId, 1, Integer::sum);
        }


        // Processing Last Occupied Time

        // Fetch last occupied time data for the last 7 days
        List<Spot> fetchedSpotsByLastOccupiedTime = spotRepository.findSpotsByClientIdAndLastOccupiedTimeBetween(clientId, startTime, endTime);
        // Calculate total last occupied time and count per location
        Map<Long, Double> locationIdToTotalLastOccupiedHours = new HashMap<>();
        Map<Long, Integer> locationIdToLastOccupiedRowCounts = new HashMap<>();

        for (Spot spot : fetchedSpotsByLastOccupiedTime) {
            long lastEmptiedMinutes = ChronoUnit.MINUTES.between(spot.getLastOccupiedTime(), endTime);
            double lastEmptiedHours = lastEmptiedMinutes / 60.0;
            Long locationId = spot.getLocation().getId();

            locationIdToTotalLastOccupiedHours.merge(locationId, lastEmptiedHours, Double::sum);
            locationIdToLastOccupiedRowCounts.merge(locationId, 1, Integer::sum);
        }

        // Calculate average time per location
        for (LocationAvgTimeDTO dto : result) {

            Double totalHours = locationIdToTotalLastOccupiedHours.getOrDefault(dto.getLocationId(), 0.0);
            Integer count = locationIdToLastOccupiedRowCounts.getOrDefault(dto.getLocationId(), 0);
            double averageLastOccupiedTime = (count > 0) ? (totalHours / count ) : 0.0;
            dto.setAverageLastOccupiedTimeHours((double) Math.round(averageLastOccupiedTime));

            Double totalLastEmptiedHours = locationIdToTotalLastEmptiedHours.getOrDefault(dto.getLocationId(), 0.0);
            Integer lastEmptiedRowCount = locationIdToLastEmptiedRowCounts.getOrDefault(dto.getLocationId(), 0);

            double averageLastEmptiedTime = (lastEmptiedRowCount > 0) ? (totalLastEmptiedHours / lastEmptiedRowCount ) : 0.0;
            dto.setAverageLastEmptiedTimeHours((double)Math.round(averageLastEmptiedTime));

        }


        // Cache the result
        avgTimeCacheMap.put(cacheKey, new AvgTimeCache(clientId, startDate, endDate, result));

        return result;
    }

    private String generateCacheKey(String clientId, LocalDate startDate, LocalDate endDate) {
        return clientId + "::" + startDate.toString() + "::" + endDate.toString();
    }

}
