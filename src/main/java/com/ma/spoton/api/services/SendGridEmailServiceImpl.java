package com.ma.spoton.api.services;

import java.util.Arrays;
import java.util.Map;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.ma.spoton.api.dtos.Attachment;
import com.ma.spoton.api.dtos.EmailMessageRequest;
import com.ma.spoton.api.dtos.EmailResponse;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@AllArgsConstructor
public class SendGridEmailServiceImpl implements EmailService {

  private String sendGridKey;
  private String fromEmail;
  private String fromName;

  @Override
  public EmailResponse sendEmail(EmailMessageRequest emailMessageRequest) {

   // log.info(">> sendEmail({})", emailMessageRequest);
    try {
      var from = new Email(fromEmail, fromName);
      emailMessageRequest.getRecipients().get(0);
      var content = new Content("text/html", emailMessageRequest.getContent().toString());
      var mail = new Mail();
      if (StringUtils.isNotBlank(emailMessageRequest.getReplyTo())) {
        mail.setReplyTo(new Email(emailMessageRequest.getReplyTo()));
      }

      var personalization = new Personalization();
      emailMessageRequest.getRecipients().forEach(
          recipient -> personalization.addTo(new Email(recipient.getEmail(), recipient.getName())));
      if (CollectionUtils.isNotEmpty(emailMessageRequest.getCcRecipients())) {
        emailMessageRequest.getCcRecipients().forEach(recipient -> personalization
            .addCc(new Email(recipient.getEmail(), recipient.getName())));
      }
      if (CollectionUtils.isNotEmpty(emailMessageRequest.getBccRecipients())) {
        emailMessageRequest.getBccRecipients().forEach(recipient -> personalization
            .addBcc(new Email(recipient.getEmail(), recipient.getName())));
      }
      mail.addPersonalization(personalization);

      personalization.setSubject(emailMessageRequest.getSubject());
      mail.setFrom(from);
      mail.addContent(content);
      if (emailMessageRequest.isAttachment() && emailMessageRequest.getAttachmentMap() != null) {
        Attachments attachments = null;
        var base64Encoder = new Base64();
        for (Map.Entry<String, Attachment> entry : emailMessageRequest.getAttachmentMap()
            .entrySet()) {
          attachments = new Attachments();
          attachments.setFilename(entry.getKey());
          attachments.setType(entry.getValue().getAttachmentContentType());
          attachments.setDisposition("attachment");
          attachments
              .setContent(base64Encoder.encodeAsString(entry.getValue().getAttachmentByteArray()));
          mail.addAttachments(attachments);
        }
      }

      var sg = new SendGrid(sendGridKey);
      var request = new Request();

      request.setMethod(Method.POST);
      request.setEndpoint("mail/send");
      request.setBody(mail.build());

      var response = sg.api(request);
     // log.info("response({})",response);
      
      return EmailResponse.builder().statusCode(response.getStatusCode())
          .responseMessage(response.getBody()).customData(response.getHeaders()).build();
    } catch (Exception e) {
      return EmailResponse.builder().statusCode(500).responseMessage(e.getMessage())
          .customData(Map.of("stackTrace", Arrays.toString(e.getStackTrace()))).build();
    }


  }

}
