package com.ma.spoton.api.services;

import java.util.List;

import com.ma.spoton.api.dtos.MobileAppUpdateDto;
import com.ma.spoton.api.dtos.MobileAppVersionDto;
import com.ma.spoton.api.requests.MobileAppVersionRequest;

public interface MobileAppVersionService {

	MobileAppUpdateDto getMobileAppUpdatStatus(float currentVersion, String OS);

	void updateMobileVersion(float currentVersion,String OS);
	
	List<MobileAppVersionDto> getLatestMobileAppVersion();

}
