package com.ma.spoton.api.services;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.dtos.EmailMessageRequest;
import com.ma.spoton.api.dtos.EmailResponse;
import com.ma.spoton.api.dtos.MailRecipient;
import com.ma.spoton.api.dtos.PushMessageRequest;
import com.ma.spoton.api.dtos.PushMessageResponse;
import com.ma.spoton.api.entities.Notification;
import com.ma.spoton.api.entities.UserDevice;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendNotificationServiceImpl implements SendNotificationService {

  private final EmailService emailService;
  private final PushNotificationService pushNotificationService;
  private final NotificationRepository notificationRepository;

  @Override
  @Transactional
  public void send(String notificationId) {
    //log.info(">> send({})", notificationId);
    var notification = notificationRepository.findByUuid(notificationId)
        .orElseThrow(() -> new ServiceException(ErrorCode.NOTIFICATION_NOT_FOUND, notificationId));
    notification.setStartTime(ZonedDateTime.now());
    try {
      switch (notification.getChannel()) {
        case EMAIL:
          var emailResponse = sendEmail(notification);
          if (emailResponse.getStatusCode() >= 300) {
            notification.setDeliveryStatus(Notification.DeliveryStatus.FAILED);
            notification.setFailureReason(emailResponse.getResponseMessage());
          } else {
            notification.setCompletedTime(ZonedDateTime.now());
            notification.setDeliveryStatus(Notification.DeliveryStatus.DELIVERED);
          }
          break;
        case PUSH:
          var pushResponse = sendPushMessage(notification);
          if (pushResponse.getStatusCode() >= 300) {
            notification.setDeliveryStatus(Notification.DeliveryStatus.FAILED);
            notification.setFailureReason(pushResponse.getResponseMessage());
          } else {
            notification.setCompletedTime(ZonedDateTime.now());
            notification.setDeliveryStatus(Notification.DeliveryStatus.DELIVERED);
          }
          break;
        default:
          throw new IllegalArgumentException("Provided channel is not supported");
      }
    } catch (Exception e) {
    	
      log.error("Some error occurred while sending notification({e})",e);
      log.error("Some error occurred while sending notification({e})",e.toString());
      notification.setDeliveryStatus(Notification.DeliveryStatus.FAILED);
      notification.setFailureReason(e.getMessage());
    }
  }

  private EmailResponse sendEmail(Notification notification) {
    var emailMessageRequest = EmailMessageRequest.builder().isAttachment(false).attachmentMap(null)
        .bccRecipients(null).ccRecipients(null)
        .recipients(List.of(new MailRecipient(null, notification.getToUser().getEmail())))
        .content(notification.getMessageBody()).replyTo(null)
        .subject(notification.getMessageTitle()).build();
    return emailService.sendEmail(emailMessageRequest);
  }

  private PushMessageResponse sendPushMessage(Notification notification) {
    if (CollectionUtils.isNotEmpty(notification.getToUser().getDevices())) {
      Set<String> userDeviceIds = notification.getToUser().getDevices().stream()
          .map(UserDevice::getDeviceRegistrationId).collect(Collectors.toSet());
      return pushNotificationService.pushMessage(PushMessageRequest.builder()
          .userDeviceIds(userDeviceIds).messageBody(notification.getMessageBody())
          .messageTitle(notification.getMessageTitle())
          .messageId(notification.getMessageReferrenceId()).build());
    } else {
      return PushMessageResponse.builder().statusCode(404)
          .responseMessage("No User devices found to push notfication!").build();
    }
  }
}
