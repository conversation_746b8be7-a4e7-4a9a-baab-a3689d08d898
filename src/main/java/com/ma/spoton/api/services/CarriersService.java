//package com.ma.spoton.api.services;
//
//import java.util.List;
//
//import org.springframework.data.domain.Pageable;
//
//import com.ma.spoton.api.dtos.CarrierDto;
//import com.ma.spoton.api.dtos.FleetDto;
//import com.ma.spoton.api.dtos.PagedResponse;
//import com.ma.spoton.api.requests.CarriersRequest;
//import com.querydsl.core.types.Predicate;
//
//public interface CarriersService {
//
//	CarrierDto createCarrier(CarriersRequest carriersRequest);
//	
//	PagedResponse<CarrierDto> getCarriers(Predicate predicate, Pageable pageable);
//	
//	void deleteCarrier(String carrierId);
//	
//	void updateCarrier(String carrierId, CarriersRequest carriersRequest);
//	
//}
