package com.ma.spoton.api.services;

import com.ma.spoton.api.dtos.LocationAvgTimeDTO;
import com.ma.spoton.api.dtos.LocationDwellTimeDTO;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.JobRepository.DwellTimeProjection;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class DwellTimeService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private LocationRepository locationRepository;


    // Cache to store dwell time results
    private static class DwellTimeCache {
        String clientId;
        LocalDate startDate;
        LocalDate endDate;
        List<LocationDwellTimeDTO> dwellTimes;

        DwellTimeCache(String clientId, LocalDate startDate, LocalDate endDate, List<LocationDwellTimeDTO> dwellTimes) {
            this.clientId = clientId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.dwellTimes = dwellTimes;
        }

        boolean isValid(String clientId, LocalDate startDate, LocalDate endDate) {
            return this.clientId.equals(clientId) &&
                    this.startDate.equals(startDate) &&
                    this.endDate.equals(endDate);
        }
    }

    // Thread-safe cache using ConcurrentHashMap
    private final ConcurrentHashMap<String, DwellTimeCache> dwellTimeCacheMap = new ConcurrentHashMap<>();


    public List<LocationDwellTimeDTO> calculateAverageDwellTime(String clientId, boolean refreshCache) {
        ZonedDateTime endTime = ZonedDateTime.now();
        ZonedDateTime startTime = endTime.minusDays(7);
        LocalDate endDate = endTime.toLocalDate();
        LocalDate startDate = startTime.toLocalDate();

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId, startDate, endDate);
        if (!refreshCache) {
            DwellTimeCache cache = dwellTimeCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId, startDate, endDate)) {
                return cache.dwellTimes;
            }
        }

        // Fetch dwell time data for the last 7 days
        List<DwellTimeProjection> dwellTimes = jobRepository.findDwellTimesForClient(clientId, startTime, endTime);

        // Calculate total dwell time and count per location
        Map<Long, Double> totalDwellHours = new HashMap<>();
        Map<Long, Integer> dwellCounts = new HashMap<>();

        for (DwellTimeProjection dwell : dwellTimes) {
            if (dwell.getDropTime() != null && dwell.getPickupTime() != null) {
                long dwellMinutes = ChronoUnit.MINUTES.between(dwell.getDropTime(), dwell.getPickupTime());
                double dwellHours = dwellMinutes / 60.0;
                Long locationId = dwell.getLocationId();

                totalDwellHours.merge(locationId, dwellHours, Double::sum);
                dwellCounts.merge(locationId, 1, Integer::sum);
            }
        }

        // Fetch all locations for the client
        List<Location> activeLocations = locationRepository.findActiveLocationsByClientId(clientId);
        List<LocationDwellTimeDTO> result = new ArrayList<>();

        // Calculate average dwell time per location
        for (Location location : activeLocations) {
            LocationDwellTimeDTO dto = new LocationDwellTimeDTO();
            dto.setLocationId(location.getId());
            dto.setLocationName(location.getLocationName());
            Double totalHours = totalDwellHours.getOrDefault(location.getId(), 0.0);
            Integer count = dwellCounts.getOrDefault(location.getId(), 0);

            double averageDwellTime = (count > 0) ? (totalHours / count / 7.0) : 0.0;
            dto.setAverageDwellTimeHours(averageDwellTime);
            result.add(dto);
        }

        // Cache the result
        dwellTimeCacheMap.put(cacheKey, new DwellTimeCache(clientId, startDate, endDate, result));

        return result;
    }


    private String generateCacheKey(String clientId, LocalDate startDate, LocalDate endDate) {
        return clientId + "::" + startDate.toString() + "::" + endDate.toString();
    }
}