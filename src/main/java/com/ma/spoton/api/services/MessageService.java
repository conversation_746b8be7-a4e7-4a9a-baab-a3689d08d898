package com.ma.spoton.api.services;

import java.util.Optional;
import org.springframework.data.domain.Pageable;
import com.ma.spoton.api.constants.MessageMode;
import com.ma.spoton.api.dtos.MessageDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.requests.ContactUsRequest;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.requests.MessageStatusRequest;
import com.querydsl.core.types.Predicate;

public interface MessageService {

  void createMessage(String fromUserId, MessageRequest messageRequest);

  PagedResponse<MessageDto> getMessages(MessageMode messageMode, Predicate predicate,
      Pageable pageable, String userId, String timeZone);

  MessageDto getMessage(String messageId, String userId, String timeZone);

  void updateMessageStatus(String messageId, MessageStatusRequest messageStatusRequest,
      String toUserId);

  void deleteMessage(String messageId, Optional<String> fromUserId);
  
  void contactEmail(String fromUserId, ContactUsRequest contactUsRequest);

}
