package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAvailabilityDto;
import com.ma.spoton.api.dtos.UserAvailabilityExceptionDto;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.requests.UserAvailbilityExceptionRequest;
import com.querydsl.core.types.Predicate;

public interface UserAvailabilityExceptionService {

    UserAvailabilityException addUserAvailabilityException(UserAvailbilityExceptionRequest userAvailabilityExceptionRequest);
	
	List<UserAvailabilityExceptionDto> getUserAvailabilityException(String userId);
	
	void deleteUserAvailabilityException(String userAvailabilityExceptionId);
}
