package com.ma.spoton.api.services;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAggTotalNumMovesResponse;
import com.ma.spoton.api.dtos.UserTotalNumMovesResponse;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.requests.JobStatusRequest;
import com.querydsl.core.types.Predicate;

public interface JobService {

    Job createJobNow(JobRequest jobRequest, String timeZone);

    Job createScheduledJob(JobRequest jobRequest, String timeZone, String scheduleDateTime);

    void updateJob(String jobId, JobRequest jobRequest, Optional<String> userId, String timeZone);

    void rescheduleJob(String jobId, JobRequest jobRequest, Optional<String> userId, String timeZone,
                       String scheduleDateTime);


    PagedResponse<JobDto> getJobs(String fromDate, String toDate, Predicate predicate,
                                  Pageable pageable, String timeZone, List<String> clientIds,
                                  Optional<String> assignedToUserId,
                                  Optional<String> createdByUserId, String notesOrAsn, String roleNames, String userIds,
                                  Boolean trailerHistory, String jobstatus, Bucket bucketType);

    PagedResponse<JobDto> getJobsForAvgMovesClient(String fromDate, String toDate, Predicate predicate,
                                                   Pageable pageable, String timeZone, List<String> clientIds,
                                                   Optional<String> assignedToUserId,
                                                   Optional<String> createdByUserId, String notesOrAsn);

    void updateJobStatus(String jobId, JobStatusRequest jobStatusRequest, String timeZone,
                         Optional<String> assignedToUserId, Optional<String> createdByUserId);

    Resource exportJobsAsCSV(String fromDate, String toDate, Predicate predicate, String timeZone,
                             List<String> clientIds, Optional<String> assignedToUserId,
                             Optional<String> createdByUserId, Boolean isSupervisorOrClient);

    Resource exportJobsAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone,
                               List<String> clientIds, Optional<String> assignedToUserId,
                               Optional<String> createdByUserId, Boolean isSupervisorOrClient);

    Resource exportTrailerHistorysAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone);

    Resource exportTrailerHistoryAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone);

    JobDto getDriverJob(String userId, String timeZone);

    List<JobDto> getSpotterJobs(String userId, String timeZone);

    void deleteFleet(String jobId);

    void assignJobFromQueue();

    void changeJobQueuePositions(String clientId, Bucket bucket, Long dragIndex, Long dopIndex);

    ResponseEntity<Map<String, UserTotalNumMovesResponse>> fetchAverageMoves(Predicate predicate, Pageable pageable);

    ResponseEntity<Map<String, Map<String, Integer>>> fetchAverageMoves(String timeZone, String clientId);

    ResponseEntity<Map<String, List<UserAggTotalNumMovesResponse>>> fetchAverageMovesForClient(Predicate predicate,
                                                                                               Pageable pageable,
                                                                                               String clientId);

    void reassignBucketJobs();

    Resource exportMovesAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone, String roleNames,
                                String userIds, String jobstatus, Bucket bucketType);

    void queueScheduledJobs();

    Resource exportMovesAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone, String role,
                              String roleNames, String userIds, String jobstatus, Bucket bucketType);

    void updateJobPriority();

    void updateRouteStatus(String jobId, String routeId, JobStatusRequest jobStatusRequest, String userId,
                           String timeZone);

    JobDto getRecentJobDetails(String userId, String timeZone);
}
