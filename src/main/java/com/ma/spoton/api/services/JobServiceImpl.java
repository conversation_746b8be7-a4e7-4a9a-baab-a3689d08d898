package com.ma.spoton.api.services;

import static com.ma.spoton.api.constants.ConfigurableType.FleetStatusConstants.FULL;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.DRIVER;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.SPOTTER;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.requests.JobRequest.IntermediateRoute;
import static com.ma.spoton.api.services.FleetServiceImpl.PETERBILT_ONSITE_YARDS;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ExceptionHandler;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.ma.spoton.api.cache.configurabletype.FleetStatusCacheLoader;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.JobExportDto;
import com.ma.spoton.api.dtos.JobExportWithoutAsnDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAggTotalNumMovesResponse;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserTotalNumMovesResponse;
import com.ma.spoton.api.entities.BaseEntity;
import com.ma.spoton.api.entities.Bol;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.entities.Job.Priority;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.entities.JobRouteStep;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.TrailerLog;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.entities.UserAvailabilityException.Type;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.JobMapper;
import com.ma.spoton.api.repositories.BolRepository;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.MessageRepository;
import com.ma.spoton.api.repositories.OverTimeRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import com.ma.spoton.api.repositories.TrailerLogRepository;
import com.ma.spoton.api.repositories.UserAvailabilityExceptionRepository;
import com.ma.spoton.api.repositories.UserAvailabilityRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.requests.JobStatusRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.utils.CSVUtils;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.utils.RoleUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class JobServiceImpl implements JobService {

    private static Boolean isJobUpdated;
    @Value("${application.cdn1}")
    private String cdn;
    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private LocationRepository locationRepository;
    @Autowired
    private SpotRepository spotRepository;
    @Autowired
    private FleetRepository fleetRepository;
    @Autowired
    private MessageRepository messageRepository;
    @Autowired
    private BolRepository bolRepository;
    @Autowired
    private TrailerLogRepository trailerLogRepository;
    @Autowired
    private JobMapper jobMapper;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private WeatherServiceImpl weatherServiceImpl;
    @Autowired
    private UserAvailabilityRepository userAvailabilityRepository;
    @Autowired
    private UserAvailabilityExceptionRepository userAvailabilityExceptionRepository;
    @Autowired
    private OverTimeRepository overTimeRepository;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    private SocketServiceImpl socketServiceImpl;
    @Autowired
    private FleetStatusCacheLoader fleetStatusCacheLoader;

    private static void addCellInPDFWithHeading(PdfPTable table, String heading, com.itextpdf.text.Font font,
                                                BaseColor backgroundColor, BaseColor borderColor) {
        PdfPCell cell = new PdfPCell(new Phrase(heading, font));
        cell.setBackgroundColor(backgroundColor);
        cell.setBorderColor(borderColor);
        cell.setFixedHeight(10f);
        cell.setNoWrap(false);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        table.addCell(cell);
    }

    @Override
    @Transactional
    public Job createScheduledJob(JobRequest jobRequest, String timeZone, String scheduleDate) {
        if (Bucket.BUCKET_DRIVER.equals(jobRequest.getBucket()) ||
            Bucket.BUCKET_SPOTTER.equals(jobRequest.getBucket())) {
            return executeJob(jobRequest, timeZone, Optional.of(scheduleDate), true);
        } else {
            throw new ServiceException(ErrorCode.JOB_SCHEDULED_NO_BUCKET_USER_ERROR);
        }
    }

    @Override
    @Transactional
    public Job createJobNow(JobRequest jobRequest, String timeZone) {
        return executeJob(jobRequest, timeZone, Optional.empty(), false);
    }

    @NotNull
    private Job executeJob(@NotNull JobRequest jobRequest,
                           String timeZone,
                           Optional<String> scheduleStartDate,
                           Boolean isScheduled) {

        String requiredRole = null;
        Boolean isJobInQueue = false;
        Location pickupLocation = locationRepository.findActiveByUuid(jobRequest.getPickupLocationId())
                .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                        jobRequest.getPickupLocationId()));

        Location dropLocation = isNotBlank(jobRequest.getDropLocationId())
                                ? locationRepository.findActiveByUuid(jobRequest.getDropLocationId())
                                        .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                                                jobRequest.getDropLocationId()))
                                : null;

        if (jobRequest.getDescription() != null) {
            if (!(jobRequest.getDescription().isEmpty())) {
                UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
                String loggedinUserRole = RoleUtils.getRole(loggedInUser);
                jobRequest.setDescription(loggedinUserRole + ":" + jobRequest.getDescription());
            }
        }

        if (jobRequest.getBucket().equals(Bucket.BUCKET_DRIVER)) {
            requiredRole = DRIVER;
        } else if (jobRequest.getBucket().equals(Bucket.BUCKET_SPOTTER)) {
            requiredRole = SPOTTER;
        }
        Fleet fleet = fleetRepository.findActiveByUuid(jobRequest.getFleetId()).orElseThrow(
                () -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, jobRequest.getFleetId()));
        if (Objects.nonNull(jobRequest.getFleetStatus())) {
            fleet.setFleetStatus(fleetStatusCacheLoader.getFleetStatus(
                    pickupLocation.getClient().getUuid(), jobRequest.getFleetStatus()));
        }

        Spot pickupSpot = spotRepository.findActiveByUuid(jobRequest.getPickupSpotId()).orElseThrow(
                () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, jobRequest.getPickupSpotId()));

        Spot dropSpot = isNotBlank(jobRequest.getDropSpotId())
                        ? spotRepository.findActiveByUuid(jobRequest.getDropSpotId()).orElseThrow(
                () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, jobRequest.getDropSpotId()))
                        : null;

        Job job = createJobWithUserNotification(jobRequest, pickupLocation, dropLocation, fleet, pickupSpot, dropSpot,
                                                requiredRole, timeZone,
                                                isJobInQueue, isScheduled, scheduleStartDate);

        // Trailer activities logged
        TrailerLog log = new TrailerLog();
        log.setFleet(fleet);
        log.setIsActive(true);
        log.setJob(job);
        trailerLogRepository.save(log);

        // Updating spot status
        pickupSpot.getPickUpJobs().add(job);

        if (Objects.nonNull(dropSpot)) {
            dropSpot.getDropOffJobs().add(job);
        }

        if (jobRequest.getMessageId() != null && !jobRequest.getMessageId().trim().isEmpty()) {
            Message message = messageRepository.findActiveByUuid(jobRequest.getMessageId()).orElseThrow(
                    () -> new ServiceException(ErrorCode.MESSAGE_NOT_FOUND, jobRequest.getMessageId()));
            message.setJob(job);
            if (jobRequest.getReplyUserId() != null && !jobRequest.getReplyUserId().trim().isEmpty()) {
                User toUser = userRepository.findActiveByUuid(jobRequest.getReplyUserId()).orElseThrow(
                        () -> new ServiceException(ErrorCode.USER_NOT_FOUND, jobRequest.getReplyUserId()));
                notificationService.createJobFromMessageNotification(job, toUser, timeZone);
            }
        }
        return job;
    }

    @NotNull
    @ExceptionHandler(Exception.class)
    private Job saveJob(JobRequest jobRequest,
                        String requiredRole,
                        Boolean isJobInQueue,
                        User user,
                        Location pickupLocation,
                        Fleet fleet,
                        Spot pickupSpot,
                        Location dropLocation,
                        Spot dropSpot,
                        ZonedDateTime startDateTime,
                        Boolean isScheduled) {

        Job job = jobMapper.mapToEntity(jobRequest);
        // ignoring in Mapper but setting here.
        if (StringUtils.isNotBlank(jobRequest.getFleetStatus())) {
            job.setFleetStatus(fleetStatusCacheLoader.getFleetStatus(pickupLocation.getClient().getUuid(),
                                                                     jobRequest.getFleetStatus()));
        }

        // Set job bucket based on role
        if (requiredRole == null) {
            job.setBucket(Bucket.NIL);
        } else if (DRIVER.equals(requiredRole)) {
            job.setBucket(Bucket.BUCKET_DRIVER);
        } else if (SPOTTER.equals(requiredRole)) {
            job.setBucket(Bucket.BUCKET_SPOTTER);
        }

        if (!isJobInQueue) { // Handle job assignment or queueing
            if (isScheduled) {
                setScheduledStatus(job, startDateTime);
                // job.setAssignedTo(user); // Needed for notification.
            } else {
                job.setAssignedTo(user);
                job.setIsScheduled(false);
                job.setStatus(Status.OPEN);
                job.setAssignedAt(ZonedDateTime.now());
            }
        } else {
            Job lastInQueueJob = jobRepository.findLastInQueueByClient(
                    pickupLocation.getClient().getUuid(),
                    jobRequest.getBucket());
            //Handle schedule job
            if (isScheduled) {
                setScheduledStatus(job, startDateTime);
            } else {
                job.setStatus(Status.QUEUE);
                job.setIsScheduled(false);
            }
            long nextPosition = (lastInQueueJob != null)
                                ? lastInQueueJob.getQueuePosition() + 1
                                : 0L;
            job.setQueuePosition(nextPosition);
        }

        // Set job details
        job.setFleet(fleet);
        job.setPickupLocation(pickupLocation);
        job.setPickupSpot(pickupSpot);
        job.setDropLocation(dropLocation);
        job.setDropSpot(dropSpot);
        job.setPriorityUpdatedAt(ZonedDateTime.now());

        if (jobRequest.isBoxTruckOrVan()) {
            Set<JobRouteStep> intermediateRoutes = createIntermediateRoutes(job, jobRequest.getRouteList());
            // reserve and update spots for intermediate.
            reserveAndUpdateSpotsForTruckJob(job, intermediateRoutes);
            job.setJobRouteSteps(intermediateRoutes);
        }

        // Save and return job
        return jobRepository.save(job);
    }

    private void reserveAndUpdateSpotsForTruckJob(Job job, Set<JobRouteStep> intermediateRoutes) {
        for (JobRouteStep route : intermediateRoutes) {
            route.getSpot().setStatus(Spot.Status.TO_BE_OCCUPIED);

            JobRouteStep.StepType stepType = route.getStepType();
            if (JobRouteStep.StepType.PICK_UP.equals(stepType)) {
                route.getSpot().getPickUpJobs().add(job);
            } else {
                route.getSpot().getDropOffJobs().add(job);
            }
        }
    }

    private Set<JobRouteStep> createIntermediateRoutes(Job job, List<JobRequest.IntermediateRoute> routeList) {
        Set<JobRouteStep> intermediateRoutes = new LinkedHashSet<>();
        int stepOrder = 0;

        // perf improvement. prefetch all (list ordered) locations & spots in one query instead of 'n' queries.
        Set<String> locationIds =
                routeList.stream().map(JobRequest.IntermediateRoute::getLocationId).collect(Collectors.toSet());
        List<Location> locations = locationRepository.findActiveLocationsByIdsOrdered(locationIds);
        Map<String, Location> locationMap = locations.stream()
                .collect(Collectors.toMap(Location::getUuid, Function.identity()));

        Set<String> spotIds =
                routeList.stream().map(JobRequest.IntermediateRoute::getSpotId).collect(Collectors.toSet());
        List<Spot> spots = spotRepository.findActiveSpotsByIdsOrdered(spotIds);
        Map<String, Spot> spotMap = spots.stream()
                .collect(Collectors.toMap(Spot::getUuid, Function.identity()));

        for (IntermediateRoute route : routeList) {
            JobRouteStep jobRouteStep = new JobRouteStep();
            jobRouteStep.setJob(job);

            Location location = locationMap.get(route.getLocationId());
            if (location == null) {
                throw new ServiceException(ErrorCode.LOCATION_NOT_FOUND, route.getLocationId());
            }
            jobRouteStep.setLocation(location);

            Spot spot = spotMap.get(route.getSpotId());
            if (spot == null) {
                throw new ServiceException(ErrorCode.SPOT_NOT_FOUND, route.getSpotId());
            }
            jobRouteStep.setSpot(spot);

            jobRouteStep.setStepType(route.getType());
            jobRouteStep.setStepOrder(stepOrder++);
            jobRouteStep.setStepStatus(JobRouteStep.StepStatus.PENDING);// Initial status
            route.getNotes().ifPresent(jobRouteStep::setNotes);

            intermediateRoutes.add(jobRouteStep);
        }
        return intermediateRoutes;
    }

    @NotNull
    private Job createJobWithUserNotification(JobRequest jobRequest,
                                              Location pickupLocation,
                                              Location dropLocation,
                                              Fleet fleet,
                                              Spot pickupSpot,
                                              Spot dropSpot,
                                              String requiredRole,
                                              String timeZone,
                                              Boolean isJobInQueue,
                                              Boolean isScheduled,
                                              Optional<String> scheduleStartTime) {
        User user = null;
        ZonedDateTime scheduleDateTime = getScheduleDateTime(timeZone, scheduleStartTime);
        User firstUserInSet = null;
        if (isNotBlank(jobRequest.getAssignedToUserId())) {
            user = userRepository.findActiveByUuid(jobRequest.getAssignedToUserId()).orElseThrow(
                    () -> new ServiceException(ErrorCode.USER_NOT_FOUND, jobRequest.getAssignedToUserId()));
        } else {

            Client client = pickupLocation.getClient();
            String clientTimeZone = client.getTimeZone();
            String clientId = client.getUuid();
            ZonedDateTime now = getScheduleDateTime(clientTimeZone, scheduleStartTime);
            DayOfWeek today = now.getDayOfWeek();
            LocalTime time = now.toLocalTime();
            LocalDate date = now.toLocalDate();

            List<UserAvailability> userAvailabilities =
                    userAvailabilityRepository.findPresentUserAvailabilities(today, time, requiredRole, clientId);
            List<UserAvailabilityException> additionalUsersInAvailabilities =
                    userAvailabilityExceptionRepository.getAdditionalWorkingUsers(date, requiredRole, time, clientId);
            List<OverTime> overTimeUsers =
                    overTimeRepository.getOverTimeUsers(date, requiredRole, time, clientId);

            Set<User> unWantedSpotters = new HashSet<>();
            if (Bucket.BUCKET_SPOTTER.equals(jobRequest.getBucket())) {
                addToUnwantedSpotters(userAvailabilities, additionalUsersInAvailabilities, overTimeUsers,
                                      pickupLocation,
                                      dropLocation, unWantedSpotters);
            }

            Set<User> availableUsers = new HashSet<>();
            populateAvailableUsers(userAvailabilities, additionalUsersInAvailabilities, overTimeUsers,
                                   unWantedSpotters, date, time, clientId, requiredRole, availableUsers);

            firstUserInSet = availableUsers.stream().findFirst().orElse(null);
            if (firstUserInSet == null) {
                isJobInQueue = true;
            } else {
                if (firstUserInSet.getIdleSince() == null) {
                    ZonedDateTime present = ZonedDateTime.now(ZoneId.of(firstUserInSet.getTimeZone()));
                    User user1 = userRepository.findByUserUuid(firstUserInSet.getUuid());
                    user1.setIdleSince(present);
                    user1.setIsIdleClear(false);
                    userRepository.save(user1);
                    firstUserInSet.setIdleSince(present);
                }

                for (User user1 : availableUsers) {
                    if (user1.getIdleSince() == null) {
                        ZonedDateTime present = ZonedDateTime.now(ZoneId.of(user1.getTimeZone()));
                        User user2 = userRepository.findByUserUuid(user1.getUuid());
                        user2.setIdleSince(present);
                        user2.setIsIdleClear(false);
                        userRepository.save(user2);
                        user1.setIdleSince(present);
                    }
                    if (firstUserInSet.getIdleSince().isAfter(user1.getIdleSince())) {
                        firstUserInSet = user1;
                    }
                }
                String uuid = firstUserInSet.getUuid();
                user = userRepository.findActiveByUuid(uuid).orElseThrow(
                        () -> new ServiceException(ErrorCode.USER_NOT_FOUND, uuid));

            }
        }

        if (user != null) {
            if (user.getRoles().stream()
                    .noneMatch(role -> role.getRoleName().equalsIgnoreCase(SPOTTER)
                                       || role.getRoleName().equalsIgnoreCase(DRIVER)
                                       || role.getRoleName().equalsIgnoreCase(SUPERVISOR))) {
                throw new ServiceException(ErrorCode.CREATE_UPDATE_JOB_INVALID_ASSIGNED_TO);
            }
        }

        Job job = saveJob(jobRequest, requiredRole, isJobInQueue, user, pickupLocation, fleet, pickupSpot, dropLocation,
                          dropSpot, scheduleDateTime, isScheduled);

        if (user != null && !isScheduled) { // scheduled will not be assigned , do we need to send notification?
            // Creating notification
            notificationService.createNewJobNotification(job);
        }
        return job;
    }

    @Override
    @Transactional
    public void updateJob(String jobId, JobRequest jobRequest, Optional<String> userId, String timeZone) {
        executeUpdateJob(jobId, timeZone, jobRequest, userId, Optional.empty());
    }

    @Override
    @Transactional
    public void rescheduleJob(String jobId, JobRequest jobRequest, Optional<String> userId, String timeZone,
                              String rescheduleDateTime) {
        executeUpdateJob(jobId, timeZone, jobRequest, userId, Optional.of(rescheduleDateTime));
    }

    private void executeUpdateJob(String jobId,
                                  String timeZone,
                                  @NotNull JobRequest jobRequest,
                                  Optional<String> userId,
                                  Optional<String> rescheduleDateTime) {

        Boolean resetQueuePosition = false;
        Long queuePosition = null;
        Bucket bucket = Bucket.NIL;
        String clientId = null;
        Boolean sameBucket = false;
        Boolean jobPriorityChange = false;

        String description = null;
        Job job = jobRepository.findByUuid(jobId)
                .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, jobId));

        if (!job.getPriority().equals(jobRequest.getPriority())) {
            jobPriorityChange = true;
        }

        if (Objects.nonNull(jobRequest.getIsEdit())) {
            if (jobRequest.getIsEdit().equals(true) && (job.getStatus().equals(Status.IN_TRANSIT) || job.getStatus()
                    .equals(Status.COMPLETED))) {
                throw new ServiceException(ErrorCode.UPDATE_STARTED_JOB);
            }
        }

        if (job.getBucket() != null) {
            //Checking whether the present job is bucket system
            if (job.getBucket().equals(Bucket.BUCKET_DRIVER) || job.getBucket().equals(Bucket.BUCKET_SPOTTER)) {
                //checking whether the present job bucket is not the same bucket or not in bucket system
                if (!job.getBucket().equals(jobRequest.getBucket())) {
                    queuePosition = job.getQueuePosition();
                    resetQueuePosition = true;
                    bucket = job.getBucket();
                    clientId = job.getPickupLocation().getClient().getUuid();
                } else {
                    queuePosition = job.getQueuePosition();
                    sameBucket = true;
                }
            }
        }

        if (userId.isPresent() && !userId.get().equals(job.getAssignedTo().getUuid())) {
            throw new ServiceException(ErrorCode.DRIVER_JOB_CREATE_UPDATE_ACCESS);
        }

        if (jobRequest.getIsEdit() != null) {
            if (jobRequest.getDescription() != null) {
                if (!(jobRequest.getDescription().isEmpty())) {
                    UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
                    String loggedinUserRole = RoleUtils.getRole(loggedInUser);
                    jobRequest.setDescription(loggedinUserRole + ":" + jobRequest.getDescription());
                }
            }
        } else {
            description = job.getDescription();

        }

        job = jobMapper.updateEntity(jobRequest, job);

        if (description != null) {
            job.setDescription(description);
        }
        if (!jobRequest.getAssignedToUserId().isEmpty()) {
            User user = userRepository.findActiveByUuid(jobRequest.getAssignedToUserId()).orElseThrow(
                    () -> new ServiceException(ErrorCode.USER_NOT_FOUND, jobRequest.getAssignedToUserId()));
            if (user.getRoles().stream()
                    .noneMatch(role -> role.getRoleName().equalsIgnoreCase(SPOTTER)
                                       || role.getRoleName().equalsIgnoreCase(DRIVER)
                                       || role.getRoleName().equalsIgnoreCase(SUPERVISOR))) {
                throw new ServiceException(ErrorCode.CREATE_UPDATE_JOB_INVALID_ASSIGNED_TO);
            }

            if (Objects.nonNull(job.getAssignedTo())) {

                if (!(job.getAssignedTo().getUuid().equals(jobRequest.getAssignedToUserId()))) {

                    job.setAssignedAt(ZonedDateTime.now());
                }

            } else {

                job.setAssignedAt(ZonedDateTime.now());
            }
            // only allow scheduled jobs to be rescheduled and not queued jobs.
            if (rescheduleDateTime.isPresent() && job.getStatus().equals(Status.SCHEDULED)) {
                ZonedDateTime scheduleDateTime = getScheduleDateTime(timeZone, rescheduleDateTime);
                setScheduledStatus(job, scheduleDateTime);

            } else {
                job.setAssignedAt(ZonedDateTime.now());
                job.setScheduleDateTime(null);
                job.setIsScheduled(false);
                job.setStatus(Status.OPEN);
            }
            job.setAssignedTo(user);
            job.setQueuePosition(null);

        } else {
            job.setAssignedTo(null);
            job.setAssignedAt(null);

            if (sameBucket == false) {
                Job lastInQueueJob = jobRepository.findLastInQueueByClient(
                        locationRepository.findActiveByUuid(jobRequest.getPickupLocationId()).get().getClient()
                                .getUuid(), jobRequest.getBucket());
                if (lastInQueueJob != null) {
                    Long pos = lastInQueueJob.getQueuePosition() + 1;
                    job.setQueuePosition(pos);
                } else {
                    job.setQueuePosition(0L);
                }
            } else {
                job.setQueuePosition(queuePosition);
            }
            if (rescheduleDateTime.isPresent()) {
                ZonedDateTime scheduleDateTime = getScheduleDateTime(timeZone, rescheduleDateTime);
                setScheduledStatus(job, scheduleDateTime);
            } else {
                job.setStatus(Status.QUEUE);
            }
        }
        job.setBucket(jobRequest.getBucket());

        if (!job.getPickupLocation().getUuid().equals(jobRequest.getPickupLocationId())) {
            job.setPickupLocation(locationRepository.findActiveByUuid(jobRequest.getPickupLocationId())
                                          .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                                                  jobRequest.getPickupLocationId())));
        }

        if (!job.getFleet().getUuid().equals(jobRequest.getFleetId())) {
            Fleet fleet = fleetRepository.findActiveByUuid(jobRequest.getFleetId()).orElseThrow(
                    () -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, jobRequest.getFleetId()));
            if (Objects.nonNull(jobRequest.getFleetStatus())) {
                fleet.setFleetStatus(fleetStatusCacheLoader.getFleetStatus(
                        job.getPickupLocation().getClient().getUuid(), jobRequest.getFleetStatus()));
            }
            job.setFleet(fleet);
        }

        if (!job.getPickupSpot().getUuid().equals(jobRequest.getPickupSpotId())) {

            Spot pickupSpot = spotRepository.findActiveByUuid(job.getPickupSpot().getUuid())
                    .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, "1"));
            Set<Long> pickupJobIds =
                    pickupSpot.getPickUpJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
            pickupSpot.getPickUpJobs().clear();
            pickupJobIds.remove(job.getId());
            Set<Job> pickupJobs = jobRepository.findAllByJobIds(pickupJobIds);
            pickupSpot.getPickUpJobs().addAll(pickupJobs);
            spotRepository.save(pickupSpot);

            job.setPickupSpot(spotRepository.findActiveByUuid(jobRequest.getPickupSpotId()).orElseThrow(
                    () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, jobRequest.getPickupSpotId())));
            job.getPickupSpot().getPickUpJobs().add(job);
        }

        if (Objects.isNull(job.getDropLocation())
            || !job.getDropLocation().getUuid().equals(jobRequest.getDropLocationId())) {
            job.setDropLocation(isNotBlank(jobRequest.getDropLocationId())
                                ? locationRepository.findActiveByUuid(jobRequest.getDropLocationId())
                                        .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                                                jobRequest.getDropLocationId()))
                                : null);
        }

        if (isNotBlank(jobRequest.getDropSpotId())) {
            if (job.getDropSpot() != null) {
                if (!job.getDropSpot().getUuid().equals(jobRequest.getDropSpotId())) {

                    Spot dropSpot = spotRepository.findActiveByUuid(job.getDropSpot().getUuid())
                            .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, "1"));
                    Set<Long> dropOffJobIds =
                            dropSpot.getDropOffJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
                    dropSpot.getDropOffJobs().clear();
                    dropOffJobIds.remove(job.getId());
                    Set<Job> dropOffJobs = jobRepository.findAllByJobIds(dropOffJobIds);
                    dropSpot.getDropOffJobs().addAll(dropOffJobs);
                    spotRepository.save(dropSpot);
                    job.setDropSpot(isNotBlank(jobRequest.getDropSpotId())
                                    ? spotRepository.findActiveByUuid(jobRequest.getDropSpotId()).orElseThrow(
                            () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, jobRequest.getDropSpotId()))
                                    : null);
                    job.getDropSpot().getDropOffJobs().add(job);


                }
            } else {

                job.setDropSpot(isNotBlank(jobRequest.getDropSpotId())
                                ? spotRepository.findActiveByUuid(jobRequest.getDropSpotId()).orElseThrow(
                        () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, jobRequest.getDropSpotId()))
                                : null);
                job.getDropSpot().getDropOffJobs().add(job);
            }
        } else {
            job.setDropSpot(null);
        }

        if (Objects.nonNull(jobRequest.getFleetStatus())) {
            // ignoring in Mapper but setting here.
            FleetStatus fleetStatus = fleetStatusCacheLoader.getFleetStatus(
                    job.getPickupLocation().getClient().getUuid(), jobRequest.getFleetStatus());
            job.getFleet().setFleetStatus(fleetStatus);
            job.setFleetStatus(fleetStatus);
        }
        if (jobPriorityChange.equals(true)) {
            job.setPriorityUpdatedAt(ZonedDateTime.now());
        }

        // Creating notification
        if (!jobRequest.getAssignedToUserId().isEmpty()) {
            notificationService.createUpdateJobNotification(job);
        }

        if (resetQueuePosition == true) {

            List<Job> remainingJobsInQueue =
                    jobRepository.findAllJobsAfterQueuePosition(clientId, bucket, queuePosition);

            for (Job job1 : remainingJobsInQueue) {
                Job requiredJob = jobRepository.findByUuid(job1.getUuid())
                        .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, job1.getUuid()));
                requiredJob.setQueuePosition(queuePosition);
                jobRepository.save(requiredJob);
                queuePosition = queuePosition + 1L;
            }
        }

        // modify the routes based on new/removal/order change of incoming job routes.
        if (job.isBoxTruckOrVan()) {
            List<IntermediateRoute> intermediateRoutes = jobRequest.getRouteList();

            Set<JobRouteStep> routeSteps = createIntermediateRoutes(job, intermediateRoutes);
            job.getJobRouteSteps().clear();
            job.setJobRouteSteps(routeSteps);
        }

        // trailer log recording for editing trailer related data.
        TrailerLog log = trailerLogRepository.findByJobId(job.getId())
                .orElseThrow(() -> new ServiceException(ErrorCode.TRAILER_LOG_NOT_FOUND, "1"));
        if (!log.getFleet().getUuid().isEmpty()) {
            log.setFleet(job.getFleet());
        }
    }

    private ZonedDateTime getScheduleDateTime(String clientTimeZone,
                                              Optional<String> scheduleStartTime) {
        return scheduleStartTime
                .map(startDate -> DateTimeUtils.convertStringIntoLocalDateTime(startDate,
                                                                               BusinessConstants.FORM_DATE_TIME_FORMAT,
                                                                               ZoneId.of(clientTimeZone),
                                                                               ZoneId.systemDefault()))
                .orElse(ZonedDateTime.now(ZoneId.of(clientTimeZone))); // fallback if not present
    }

    private void setScheduledStatus(Job job, ZonedDateTime scheduleDateTime) {
        job.setAssignedAt(scheduleDateTime);
        job.setStatus(Status.SCHEDULED);
        job.setIsScheduled(true);
        job.setScheduleDateTime(scheduleDateTime);
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<JobDto> getJobs(String fromDate, String toDate, Predicate predicate,
                                         Pageable pageable, String timeZone, List<String> clientIds,
                                         Optional<String> assignedToUserId,
                                         Optional<String> createdByUserId, String notesOrAsn, String roleNames,
                                         String userIds, Boolean trailerHistory, String jobstatus, Bucket bucketType) {
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        if (assignedToUserId.isPresent() && createdByUserId.isEmpty()) {
            BooleanExpression assignedToUserCriteria =
                    QJob.job.assignedTo.uuid.eq(assignedToUserId.get());
            predicate = Objects.nonNull(predicate) ? assignedToUserCriteria.and(predicate)
                                                   : assignedToUserCriteria;
        } else if (createdByUserId.isPresent() && assignedToUserId.isEmpty()) {
            BooleanExpression createdByCriteria = QJob.job.createdBy.uuid.eq(createdByUserId.get());
            predicate = Objects.nonNull(predicate) ? createdByCriteria.and(predicate) : createdByCriteria;
        } else if (createdByUserId.isPresent() && assignedToUserId.isPresent()) {
            if (AuthDetailsProvider.isRolePresent(GUARD)) {
                BooleanExpression createdByOrAssignedToCriteria = QJob.job.createdBy.uuid
                        .eq(createdByUserId.get()).or(QJob.job.assignedTo.uuid.eq(assignedToUserId.get()));
                predicate = Objects.nonNull(predicate) ? createdByOrAssignedToCriteria.and(predicate)
                                                       : createdByOrAssignedToCriteria;
            } else {
                BooleanExpression createdByOrAssignedToCriteria = QJob.job.createdBy.uuid
                        .eq(createdByUserId.get()).and(QJob.job.assignedTo.uuid.eq(assignedToUserId.get()));
                predicate = Objects.nonNull(predicate) ? createdByOrAssignedToCriteria.and(predicate)
                                                       : createdByOrAssignedToCriteria;
            }
        }
        if (CollectionUtils.isNotEmpty(clientIds)) {
            BooleanExpression clientsCriteria = QJob.job.pickupLocation.client.uuid.in(clientIds);
            predicate = Objects.nonNull(predicate) ? clientsCriteria.and(predicate) : clientsCriteria;
        }

        if (isNotBlank(notesOrAsn)) {
            BooleanExpression notesOrAsnCriteria = QJob.job.description.contains(notesOrAsn)
                    .or(QJob.job.pickupNotes.contains(notesOrAsn)).or(QJob.job.dropNotes.contains(notesOrAsn)).
                    or(QJob.job.sequenceAsn.contains(notesOrAsn));
            predicate = Objects.nonNull(predicate) ? notesOrAsnCriteria.and(predicate) : notesOrAsnCriteria;
        }

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }

        if (userIds != null && !userIds.isBlank()) {
            List<String> usersIdsList = Arrays.asList(userIds.split(","));

            BooleanExpression userFilter = QJob.job.assignedTo.uuid.in(usersIdsList);
            predicate = (predicate == null) ? userFilter : ((BooleanExpression) predicate).and(userFilter);
        }

        if (jobstatus != null && !jobstatus.isBlank()) {
            List<Job.Status> jobstatusList = Arrays.stream(jobstatus.split(","))
                    .map(String::trim)
                    .map(s -> Job.Status.valueOf(s.toUpperCase()))
                    .collect(Collectors.toList());

            BooleanExpression combinedFilter;

            if (jobstatusList.contains(Job.Status.QUEUE)) {

                jobstatusList.remove(Job.Status.QUEUE);

                BooleanExpression queueFilter = QJob.job.status.eq(Job.Status.QUEUE)
                        .and(QJob.job.bucket.eq(bucketType));

                if (!jobstatusList.isEmpty()) {
                    BooleanExpression statusFilter;
                    if (isNotBlank(roleNames)) {
                        List<String> roleList = Arrays.stream(roleNames.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        statusFilter = QJob.job.status.in(jobstatusList)
                                .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                    } else {
                        statusFilter = QJob.job.status.in(jobstatusList);
                    }
                    combinedFilter = queueFilter.or(statusFilter);
                } else {
                    combinedFilter = queueFilter;
                }
            } else {
                if (isNotBlank(roleNames)) {
                    List<String> roleList = Arrays.stream(roleNames.split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    combinedFilter = QJob.job.status.in(jobstatusList)
                            .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                } else {
                    combinedFilter = QJob.job.status.in(jobstatusList);
                }
            }

            predicate = predicate == null ? combinedFilter : ((BooleanExpression) predicate).and(combinedFilter);
        } else {
            if (isNotBlank(roleNames)) {
                List<String> roleList = Arrays.stream(roleNames.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

                if (!roleList.isEmpty()) {
                    BooleanExpression roleCriteria = QJob.job.assignedTo.roles.any().value.in(roleList);
                    predicate = predicate != null ? roleCriteria.and(predicate) : roleCriteria;
                }
            }
        }

        if (trailerHistory != null) {
            BooleanExpression statusPredicate = QJob.job.status.in(List.of(Status.IN_TRANSIT, Status.COMPLETED));
            predicate =
                    Objects.nonNull(predicate) ? statusPredicate.and(predicate) : statusPredicate;

        }

        BooleanBuilder builder = new BooleanBuilder();
        if (predicate != null) {
            builder.and(predicate);
        }
        // check if "isBoxTruckOrVan" was in the query params
        boolean hasBoxTruckParam = builder.getValue() != null &&
                                   builder.getValue().toString().contains("isBoxTruckOrVan");

        // safeguard existing jobs query to not include boxtruck jobs.
        // UI should send isBoxTruckOrVan = false ideally
        if (!hasBoxTruckParam) {
            builder.and(QJob.job.isBoxTruckOrVan.eq(false));
        }

        Page<Job> jobsPage = jobRepository.findAll(builder, pageable);
        jobsPage.forEach(job ->
                                 job.getBols().forEach(bol -> {
                                     String currentImagePath = bol.getImagePath();
                                     String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                     bol.setImagePath(newImagePath);
                                 })
        );
        return PagedResponse.<JobDto>builder()
                .list(jobsPage.stream().map(job -> jobMapper.mapToDto(job, timeZone))
                              .collect(Collectors.toCollection(LinkedList::new))) // maintain the order
                .page(jobsPage.getNumber()).size(jobsPage.getSize())
                .totalElements(jobsPage.getTotalElements()).build();
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<JobDto> getJobsForAvgMovesClient(String fromDate, String toDate, Predicate predicate,
                                                          Pageable pageable, String timeZone, List<String> clientIds,
                                                          Optional<String> assignedToUserId,
                                                          Optional<String> createdByUserId, String notesOrAsn) {

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        if (assignedToUserId.isPresent() && createdByUserId.isEmpty()) {
            BooleanExpression assignedToUserCriteria =
                    QJob.job.assignedTo.uuid.eq(assignedToUserId.get());
            predicate = Objects.nonNull(predicate) ? assignedToUserCriteria.and(predicate)
                                                   : assignedToUserCriteria;
        } else if (createdByUserId.isPresent() && assignedToUserId.isEmpty()) {
            BooleanExpression createdByCriteria = QJob.job.createdBy.uuid.eq(createdByUserId.get());
            predicate = Objects.nonNull(predicate) ? createdByCriteria.and(predicate) : createdByCriteria;
        } else if (createdByUserId.isPresent() && assignedToUserId.isPresent()) {
            if (AuthDetailsProvider.isRolePresent(GUARD)) {
                BooleanExpression createdByOrAssignedToCriteria = QJob.job.createdBy.uuid
                        .eq(createdByUserId.get()).or(QJob.job.assignedTo.uuid.eq(assignedToUserId.get()));
                predicate = Objects.nonNull(predicate) ? createdByOrAssignedToCriteria.and(predicate)
                                                       : createdByOrAssignedToCriteria;
            } else {
                BooleanExpression createdByOrAssignedToCriteria = QJob.job.createdBy.uuid
                        .eq(createdByUserId.get()).and(QJob.job.assignedTo.uuid.eq(assignedToUserId.get()));
                predicate = Objects.nonNull(predicate) ? createdByOrAssignedToCriteria.and(predicate)
                                                       : createdByOrAssignedToCriteria;
            }
        }
        if (CollectionUtils.isNotEmpty(clientIds)) {
            BooleanExpression clientsCriteria = QJob.job.pickupLocation.client.uuid.in(clientIds);
            predicate = Objects.nonNull(predicate) ? clientsCriteria.and(predicate) : clientsCriteria;
        }

        if (isNotBlank(notesOrAsn)) {
            BooleanExpression notesOrAsnCriteria = QJob.job.description.contains(notesOrAsn)
                    .or(QJob.job.pickupNotes.contains(notesOrAsn)).or(QJob.job.dropNotes.contains(notesOrAsn)).
                    or(QJob.job.sequenceAsn.contains(notesOrAsn));
            predicate = Objects.nonNull(predicate) ? notesOrAsnCriteria.and(predicate) : notesOrAsnCriteria;
        }

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }

        BooleanExpression driver = QJob.job.assignedTo.uuid.eq("1402ec29-ef66-4433-9862-5a2806442765");
        predicate = driver.and(predicate);

        Pageable unlimitedPageable = PageRequest.of(0, Integer.MAX_VALUE, pageable.getSort());

        Page<Job> jobsPage = jobRepository.findCompletedJobsByDateRange(
                LocalDateTime.now().minusDays(45).atZone(ZoneId.of(timeZone)),
                LocalDateTime.now().minusDays(30).atZone(ZoneId.of(timeZone)),
                unlimitedPageable);

        jobsPage.forEach(job ->
                                 job.getBols().forEach(bol -> {
                                     String currentImagePath = bol.getImagePath();
                                     String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                     bol.setImagePath(newImagePath);
                                 })
        );
        return PagedResponse.<JobDto>builder()
                .list(jobsPage.stream().map(job -> jobMapper.mapToDto(job, timeZone))
                              .collect(Collectors.toList()))
                .page(jobsPage.getNumber()).size(jobsPage.getSize())
                .totalElements(jobsPage.getTotalElements()).build();
    }

    @Override
    @Transactional
    public void updateJobStatus(String jobId, JobStatusRequest jobStatusRequest, String timeZone,
                                Optional<String> assignedToUserId, Optional<String> createdByUserId) {

        UserAvailability userAvailability = null;
        UserAvailabilityException userAvailabilityException = null;

        if (jobStatusRequest.getNotes() != null) {
            if (!(jobStatusRequest.getNotes().isEmpty())) {
                UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
                String loggedinUserRole = RoleUtils.getRole(loggedInUser);
                jobStatusRequest.setNotes(loggedinUserRole + ":" + jobStatusRequest.getNotes());
            }
        }

        Job job = jobRepository.findByUuid(jobId)
                .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, jobId));

        if (assignedToUserId.isPresent()
            && !job.getAssignedTo().getUuid().equals(assignedToUserId.get())) {
            throw new ServiceException(ErrorCode.DRIVER_JOB_CREATE_UPDATE_ACCESS);
        }

        ZonedDateTime statusDateTime = getStatusDateTime(jobStatusRequest.getStatusDateTime(), timeZone);

        if (Status.IN_TRANSIT.equals(jobStatusRequest.getStatus())) {

            /*if (!Status.OPEN.equals(job.getStatus())) {
                throw new ServiceException(ErrorCode.JOB_INVALID_STATE, job.getStatus(), jobStatusRequest.getStatus());
            }*/

            if (jobStatusRequest.getLat() != null && jobStatusRequest.getLng() != null) {
                String weather = weatherServiceImpl.getWeather(jobStatusRequest);
                JSONObject jsonObject = new JSONObject(weather);
                JSONArray weatherArray = jsonObject.getJSONArray("weather");
                JSONObject weatherObject = weatherArray.getJSONObject(0);
                String weatherMain = weatherObject.getString("main");

                JSONObject mainObject = jsonObject.getJSONObject("main");
                Double tempValue = mainObject.getDouble("temp");

                job.setClimate(weatherMain);
                job.setTemperature(tempValue);
            }

            job.setPickupDateTime(statusDateTime);
            job.setPickupNotes(jobStatusRequest.getNotes());

            // Update status of Spot
            job.getPickupSpot().setStatus(com.ma.spoton.api.entities.Spot.Status.EMPTY);
            job.getPickupSpot().setLastEmptiedTime(ZonedDateTime.now());
            job.getPickupSpot().setIsOccupied(false);

            // Remove fleet from Pickup Spot
            job.getPickupSpot().setFleet(null);

            // Remove Pickup Spot from fleet
            job.getFleet().setSpot(null);
            List<Spot> previousSpots = spotRepository.findAllByFleet(job.getFleet());
            previousSpots.forEach(spot -> {

                                      spot.setFleet(null);
                                      spot.setStatus(com.ma.spoton.api.entities.Spot.Status.EMPTY);
                                      spot.setLastEmptiedTime(ZonedDateTime.now());
                                      spot.setIsOccupied(false);

                                  }
            );
            job.getAssignedTo().setIdleSince(null);
            job.getPickupSpot().getPickUpJobs().remove(job);

            Spot pickupSpot = spotRepository.findActiveByUuid(job.getPickupSpot().getUuid())
                    .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, job.getPickupSpot().getUuid()));
            Set<Long> pickupJobIds =
                    pickupSpot.getPickUpJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
            pickupSpot.getPickUpJobs().clear();
            pickupJobIds.remove(job.getId());
            Set<Job> pickupJobs = jobRepository.findAllByJobIds(pickupJobIds);
            pickupSpot.getPickUpJobs().addAll(pickupJobs);
            spotRepository.save(pickupSpot);
        }

        if (Status.COMPLETED.equals(jobStatusRequest.getStatus())) {

            // TODO : fix UI to not call updateJob API before sending status (confirm pickup/drop)
            /*if (!Status.IN_TRANSIT.equals(job.getStatus())) {
                throw new ServiceException(ErrorCode.JOB_INVALID_STATE, job.getStatus(), jobStatusRequest.getStatus());
            }*/

            Spot dropSpot = spotRepository.findActiveByUuid(job.getDropSpot().getUuid())
                    .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, job.getDropSpot().getUuid()));
            Set<Long> dropOffJobIds =
                    dropSpot.getDropOffJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
            dropSpot.getDropOffJobs().clear();
            dropOffJobIds.remove(job.getId());
            Set<Job> dropOffJobs = jobRepository.findAllByJobIds(dropOffJobIds);
            dropSpot.getDropOffJobs().addAll(dropOffJobs);
            spotRepository.save(dropSpot);

            job.setDropDateTime(statusDateTime);
            job.setDropNotes(jobStatusRequest.getNotes());

            Spot spot = spotRepository.findByUuid(job.getDropSpot().getUuid())
                    .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, job.getDropSpot().getUuid()));

            List<Fleet> fleetBySpot = fleetRepository.findAllBySpot(spot.getId());
            fleetBySpot.forEach(fleet -> {
                fleet.setSpot(null);
            });

            // Update status of Spot
            job.getDropSpot().setStatus(com.ma.spoton.api.entities.Spot.Status.OCCUPIED);
            job.getDropSpot().setLastOccupiedTime(ZonedDateTime.now());
            job.getDropSpot().setIsOccupied(true);

            // Assign fleet to Drop Spot
            job.getDropSpot().setFleet(job.getFleet());

            // Assign Drop Spot to Fleet
            Fleet fleet = job.getFleet();
            fleet.setSpot(job.getDropSpot());
            if (PETERBILT_ONSITE_YARDS.contains(spot.getLocation().getLocationName())) {
                if (!PETERBILT_ONSITE_YARDS.contains(job.getPickupLocation().getLocationName())) {
                    fleet.setLastFleetAgeResetTime(ZonedDateTime.now());
                }
            } else {
                fleet.setLastFleetAgeResetTime(null);
            }

//            job.getAssignedTo().setIdleSince(ZonedDateTime.now());

            ZonedDateTime now = ZonedDateTime.now(ZoneId.of(job.getAssignedTo().getTimeZone()));
            DayOfWeek day = now.getDayOfWeek();
            LocalTime time = now.toLocalTime();
            LocalDate date = now.toLocalDate();
            List<UserAvailability> userAvailabilities =
                    userAvailabilityRepository.findActiveByUserAndDay(job.getAssignedTo().getUuid(), day, time);
            if (!userAvailabilities.isEmpty()) {
                userAvailability = userAvailabilities.get(0);
            }

            List<UserAvailabilityException> userAvailabilityExceptions =
                    userAvailabilityExceptionRepository.getPresentTimeAdditionalWorkingUsers(
                            job.getAssignedTo().getUuid(), date, time);
            if (!userAvailabilityExceptions.isEmpty()) {
                userAvailabilityException = userAvailabilityExceptions.get(0);
            }

            if (userAvailability != null) {
                if (userAvailability.getBreakStartingTime() != null && userAvailability.getBreakEndingTime() != null) {
                    if (time.isAfter(userAvailability.getBreakStartingTime()) && time.isBefore(
                            userAvailability.getBreakEndingTime())) {
                        ZonedDateTime breakEndingTime = ZonedDateTime.of(date, userAvailability.getBreakEndingTime(),
                                                                         ZoneId.of(job.getAssignedTo().getTimeZone()));
                        job.getAssignedTo().setIdleSince(breakEndingTime);

                    }
                }
            }

            if (userAvailabilityException != null) {
                if (userAvailabilityException.getBreakStartingTime() != null
                    && userAvailabilityException.getBreakEndingTime() != null) {
                    if (Type.WORKING_DAY.equals(userAvailabilityException.getType()) && time.isAfter(
                            userAvailabilityException.getBreakStartingTime()) &&
                        time.isBefore(userAvailabilityException.getBreakEndingTime())) {

                        ZonedDateTime breakEndingTime =
                                ZonedDateTime.of(date, userAvailabilityException.getBreakEndingTime(),
                                                 ZoneId.of(job.getAssignedTo().getTimeZone()));
                        job.getAssignedTo().setIdleSince(breakEndingTime);

                    }
                }
            }

            // Ensure all intermediate jobs are completed before marking main job as COMPLETED.
            if (job.isBoxTruckOrVan()) {
                job.getJobRouteSteps().stream()
                        .filter(step -> !JobRouteStep.StepStatus.COMPLETED.equals(step.getStepStatus()))
                        .findAny()
                        .ifPresent(value -> {
                            throw new ServiceException(ErrorCode.BOXTRUCK_JOB_INCOMPLETE_PATCH_ERROR);
                        });
            }

            long mins = ChronoUnit.MINUTES.between(job.getDropDateTime(), job.getPickupDateTime());
            String type = job.isBoxTruckOrVan() ? "BoxTruck Job" : "Job";
            log.debug("{} {} completed in {} minutes.", type, job.getUuid(), mins);
        }

        // IN_PROGRESS is only for JobRouteStep status (BoxTruck job) and not main job.
        if (!Status.IN_PROGRESS.equals(jobStatusRequest.getStatus())) {
            job.setStatus(jobStatusRequest.getStatus());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportJobsAsCSV(String fromDate, String toDate, Predicate predicate,
                                    String timeZone, List<String> clientIds, Optional<String> assignedToUserId,
                                    Optional<String> createdByUserId, Boolean isSupervisorOrClient) {

        if (assignedToUserId.isPresent()) {
            BooleanExpression assignedToUserCriteria =
                    QJob.job.assignedTo.uuid.eq(assignedToUserId.get());
            predicate = Objects.nonNull(predicate) ? assignedToUserCriteria.and(predicate)
                                                   : assignedToUserCriteria;
        }
        if (createdByUserId.isPresent()) {
            BooleanExpression createdByCriteria = QJob.job.createdBy.uuid.eq(createdByUserId.get());
            predicate = Objects.nonNull(predicate) ? createdByCriteria.and(predicate) : createdByCriteria;
        }
        if (CollectionUtils.isNotEmpty(clientIds)) {
            BooleanExpression clientsCriteria = QJob.job.pickupLocation.client.uuid.in(clientIds);
            predicate = Objects.nonNull(predicate) ? clientsCriteria.and(predicate) : clientsCriteria;
        }

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }

        var jobs = jobRepository.findAll(predicate);

        List<JobExportDto> jobExportDtos = new ArrayList<>();
        List<JobExportWithoutAsnDto> jobExportWithoutAsnDtos = new ArrayList<>();

        String[] headerLables;

        if (isSupervisorOrClient) {
            jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));
            headerLables = new String[]{"JOB #", "SPOT CREATION DATE", "CARRIER", "EQUIPMENT", "UNIT #", "EQUIP STATUS",
                                        "DESCRIPTION", "PRIORITY", "STATUS", "P/U LOCATION", "P/U SPOT",
                                        "P/U DROP TYPE",
                                        "P/U DATE/TIME", "P/U NOTES", "DROP LOCATION", "DROP SPOT", "DROP TYPE",
                                        "DROP DATE/TIME",
                                        //"DROP NOTES", "COMPLETION TIME", "DATE CREATED", "MODIFIED DATE"};
                                        "DROP NOTES", "COMPLETION TIME", "DRIVER NAME", "UNSIGNED BOL", "SIGNED BOL",
                                        "TEMPERATURE", "WEATHER", "SEQUENCE ASN"};
        } else {
            jobs.forEach(job -> jobExportWithoutAsnDtos.add(jobMapper.mapToExportWithoutAsnDto(job, timeZone)));
            headerLables = new String[]{"JOB #", "SPOT CREATION DATE", "CARRIER", "EQUIPMENT", "UNIT #", "EQUIP STATUS",
                                        "DESCRIPTION", "PRIORITY", "STATUS", "P/U LOCATION", "P/U SPOT",
                                        "P/U DROP TYPE",
                                        "P/U DATE/TIME", "P/U NOTES", "DROP LOCATION", "DROP SPOT", "DROP TYPE",
                                        "DROP DATE/TIME",
                                        //"DROP NOTES", "COMPLETION TIME", "DATE CREATED", "MODIFIED DATE"};
                                        "DROP NOTES", "COMPLETION TIME", "DRIVER NAME", "UNSIGNED BOL", "SIGNED BOL",
                                        "TEMPERATURE", "WEATHER"};
        }

        try {
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName =
                    tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
            try (OutputStream outputStream = new FileOutputStream(fileName);) {
                String csvData = "";
                if (isSupervisorOrClient) {
                    csvData = CSVUtils.toCSV(jobExportDtos, ',', true, headerLables);
                } else {
                    csvData = CSVUtils.toCSV(jobExportWithoutAsnDtos, ',', true, headerLables);
                }

                outputStream.write(csvData.getBytes());
                outputStream.flush();
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting CSV!", e);
            throw new ServiceException(ErrorCode.JOB_EXPORT, e.getMessage());
        }
    }

    @Override
    public Resource exportJobsAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone,
                                      List<String> clientIds, Optional<String> assignedToUserId,
                                      Optional<String> createdByUserId, Boolean isSupervisorOrClient) {

        if (assignedToUserId.isPresent()) {
            BooleanExpression assignedToUserCriteria =
                    QJob.job.assignedTo.uuid.eq(assignedToUserId.get());
            predicate = Objects.nonNull(predicate) ? assignedToUserCriteria.and(predicate)
                                                   : assignedToUserCriteria;
        }
        if (createdByUserId.isPresent()) {
            BooleanExpression createdByCriteria = QJob.job.createdBy.uuid.eq(createdByUserId.get());
            predicate = Objects.nonNull(predicate) ? createdByCriteria.and(predicate) : createdByCriteria;
        }
        if (CollectionUtils.isNotEmpty(clientIds)) {
            BooleanExpression clientsCriteria = QJob.job.pickupLocation.client.uuid.in(clientIds);
            predicate = Objects.nonNull(predicate) ? clientsCriteria.and(predicate) : clientsCriteria;
        }

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }

        var jobs = jobRepository.findAll(predicate);

        List<JobExportDto> jobExportDtos = new ArrayList<>();
        jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));

        try {

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Jobs");

            // Create header row
            Row headerRow = sheet.createRow(0);
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("JOB#");
            cell1.setCellStyle(cellStyle);
            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("SPOT CREATION DATE");
            cell2.setCellStyle(cellStyle);
            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("CARRIER");
            cell3.setCellStyle(cellStyle);
            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("EQUIPMENT");
            cell4.setCellStyle(cellStyle);
            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("UNIT#");
            cell5.setCellStyle(cellStyle);
            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("EQUIP STATUS");
            cell6.setCellStyle(cellStyle);
            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("DESCRIPTION");
            cell7.setCellStyle(cellStyle);
            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("PRIORITY");
            cell8.setCellStyle(cellStyle);
            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("STATUS");
            cell9.setCellStyle(cellStyle);
            Cell cell10 = headerRow.createCell(9);
            cell10.setCellValue("P/U LOCATION");
            cell10.setCellStyle(cellStyle);
            Cell cell11 = headerRow.createCell(10);
            cell11.setCellValue("P/U SPOT");
            cell11.setCellStyle(cellStyle);
            Cell cell12 = headerRow.createCell(11);
            cell12.setCellValue("P/U DROPTYPE");
            cell12.setCellStyle(cellStyle);
            Cell cell13 = headerRow.createCell(12);
            cell13.setCellValue("P/U DATE/TIME");
            cell13.setCellStyle(cellStyle);
            Cell cell14 = headerRow.createCell(13);
            cell14.setCellValue("P/U NOTES");
            cell14.setCellStyle(cellStyle);
            Cell cell15 = headerRow.createCell(14);
            cell15.setCellValue("DROP LOCATION");
            cell15.setCellStyle(cellStyle);
            Cell cell16 = headerRow.createCell(15);
            cell16.setCellValue("DROP SPOT");
            cell16.setCellStyle(cellStyle);
            Cell cell17 = headerRow.createCell(16);
            cell17.setCellValue("DROP TYPE");
            cell17.setCellStyle(cellStyle);
            Cell cell18 = headerRow.createCell(17);
            cell18.setCellValue("DROP DATE/TIME");
            cell18.setCellStyle(cellStyle);
            Cell cell19 = headerRow.createCell(18);
            cell19.setCellValue("DROP NOTES");
            cell19.setCellStyle(cellStyle);
            Cell cell20 = headerRow.createCell(19);
            cell20.setCellValue("COMPLETION TIME");
            cell20.setCellStyle(cellStyle);
            Cell cell21 = headerRow.createCell(20);
            cell21.setCellValue("DRIVER NAME");
            cell21.setCellStyle(cellStyle);
            Cell cell22 = headerRow.createCell(21);
            cell22.setCellValue("UNSIGNED BOL");
            cell22.setCellStyle(cellStyle);
            Cell cell23 = headerRow.createCell(22);
            cell23.setCellValue("SIGNED BOL");
            cell23.setCellStyle(cellStyle);
            Cell cell24 = headerRow.createCell(23);
            cell24.setCellValue("WEATHER");
            cell24.setCellStyle(cellStyle);
            Cell cell25 = headerRow.createCell(24);
            cell25.setCellValue("TEMPERATURE");
            cell25.setCellStyle(cellStyle);
            if (isSupervisorOrClient) {
                Cell cell26 = headerRow.createCell(25);
                cell26.setCellValue("SEQUENCE ASN");
                cell26.setCellStyle(cellStyle);
            }

            int rowNum = 1;
            for (JobExportDto jobExportDto : jobExportDtos) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(jobExportDto.getJobNumber());
                row.createCell(1).setCellValue(jobExportDto.getCreatedDate());
                row.createCell(2).setCellValue(jobExportDto.getFleetCarrier());
                row.createCell(3).setCellValue(jobExportDto.getFleetType().toString());
                row.createCell(4).setCellValue(jobExportDto.getFleetUnitNumber());

                if (jobExportDto.getFleetStatus() != null && jobExportDto.getFleetStatus().equals(FULL)) {
                    row.createCell(5).setCellValue("LOADED");
                } else if (jobExportDto.getFleetStatus() != null) {
                    row.createCell(5).setCellValue(jobExportDto.getFleetStatus());
                } else {
                    row.createCell(5).setCellValue("---");
                }

                row.createCell(6).setCellValue(jobExportDto.getDescription());
                row.createCell(7).setCellValue(jobExportDto.getPriority().toString());
                row.createCell(8).setCellValue(jobExportDto.getStatus().toString());
                row.createCell(9).setCellValue(jobExportDto.getPickupLocationName());
                row.createCell(10).setCellValue(jobExportDto.getPickupSpotName());
                if (jobExportDto.getPickupSpotType() != null) {
                    row.createCell(11).setCellValue(jobExportDto.getPickupSpotType().toString());
                } else {
                    row.createCell(11).setCellValue("---");
                }
                row.createCell(12).setCellValue(jobExportDto.getPickupDateTime());
                row.createCell(13).setCellValue(jobExportDto.getPickupNotes());
                row.createCell(14).setCellValue(jobExportDto.getDropLocationName());
                row.createCell(15).setCellValue(jobExportDto.getDropSpotName());
                if (jobExportDto.getDropSpotType() != null) {
                    row.createCell(16).setCellValue(jobExportDto.getDropSpotType().toString());
                } else {
                    row.createCell(16).setCellValue("---");
                }
                row.createCell(17).setCellValue(jobExportDto.getDropDateTime());
                row.createCell(18).setCellValue(jobExportDto.getDropNotes());
                row.createCell(19).setCellValue(jobExportDto.getSpotCompletionTime());
                if (jobExportDto.getAssignedName() != null) {
                    row.createCell(20).setCellValue(jobExportDto.getAssignedName());
                } else {
                    row.createCell(20).setCellValue("---");
                }
                row.createCell(21).setCellValue(jobExportDto.getBolUnsigned());
                row.createCell(22).setCellValue(jobExportDto.getBolSigned());
                if (jobExportDto.getClimate() != null) {
                    row.createCell(23).setCellValue(jobExportDto.getClimate());
                }
                if (jobExportDto.getTemperature() != null) {
                    row.createCell(24).setCellValue(jobExportDto.getTemperature());
                }
                if (isSupervisorOrClient) {
                    row.createCell(25).setCellValue(jobExportDto.getSequenceAsn());
                }
            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public JobDto getDriverJob(String userId, String timeZone) {
        BooleanExpression assignedToCriteria = QJob.job.assignedTo.uuid.eq(userId);
        Pageable pageable = PageRequest.of(0, 1, Sort.by("createdDate").ascending());

        // Fetching In Transit Jobs by assignedTo provided userId
        BooleanExpression criteria = assignedToCriteria.and(QJob.job.status.eq(Status.IN_TRANSIT));
        Page<Job> myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN created by provided userId and HIGH Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.HIGH));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN created by provided userId and MEDIUM Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.MEDIUM));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN created by provided userId and LOW Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.LOW));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN Not created by provided user and HIGH Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.HIGH));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );

        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN Not created by provided user and MEDIUM Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.MEDIUM));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        // Fetching OPEN Not created by provided user and LOW Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.LOW));
        myJob = jobRepository.findAll(criteria, pageable);
        myJob.forEach(job ->
                              job.getBols().forEach(bol -> {
                                  String currentImagePath = bol.getImagePath();
                                  String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                                  bol.setImagePath(newImagePath);
                              })
        );
        if (myJob.hasContent()) {
            return jobMapper.mapToDto(myJob.getContent().get(0), timeZone);
        }

        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public List<JobDto> getSpotterJobs(String userId, String timeZone) {
        BooleanExpression assignedToCriteria = QJob.job.assignedTo.uuid.eq(userId);
        Sort sort = Sort.by("createdDate").ascending();

        List<JobDto> jobs = new ArrayList<>();

        // Fetching In Transit Jobs by assignedTo provided userId
        BooleanExpression criteria = assignedToCriteria.and(QJob.job.status.eq(Status.IN_TRANSIT));
        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });

        // Fetching OPEN created by provided userId and HIGH Priority Jobs
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.HIGH));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });

        // Fetching OPEN created by provided userId and MEDIUM Priority Jobs
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.MEDIUM));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });
        // Fetching OPEN created by provided userId and LOW Priority Jobs
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.eq(userId)).and(QJob.job.priority.eq(Priority.LOW));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });
        // Fetching OPEN Not created by provided user and HIGH Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.HIGH));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });
        // Fetching OPEN Not created by provided user and MEDIUM Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.MEDIUM));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });
        // Fetching OPEN Not created by provided user and LOW Priority Job
        criteria = assignedToCriteria.and(QJob.job.status.eq(Status.OPEN))
                .and(QJob.job.createdBy.uuid.ne(userId)).and(QJob.job.priority.eq(Priority.LOW));

        jobRepository.findAll(criteria, sort)
                .forEach(job -> {
                    job.getBols().forEach(bol -> {
                        String currentImagePath = bol.getImagePath();
                        String newImagePath = cdn + "/" + "BOL" + "/" + currentImagePath;
                        bol.setImagePath(newImagePath);
                    });
                    jobs.add(jobMapper.mapToDto(job, timeZone));
                });
        return jobs;
    }

    @Override
    @Transactional
    public void deleteFleet(String jobId) {

        List<Long> jobids;
        Job job = jobRepository.findByUuid(jobId)
                .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, jobId));
        if (Status.OPEN.equals(job.getStatus()) || Status.QUEUE.equals(job.getStatus()) || Status.SCHEDULED.equals(
                job.getStatus())) {
            Optional<Message> message = messageRepository.findByJobId(job.getId());

            Optional<TrailerLog> trailerLog = trailerLogRepository.findByJobId(job.getId());
            if (trailerLog.isPresent()) {
                TrailerLog log = trailerLog.get();
                trailerLogRepository.delete(log);
            }

            if (message.isPresent()) {
                Message msg = message.get();
                msg.setJob(null);
            }

            Optional<Bol> bol = bolRepository.findByJobId(job.getId());

            if (bol.isPresent()) {
                Bol bl = bol.get();
                //    	  bl.setJob(null);
                bolRepository.delete(bl);
            }
            if (Status.QUEUE.equals(job.getStatus())) {
                List<Job> remainingJobsInQueue =
                        jobRepository.findAllJobsAfterQueuePosition(job.getPickupLocation().getClient().getUuid(),
                                                                    job.getBucket(), job.getQueuePosition());
                Long requiredPosition = job.getQueuePosition();
                for (Job job1 : remainingJobsInQueue) {
                    Job requiredJob = jobRepository.findByUuid(job1.getUuid())
                            .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, job1.getUuid()));
                    requiredJob.setQueuePosition(requiredPosition);
                    jobRepository.save(requiredJob);
                    requiredPosition = requiredPosition + 1L;
                }
            }
            if (job.getPreviouslyAssignedUsers() != null) {

                job.setPreviouslyAssignedUsers(null);
            }

            Spot pickupSpot = spotRepository.findActiveByUuid(job.getPickupSpot().getUuid())
                    .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, job.getPickupSpot().getUuid()));
            Set<Long> pickupJobIds =
                    pickupSpot.getPickUpJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
            pickupSpot.getPickUpJobs().clear();
            pickupJobIds.remove(job.getId());
            Set<Job> pickupJobs = jobRepository.findAllByJobIds(pickupJobIds);
            pickupSpot.getPickUpJobs().addAll(pickupJobs);
            spotRepository.save(pickupSpot);

            if (job.getDropSpot() != null) {
                Spot dropSpot = spotRepository.findActiveByUuid(job.getDropSpot().getUuid())
                        .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, job.getDropSpot().getUuid()));
                Set<Long> dropOffJobIds =
                        dropSpot.getDropOffJobs().stream().map(job1 -> job1.getId()).collect(Collectors.toSet());
                dropSpot.getDropOffJobs().clear();
                dropOffJobIds.remove(job.getId());
                Set<Job> dropOffJobs = jobRepository.findAllByJobIds(dropOffJobIds);
                dropSpot.getDropOffJobs().addAll(dropOffJobs);
                spotRepository.save(dropSpot);
            }

            jobRepository.delete(job);
        } else {
            throw new ServiceException(ErrorCode.DELETE_JOB, jobId);
        }
    }

    @Override
    @Transactional
    public void changeJobQueuePositions(String clientId, Bucket bucket, Long dragIndex, Long dropIndex) {

        if (dragIndex > dropIndex) {
            Job draggedJob = jobRepository.findByClientBucketPosition(clientId, bucket, dragIndex);
            List<Job> jobsAfterDraggedJob =
                    jobRepository.findJobsAfterQueuePosition(clientId, bucket, dropIndex, dragIndex);
            for (Job job : jobsAfterDraggedJob) {
                job.setQueuePosition(job.getQueuePosition() + 1);
                jobRepository.save(job);
            }
            draggedJob.setQueuePosition(dropIndex);
            jobRepository.save(draggedJob);
        } else if (dragIndex < dropIndex) {
            Job draggedJob = jobRepository.findByClientBucketPosition(clientId, bucket, dragIndex);
            List<Job> jobsBeforeDraggedJob =
                    jobRepository.findJobsBeforeQueuePosition(clientId, bucket, dropIndex, dragIndex);
            for (Job job : jobsBeforeDraggedJob) {
                job.setQueuePosition(job.getQueuePosition() - 1);
                jobRepository.save(job);
            }
            draggedJob.setQueuePosition(dropIndex);
            jobRepository.save(draggedJob);
        }

    }

    @Override
    @Transactional
    public void queueScheduledJobs() {

        List<Job> allScheduledJobs = jobRepository.findAllScheduledBucketJobs(List.of(Bucket.BUCKET_DRIVER,
                                                                                      Bucket.BUCKET_SPOTTER));

        List<Job> jobsToBeQueued = new ArrayList<>();

        for (Job job : allScheduledJobs) {
            if (job.getScheduleDateTime().isBefore(ZonedDateTime.now())) {
                jobsToBeQueued.add(job);
            }
        }

        // Queue eligible jobs
        for (Job job : jobsToBeQueued) {
            // Stack up the next job in queue
            List<Job> jobsInQueue = jobRepository.findAllInQueueByClient(job.getPickupLocation().getClient().getUuid(),
                                                                         job.getBucket());
            for (Job job1 : jobsInQueue) {
                job1.setQueuePosition(job1.getQueuePosition() + 1);
                jobRepository.save(job1);
            }
            job.setStatus(Status.QUEUE);
            job.setQueuePosition(0L);
            jobRepository.save(job);
        }
        isJobUpdated = !jobsToBeQueued.isEmpty();

        //        assignJobFromQueue();
    }

    @Override
    @Transactional
    public void reassignBucketJobs() {

        List<Job> allDriverBucketOpenJobs = jobRepository.findAllOpenBucketJobs(List.of(Bucket.BUCKET_DRIVER));
        List<Job> allSpotterBucketOpenJobs = jobRepository.findAllOpenBucketJobs(List.of(Bucket.BUCKET_SPOTTER));

        isJobUpdated = false;
        List<Job> jobsToBeReAssigned = new ArrayList<>();

        processJobsForReassignment(allDriverBucketOpenJobs, true, jobsToBeReAssigned);
        processJobsForReassignment(allSpotterBucketOpenJobs, false, jobsToBeReAssigned);

        for (Job job : jobsToBeReAssigned) {

            List<Job> jobsInQueue = jobRepository.findAllInQueueByClient(job.getPickupLocation().getClient().getUuid(),
                                                                         job.getBucket());
            for (Job job1 : jobsInQueue) {

                job1.setQueuePosition(job1.getQueuePosition() + 1);
                jobRepository.save(job1);
            }

            User user = userRepository.findOneActiveByUuid(job.getAssignedTo().getUuid());
            job.setAssignedTo(null);
            job.setAssignedAt(null);
            job.setPermanentlyAssignedUser(null);
            job.setStatus(Status.QUEUE);
            job.setQueuePosition(0L);
            job.getPreviouslyAssignedUsers().add(user);
            jobRepository.save(job);
        }
        isJobUpdated = !jobsToBeReAssigned.isEmpty();
        assignJobFromQueue();
    }

    private void processJobsForReassignment(List<Job> jobs, boolean isDriver, List<Job> result) {
        for (Job job : jobs) {
            Client client = job.getPickupLocation().getClient();
            if (Objects.nonNull(client.getJobReAssign())) {
                long minutes = isDriver ? client.getDriverJobReAssignAfter() : client.getSpotterJobReAssignAfter();

                // if permanently assigned (because of 0 fallback timer) or fallback timer, don't move back to queue
                // first time let the job get assigned permanently and
                // only when fallback timer is reset the permanently assigned job will be reassigned.
                if ((minutes > 0 && job.getAssignedAt() != null &&
                     job.getAssignedAt().isBefore(ZonedDateTime.now().minusMinutes(minutes))) ||
                    job.getPermanentlyAssignedUser() != null) {
                    result.add(job);
                }
            }
        }
    }

    @Override
    @Transactional
    public void assignJobFromQueue() {

        Bucket buckets[] = {Bucket.BUCKET_DRIVER, Bucket.BUCKET_SPOTTER};

        List<Client> allClients = clientRepository.findAllActiveClients();
        for (Bucket bucket : buckets) {
            String requiredRole = null;

            if (bucket.equals(Bucket.BUCKET_DRIVER)) {
                requiredRole = DRIVER;
            } else if (bucket.equals(Bucket.BUCKET_SPOTTER)) {
                requiredRole = SPOTTER;
            }

            boolean isDriver = bucket.equals(Bucket.BUCKET_DRIVER);

            for (Client client : allClients) {
                List<Job> allJobsinQueue = jobRepository.findAllInQueueByClient(client.getUuid(), bucket);

                String clientTimeZone = client.getTimeZone();
                String clientId = client.getUuid();
                ZonedDateTime now = getScheduleDateTime(clientTimeZone, Optional.empty());
                DayOfWeek today = now.getDayOfWeek();
                LocalTime time = now.toLocalTime();
                LocalDate date = now.toLocalDate();

                long fallbackTimer =
                        isDriver ? client.getDriverJobReAssignAfter() : client.getSpotterJobReAssignAfter();

                List<UserAvailability> userAvailabilities =
                        userAvailabilityRepository.findPresentUserAvailabilities(today, time, requiredRole,
                                                                                 clientId);
                List<UserAvailabilityException> additionalUsersInAvailabilities =
                        userAvailabilityExceptionRepository.getAdditionalWorkingUsers(date, requiredRole, time,
                                                                                      clientId);
                List<OverTime> overTimeUsers =
                        overTimeRepository.getOverTimeUsers(date, requiredRole, time, clientId);

                for (Job job : allJobsinQueue) {
                    Location pickupLocation = locationRepository.findActiveByUuid(job.getPickupLocation().getUuid())
                            .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                                    job.getPickupLocation().getUuid()));

                    Location dropLocation = locationRepository.findActiveByUuid(job.getDropLocation().getUuid())
                            .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                                                                    job.getDropLocation().getUuid()));

                    Set<User> unWantedSpotters = new HashSet<>();
                    if (Bucket.BUCKET_SPOTTER.equals(bucket)) {
                        addToUnwantedSpotters(userAvailabilities, additionalUsersInAvailabilities, overTimeUsers,
                                              pickupLocation, dropLocation, unWantedSpotters);
                    }

                    Set<User> availableUsers = new HashSet<>();
                    populateAvailableUsers(userAvailabilities, additionalUsersInAvailabilities, overTimeUsers,
                                           unWantedSpotters, date, time, clientId, requiredRole, availableUsers);

                    User firstUserInSet = availableUsers.stream()
                            .findFirst()
                            .orElse(null);
                    if (firstUserInSet != null) {
                        // mark the first (or anyone) non-idle user as idle.
                        if (firstUserInSet.getIdleSince() == null) {
                            ZonedDateTime present = ZonedDateTime.now(ZoneId.of(firstUserInSet.getTimeZone()));
                            User user1 = userRepository.findByUserUuid(firstUserInSet.getUuid());
                            user1.setIdleSince(present);
                            user1.setIsIdleClear(false);
                            userRepository.save(user1);
                            firstUserInSet.setIdleSince(present);
                        }

                        // pick the most idle available user who has never been assigned this job before
                        Set<String> previouslyAssignedUserIds = job.getPreviouslyAssignedUsers().stream()
                                .map(User::getUuid)
                                .collect(Collectors.toSet());

                        firstUserInSet = availableUsers.stream()
                                .peek(user -> {
                                    if (user.getIdleSince() == null) {
                                        ZonedDateTime userNow = ZonedDateTime.now(ZoneId.of(user.getTimeZone()));
                                        user.setIdleSince(userNow);
                                        user.setIsIdleClear(false);
                                        userRepository.save(user);
                                    }
                                })
                                .filter(user -> !previouslyAssignedUserIds.contains(user.getUuid()))
                                .min(Comparator.comparing(User::getIdleSince))
                                .orElse(null);

                        // Fallback: if fallback is zero, pick most idle regardless
                        if (fallbackTimer == 0) {
                            firstUserInSet = availableUsers.stream()
                                    .min(Comparator.comparing(User::getIdleSince))
                                    .orElse(null);
                        }

                        // assign the job to most idle user.
                        if (firstUserInSet != null) {

                            String uuid = firstUserInSet.getUuid();
                            User user = userRepository.findActiveByUuid(uuid).orElseThrow(
                                    () -> new ServiceException(ErrorCode.USER_NOT_FOUND, uuid));
                            if (user != null) {
                                if (user.getRoles().stream()
                                        .noneMatch(role -> role.getRoleName().equalsIgnoreCase(SPOTTER)
                                                           || role.getRoleName().equalsIgnoreCase(DRIVER)
                                                           || role.getRoleName().equalsIgnoreCase(SUPERVISOR))) {
                                    throw new ServiceException(ErrorCode.CREATE_UPDATE_JOB_INVALID_ASSIGNED_TO);
                                }
                            }
                            job.setAssignedTo(user);
                            job.setAssignedAt(ZonedDateTime.now());
                            job.setQueuePosition(null);
                            job.setStatus(Status.OPEN);
                            if (fallbackTimer == 0) {
                                job.setPermanentlyAssignedUser(user);
                            }
                            jobRepository.save(job);

                            if (user != null) {
                                // Creating notification -- new job notification or update job notification?
                                notificationService.createNewJobNotification(job);
                            }
                        }
                    }
                }
            }
        }
        // Reposition all queued jobs for all clients.
        for (Bucket bucket : buckets) {
            for (Client client : allClients) {
                List<Job> pendingJobsinQueue = jobRepository.findAllInQueueByClient(client.getUuid(), bucket);
                if (!pendingJobsinQueue.isEmpty()) {
                    Long firstPosinQueue = 0L;
                    for (Job job : pendingJobsinQueue) {
                        job.setQueuePosition(firstPosinQueue);
                        jobRepository.save(job);
                        firstPosinQueue++;
                    }
                }
            }
        }
    }

    private void addToUnwantedSpotters(List<UserAvailability> userAvailabilities,
                                       List<UserAvailabilityException> additionalUsersInAvailabilities,
                                       List<OverTime> overTimeUsers, Location pickupLocation, Location dropLocation,
                                       Set<User> unWantedSpotters) {
        List<User> availableSpotters = Stream.of(
                        userAvailabilities.stream().map(UserAvailability::getUser),
                        additionalUsersInAvailabilities.stream().map(UserAvailabilityException::getUser),
                        overTimeUsers.stream().map(OverTime::getUser)
                )
                .flatMap(s -> s) // merge all streams into one
                .collect(Collectors.toList());

        // add spotters (to remove later), whose either pickup or drop location is not their location.
        availableSpotters.stream()
                .filter(spotter -> !spotter.getLocations()
                        .containsAll(Arrays.asList(pickupLocation, dropLocation)))
                .forEach(unWantedSpotters::add);
    }

    private void populateAvailableUsers(List<UserAvailability> userAvailabilities,
                                        List<UserAvailabilityException> additionalUsersInAvailabilities,
                                        List<OverTime> overTimeUsers, Set<User> unWantedSpotters,
                                        LocalDate date, LocalTime time, String clientId, String requiredRole,
                                        Set<User> availableUsers) {

        // 1. Merge all sources into availableUsers
        availableUsers.addAll(Stream.concat(
                Stream.concat(
                        // available users
                        userAvailabilities.stream()
                                .filter(ua -> !isOnBreak(time, ua.getBreakStartingTime(),
                                                         ua.getBreakEndingTime()))
                                .map(UserAvailability::getUser),
                        // additional(exceptional) users
                        additionalUsersInAvailabilities.stream()
                                .filter(uae -> !isOnBreak(time, uae.getBreakStartingTime(),
                                                          uae.getBreakEndingTime()))
                                .map(UserAvailabilityException::getUser)
                ),
                // overTime users
                overTimeUsers.stream()
                        .map(OverTime::getUser)
        ).collect(Collectors.toSet()));

        // 2. Remove on-leave users
        List<UserAvailabilityException> usersOnLeaveInAvailabilities =
                userAvailabilityExceptionRepository.getOnLeaveUsers(date, requiredRole, time, clientId);

        Set<String> onLeaveUserIds = usersOnLeaveInAvailabilities.stream()
                .map(uae -> uae.getUser().getUuid())
                .collect(Collectors.toSet());

        availableUsers.removeIf(u -> onLeaveUserIds.contains(u.getUuid()));

        // 3. Remove unwanted spotters (wrong pickup/drop location)
        unWantedSpotters.forEach(availableUsers::remove);

        // 4. Remove users with incomplete jobs
        availableUsers.removeIf(u -> !jobRepository.findInCompleteJobsByUser(u.getUuid()).isEmpty());
    }

    // Helper: check if a user is on break at given time
    private boolean isOnBreak(LocalTime time, LocalTime breakStart, LocalTime breakEnd) {
        return breakStart != null && breakEnd != null
               && breakStart.isBefore(time)
               && breakEnd.isAfter(time);
    }

    public ResponseEntity<Map<String, UserTotalNumMovesResponse>> fetchAverageMoves(Predicate predicate,
                                                                                    Pageable pageable) {
        long startTimeBegining = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

        // Get current date and time for comparisons
        ZonedDateTime now = ZonedDateTime.now();

        // Calculate date ranges for day, week, and month
        ZonedDateTime oneWeekAgo = now.minusWeeks(1);
        ZonedDateTime oneMonthAgo = now.minusMonths(1);

        // Fetch completed jobs directly from DB for the calculated date range (current day, last week, and last month)
        Pageable unlimitedPageable = PageRequest.of(0, Integer.MAX_VALUE, pageable.getSort());

        long startTimeQuery = System.currentTimeMillis();

        Page<Job> jobPage = jobRepository.findCompletedJobsByDateRange(oneMonthAgo, now, unlimitedPageable);

        log.info("Query time : {}", System.currentTimeMillis() - startTimeQuery);

        // Process the results
        Map<String, UserTotalNumMovesResponse> userMovesMap = new HashMap<>();

        for (Job job : jobPage.getContent()) {
            long startTime = System.currentTimeMillis();
            User userDto = job.getAssignedTo();

            String roleName = userDto.getRoles().stream()
                    .map(Role::getRoleName)
                    .findFirst()
                    .orElse("UNKNOWN");

            if (!Arrays.asList(DRIVER, SPOTTER).contains(roleName)) {
                continue;
            }

            String clientName = userDto.getClients().stream()
                    .map(Client::getClientName)
                    .findFirst()
                    .orElse("UNKNOWN");

            String clientUuid = userDto.getClients().stream()
                    .map(Client::getUuid)
                    .findFirst()
                    .orElse("UNKNOWN");

            UserTotalNumMovesResponse userMoves = userMovesMap.getOrDefault(userDto.getUuid(),
                                                                            new UserTotalNumMovesResponse(
                                                                                    job.getAssignedTo().getFirstName()
                                                                                    + " " + job.getAssignedTo()
                                                                                            .getLastName(), roleName,
                                                                                    clientName, clientUuid, 0, 0, 0));

            try {
                ZonedDateTime dropDateTime = job.getDropDateTime();

                // Day-wise count: Compare only the date part, ignoring time
                if (dropDateTime.toLocalDate().equals(now.toLocalDate())) {
                    userMoves.setDayMoves(userMoves.getDayMoves() + 1);
                }

                // Week-wise count: Compare if the dropDateTime is within the last 7 days
                if (dropDateTime.isAfter(oneWeekAgo)) {
                    userMoves.setWeekMoves(userMoves.getWeekMoves() + 1);
                }

                // Month-wise count: Compare if the dropDateTime is within the last 30 days
                if (dropDateTime.isAfter(oneMonthAgo)) {
                    userMoves.setMonthMoves(userMoves.getMonthMoves() + 1);
                }

                // Put the updated user moves into the map
                userMovesMap.put(userDto.getUuid(), userMoves);
                //log.info("Loop time : {}", startTime - System.currentTimeMillis());

            } catch (Exception e) {
                log.error("Error processing job {}: {}", job.getId(), e.getMessage());
            }
        }

        log.info("Complete time : {}", System.currentTimeMillis() - startTimeBegining);

        return ResponseEntity.ok(userMovesMap);
    }

    public ResponseEntity<Map<String, Map<String, Integer>>> fetchAverageMoves(String timeZone, String clientId) {
        Map<String, Map<String, Integer>> roleDayCountMap = new LinkedHashMap<>();

        // Initialize structure
        roleDayCountMap.put(DRIVER, new LinkedHashMap<>());
        roleDayCountMap.put(SPOTTER, new LinkedHashMap<>());

        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate today = LocalDate.now(zoneId);

        for (int i = 6; i >= 0; i--) {
            LocalDate day = today.minusDays(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");

            ZonedDateTime fromDate = day.atStartOfDay(zoneId);
            ZonedDateTime toDate = day.atTime(LocalTime.MAX).atZone(zoneId);

            // Fetch total move count from the query for each role
            int driverMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, DRIVER, clientId);
            int spotterMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, SPOTTER, clientId);

            String dayOfWeek = day.getDayOfWeek().name() + " (" + day.format(formatter) + ")";

            // Store results in the map
            roleDayCountMap.get(DRIVER).put(dayOfWeek, driverMoves);
            roleDayCountMap.get(SPOTTER).put(dayOfWeek, spotterMoves);
        }

        return ResponseEntity.ok(roleDayCountMap);
    }

    @Override
    public ResponseEntity<Map<String, List<UserAggTotalNumMovesResponse>>> fetchAverageMovesForClient(
            Predicate predicate, Pageable pageable, String clientId) {

        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime oneWeekAgo = now.minusWeeks(1);
        ZonedDateTime oneMonthAgo = now.minusMonths(1);

        Pageable unlimitedPageable = PageRequest.of(0, Integer.MAX_VALUE, pageable.getSort());
        Page<Job> jobPage = jobRepository.findCompletedJobsByDateRange(oneMonthAgo, now, unlimitedPageable);

        Map<String, List<UserAggTotalNumMovesResponse>> userMovesMap = new HashMap<>();

        for (Job job : jobPage.getContent()) {
            User userDto = job.getAssignedTo();

            String roleName = userDto.getRoles().stream()
                    .map(Role::getRoleName)
                    .findFirst()
                    .orElse("UNKNOWN");

            if (!Arrays.asList(DRIVER, SPOTTER).contains(roleName)) {
                continue;
            }

            String clientUuid = userDto.getClients().stream()
                    .map(Client::getUuid)
                    .findFirst()
                    .orElse("UNKNOWN");

            // Filter by clientId if provided
            if (clientId != null && !clientId.equals(clientUuid)) {
                continue;
            }

            // Fetch or initialize the list for this client
            List<UserAggTotalNumMovesResponse> userMovesList = userMovesMap.getOrDefault(clientUuid, new ArrayList<>());

            // Find if an entry for the role already exists
            UserAggTotalNumMovesResponse userMoves = userMovesList.stream()
                    .filter(u -> u.getType().equals(roleName))
                    .findFirst()
                    .orElse(null);

            if (userMoves == null) {
                userMoves = new UserAggTotalNumMovesResponse("ALL USERS", clientUuid, roleName, 0);
                userMovesList.add(userMoves);
            }

            try {
                ZonedDateTime dropDateTime = job.getDropDateTime();

                int moveCount = 0;
                if (dropDateTime.isAfter(oneMonthAgo)) {
                    moveCount++; // Count jobs in the last month
                }

                // Accumulate total moves for this role
                userMoves.setTotalAggMoves(userMoves.getTotalAggMoves() + moveCount);

                // Store/update the map
                userMovesMap.put(clientUuid, userMovesList);
            } catch (Exception e) {
                log.error("Error processing job {}: {}", job.getId(), e.getMessage());
            }
        }
        return ResponseEntity.ok(userMovesMap);
    }

    @Override
    public Resource exportMovesAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone,
                                       String roleNames, String userIds, String jobstatus, Bucket bucketType) {

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime = DateTimeUtils
                    .convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MIN)
                    .atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime = DateTimeUtils
                    .convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MAX)
                    .atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria = QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate = Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }

        Boolean isCompleted = false;

        Boolean isClient = false;
        if (AuthDetailsProvider.isRolePresent(CLIENT)) {
            isClient = true;
        }

        if (jobstatus != null && !jobstatus.isBlank()) {
            List<Job.Status> jobstatusList = Arrays.stream(jobstatus.split(","))
                    .map(String::trim)
                    .map(s -> Job.Status.valueOf(s.toUpperCase()))
                    .collect(Collectors.toList());

            if (jobstatusList.contains(Job.Status.COMPLETED) && jobstatusList.size() == 1) {
                isCompleted = true;
            }

            BooleanExpression combinedFilter;

            if (jobstatusList.contains(Job.Status.QUEUE)) {

                jobstatusList.remove(Job.Status.QUEUE);

                BooleanExpression queueFilter = QJob.job.status.eq(Job.Status.QUEUE)
                        .and(QJob.job.bucket.eq(bucketType));

                if (!jobstatusList.isEmpty()) {
                    BooleanExpression statusFilter;
                    if (isNotBlank(roleNames)) {
                        List<String> roleList = Arrays.stream(roleNames.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        statusFilter = QJob.job.status.in(jobstatusList)
                                .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                    } else {
                        statusFilter = QJob.job.status.in(jobstatusList);
                    }
                    combinedFilter = queueFilter.or(statusFilter);
                } else {
                    combinedFilter = queueFilter;
                }
            } else {
                if (isNotBlank(roleNames)) {
                    List<String> roleList = Arrays.stream(roleNames.split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    combinedFilter = QJob.job.status.in(jobstatusList)
                            .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                } else {
                    combinedFilter = QJob.job.status.in(jobstatusList);
                }
            }

            predicate = predicate == null ? combinedFilter : ((BooleanExpression) predicate).and(combinedFilter);
        } else {
            if (predicate != null && predicate.toString().contains("status = COMPLETED")) {
                isCompleted = true;
            }
            if (isNotBlank(roleNames)) {
                List<String> roleList = Arrays.stream(roleNames.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

                if (!roleList.isEmpty()) {
                    BooleanExpression roleCriteria = QJob.job.assignedTo.roles.any().value.in(roleList);
                    predicate = predicate != null ? roleCriteria.and(predicate) : roleCriteria;
                }
            }
        }

        if (userIds != null && !userIds.isBlank()) {
            List<String> usersIdsList = Arrays.asList(userIds.split(","));

            BooleanExpression userFilter = QJob.job.assignedTo.uuid.in(usersIdsList);
            predicate = (predicate == null) ? userFilter : ((BooleanExpression) predicate).and(userFilter);
        }

        var jobs = jobRepository.findAll(predicate);
        List<JobExportDto> jobExportDtos = new ArrayList<>();
        jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));

        try {

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Jobs");

            // Create header row
            Row headerRow = sheet.createRow(0);
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("P/U LOCATION");
            cell1.setCellStyle(cellStyle);
            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("DROP LOCATION");
            cell2.setCellStyle(cellStyle);
            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("SPOT CREATION DATE/TIME");
            cell3.setCellStyle(cellStyle);
            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("P/U DATE/TIME");
            cell4.setCellStyle(cellStyle);
            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("DROP DATE/TIME");
            cell5.setCellStyle(cellStyle);
            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("UNIT#");
            cell6.setCellStyle(cellStyle);
            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("CARRIER");
            cell7.setCellStyle(cellStyle);
            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("DURATION");
            cell8.setCellStyle(cellStyle);
            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("STATUS");
            cell9.setCellStyle(cellStyle);
            if (!isClient) {
                if (isCompleted) {
                    Cell cell10 = headerRow.createCell(9);
                    cell10.setCellValue("Completed By");
                    cell10.setCellStyle(cellStyle);
                } else {
                    Cell cell10 = headerRow.createCell(9);
                    cell10.setCellValue("ASSIGNED TO");
                    cell10.setCellStyle(cellStyle);
                }
            }

            int rowNum = 1;
            for (JobExportDto jobExportDto : jobExportDtos) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0)
                        .setCellValue(jobExportDto.getPickupLocationName() + " " + jobExportDto.getPickupSpotName());
                row.createCell(1)
                        .setCellValue(jobExportDto.getDropLocationName() + " " + jobExportDto.getDropSpotName());
                row.createCell(2)
                        .setCellValue(jobExportDto.getCreatedDate());
                row.createCell(3).setCellValue(jobExportDto.getPickupDateTime());
                row.createCell(4).setCellValue(jobExportDto.getDropDateTime());
                row.createCell(5).setCellValue(jobExportDto.getFleetUnitNumber());
                row.createCell(6).setCellValue(jobExportDto.getFleetCarrier());
                StringBuilder str = new StringBuilder();
                if (jobExportDto.getStatus() == Status.COMPLETED &&
                    isNotBlank(jobExportDto.getDropDateTime()) && isNotBlank(jobExportDto.getPickupDateTime())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm a");
                    //					DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm:ss a");
                    LocalDateTime start = LocalDateTime.parse(jobExportDto.getPickupDateTime(), formatter);
                    LocalDateTime end = LocalDateTime.parse(jobExportDto.getDropDateTime(), formatter);

                    Duration duration = Duration.between(start, end);

                    long totalSeconds = duration.getSeconds();
                    long hours = totalSeconds / 3600;
                    long minutes = (totalSeconds % 3600) / 60;
                    long seconds = totalSeconds % 60;

                    str.append(hours).append("hrs").append(" : ");
                    str.append(minutes).append("mins").append(" : ");
                    str.append(seconds).append("sec");
                } else {
                    str = str.append("");
                }
                row.createCell(7).setCellValue(str.toString());
                row.createCell(8).setCellValue(jobExportDto.getStatus().toString());
                if (!isClient) {
                    row.createCell(9).setCellValue(jobExportDto.getAssignedName());
                }

            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
        }
    }

    @Override
    public Resource exportMovesAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone, String role,
                                     String roleNames, String userIds, String jobstatus, Bucket bucketType) {

        log.info("predicate({})", predicate);

        Boolean isClient = false;
        if (AuthDetailsProvider.isRolePresent(CLIENT)) {
            isClient = true;
        }

        float[] columnWidths;

        if (isClient) {
            columnWidths = new float[]{100f, 100f, 100f, 85f, 85f, 80f, 85f, 80f, 80f};
        } else {
            columnWidths = new float[]{100f, 100f, 100f, 85f, 85f, 80f, 80f, 85f, 80f, 80f};
        }

        PdfWriter writer;

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
        String startDate = "";
        String endDate = "";

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                startDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                endDate = outputFormat.format(date2);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Boolean isCompleted = false;

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime = DateTimeUtils
                    .convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MIN)
                    .atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime = DateTimeUtils
                    .convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MAX)
                    .atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria = QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate = Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }
        if (jobstatus != null && !jobstatus.isBlank()) {
            List<Job.Status> jobstatusList = Arrays.stream(jobstatus.split(","))
                    .map(String::trim)
                    .map(s -> Job.Status.valueOf(s.toUpperCase()))
                    .collect(Collectors.toList());

            if (jobstatusList.contains(Job.Status.COMPLETED) && jobstatusList.size() == 1) {
                isCompleted = true;
            }

            BooleanExpression combinedFilter;

            if (jobstatusList.contains(Job.Status.QUEUE)) {

                jobstatusList.remove(Job.Status.QUEUE);

                BooleanExpression queueFilter = QJob.job.status.eq(Job.Status.QUEUE)
                        .and(QJob.job.bucket.eq(bucketType));

                if (!jobstatusList.isEmpty()) {
                    BooleanExpression statusFilter;
                    if (isNotBlank(roleNames)) {
                        List<String> roleList = Arrays.stream(roleNames.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        statusFilter = QJob.job.status.in(jobstatusList)
                                .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                    } else {
                        statusFilter = QJob.job.status.in(jobstatusList);
                    }
                    combinedFilter = queueFilter.or(statusFilter);
                } else {
                    combinedFilter = queueFilter;
                }
            } else {
                if (isNotBlank(roleNames)) {
                    List<String> roleList = Arrays.stream(roleNames.split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    combinedFilter = QJob.job.status.in(jobstatusList)
                            .and(QJob.job.assignedTo.roles.any().value.in(roleList));
                } else {
                    combinedFilter = QJob.job.status.in(jobstatusList);
                }
            }

            predicate = predicate == null ? combinedFilter : ((BooleanExpression) predicate).and(combinedFilter);
        } else {
            if (predicate != null && predicate.toString().contains("status = COMPLETED")) {
                isCompleted = true;
            }
            if (isNotBlank(roleNames)) {
                List<String> roleList = Arrays.stream(roleNames.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

                if (!roleList.isEmpty()) {
                    BooleanExpression roleCriteria = QJob.job.assignedTo.roles.any().value.in(roleList);
                    predicate = predicate != null ? roleCriteria.and(predicate) : roleCriteria;
                }
            }
        }

        if (userIds != null && !userIds.isBlank()) {
            List<String> usersIdsList = Arrays.asList(userIds.split(","));

            BooleanExpression userFilter = QJob.job.assignedTo.uuid.in(usersIdsList);
            predicate = (predicate == null) ? userFilter : ((BooleanExpression) predicate).and(userFilter);
        }

        var jobs = jobRepository.findAll(predicate);
        List<JobExportDto> jobExportDtos = new ArrayList<>();
        jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));
        try {

            Document document = new Document();
            writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
            document.open();
            PdfContentByte pdfContentByte = writer.getDirectContent();
            uploadJobPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);
            String latoFontPath = "/Font/Lato-Regular.ttf";
            java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
            if (fontStream == null) {
                throw new IOException("Font file not found");
            }
            BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
                                              fontStream.readAllBytes(), null);
            com.itextpdf.text.Font blackFont =
                    new com.itextpdf.text.Font(bf, 9, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
            ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("Moves by" + " " + role), 290,
                                       720, 0);
            ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER,
                                       new Phrase(startDate + " to " + endDate, blackFont), 290, 700, 0);

            if (jobExportDtos.size() > 60) {

                List<JobExportDto> firstPageEntries = jobExportDtos.subList(0, 60);
                List<JobExportDto> remainingPageEntries = jobExportDtos.subList(61, jobExportDtos.size());

                PdfPTable table = modifyTableInPdf(columnWidths, firstPageEntries, true, isCompleted);
                table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

                List<List<JobExportDto>> paginatedRows = paginate(remainingPageEntries, 74);
                for (List<JobExportDto> pageRows : paginatedRows) {
                    document.newPage();
                    PdfPTable nextTable = modifyTableInPdf(columnWidths, pageRows, false, isCompleted);
                    document.add(nextTable);

                }
            } else {
                PdfPTable table = modifyTableInPdf(columnWidths, jobExportDtos, true, isCompleted);
                table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());
            }

            document.close();
            File pdfFile = new File("iTextTable.pdf");
            return new InputStreamResource(new FileInputStream(pdfFile));

        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }
    }

    private PdfPTable modifyTableInPdf(float[] columnWidths, List<JobExportDto> jobExportData, Boolean isFirstPage,
                                       Boolean isCompleted) {
        PdfPTable table = new PdfPTable(columnWidths);
        if (isFirstPage == true) {
            addTableHeader(table, isCompleted);
        }
        addRows(table, jobExportData, isCompleted);
        table.setTotalWidth(500);

        table.setLockedWidth(true);

        return table;
    }

    private void addTableHeader(PdfPTable table, Boolean isCompleted) {
        try {

            Boolean isClient = false;
            if (AuthDetailsProvider.isRolePresent(CLIENT)) {
                isClient = true;
            }

            String latoFontPath = "/Font/Lato-SemiBold.ttf";
            java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
            if (fontStream == null) {
                throw new IOException("File not found");
            }
            BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
                                              fontStream.readAllBytes(), null);
            com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 7, com.itextpdf.text.Font.NORMAL,
                                                                     BaseColor.BLACK);
            if (isClient) {
                Stream.of("P/U Location", "D/P Location", "Spot Creation DateTime", "P/U DateTime", "D/P DateTime",
                          "Trailer #", "Carrier", "Duration", "Status")
                        .forEach(columnTitle -> {
                            PdfPCell header = new PdfPCell();
                            header.setBorderColor(new BaseColor(221, 221, 223));
                            header.setPhrase(new Phrase(columnTitle, font));
                            header.setHorizontalAlignment(Element.ALIGN_LEFT);
                            header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            header.setFixedHeight(20f);
                            table.addCell(header);
                        });
            } else {
                if (isCompleted) {
                    Stream.of("P/U Location", "D/P Location", "Spot Creation DateTime", "P/U DateTime", "D/P DateTime",
                              "Trailer #", "Carrier", "Duration", "Status", "Completed By")
                            .forEach(columnTitle -> {
                                PdfPCell header = new PdfPCell();
                                header.setBorderColor(new BaseColor(221, 221, 223));
                                header.setPhrase(new Phrase(columnTitle, font));
                                header.setHorizontalAlignment(Element.ALIGN_LEFT);
                                header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                                header.setFixedHeight(20f);
                                table.addCell(header);
                            });
                } else {
                    Stream.of("P/U Location", "D/P Location", "Spot Creation DateTime", "P/U DateTime", "D/P DateTime",
                              "Trailer #", "Carrier", "Duration", "Status", "Assigned To")
                            .forEach(columnTitle -> {
                                PdfPCell header = new PdfPCell();
                                header.setBorderColor(new BaseColor(221, 221, 223));
                                header.setPhrase(new Phrase(columnTitle, font));
                                header.setHorizontalAlignment(Element.ALIGN_LEFT);
                                header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                                header.setFixedHeight(20f);
                                table.addCell(header);
                            });
                }

            }

            if (isClient) {
                for (int i = 0; i < 9; i++) {
                    PdfPCell borderCell = new PdfPCell();
                    borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
                    borderCell.setBorderColor(new BaseColor(221, 221, 223));
                    borderCell.setFixedHeight(5);
                    table.addCell(borderCell);
                }
            } else {
                for (int i = 0; i < 10; i++) {
                    PdfPCell borderCell = new PdfPCell();
                    borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
                    borderCell.setBorderColor(new BaseColor(221, 221, 223));
                    borderCell.setFixedHeight(5);
                    table.addCell(borderCell);
                }
            }

        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }
    }

    private void addRows(PdfPTable table, List<JobExportDto> jobExportDtos, Boolean isCompleted) {
        try {
            Boolean isClient = false;
            if (AuthDetailsProvider.isRolePresent(CLIENT)) {
                isClient = true;
            }
            for (JobExportDto jobExportDto : jobExportDtos) {
                String latoFontPath = "/Font/Lato-Regular.ttf";
                java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
                if (fontStream == null) {
                    // throw new IOException("Font file not found");
                    throw new IOException("File not found");
                }
                BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
                                                  BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
                com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL);
                //				addCellInPDFWithHeading(table, jobExportDto.getJobNumber(), font, BaseColor.WHITE,
                //						new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getPickupLocationName().concat("  ")
                                                .concat(jobExportDto.getPickupSpotName()), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                if (jobExportDto.getDropSpotName() == null) {
                    addCellInPDFWithHeading(table, jobExportDto.getDropLocationName().concat("  ")
                                                    .concat(""), font, BaseColor.WHITE,
                                            new BaseColor(221, 221, 223));
                } else {
                    addCellInPDFWithHeading(table, jobExportDto.getDropLocationName().concat("  ")
                                                    .concat(jobExportDto.getDropSpotName()), font, BaseColor.WHITE,
                                            new BaseColor(221, 221, 223));
                }

                addCellInPDFWithHeading(table, jobExportDto.getCreatedDate(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getPickupDateTime(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getDropDateTime(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getFleetUnitNumber(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getFleetCarrier(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                StringBuilder str = new StringBuilder();
                if (jobExportDto.getStatus() == Status.COMPLETED &&
                    isNotBlank(jobExportDto.getDropDateTime()) && isNotBlank(jobExportDto.getPickupDateTime())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm[:s] a");

                    LocalDateTime start = LocalDateTime.parse(jobExportDto.getPickupDateTime(), formatter);
                    LocalDateTime end = LocalDateTime.parse(jobExportDto.getDropDateTime(), formatter);

                    Duration duration = Duration.between(start, end);

                    long totalSeconds = duration.getSeconds();
                    long hours = totalSeconds / 3600;
                    long minutes = (totalSeconds % 3600) / 60;
                    //			        long seconds = totalSeconds % 60;

                    str.append(hours).append("hrs :");
                    str.append(minutes).append("min :");
                    str.append("0sec");

                } else {
                    str = str.append("");
                }

                addCellInPDFWithHeading(table, str.toString(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                addCellInPDFWithHeading(table, jobExportDto.getStatus().toString(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));
                if (!isClient) {
                    addCellInPDFWithHeading(table, jobExportDto.getAssignedName(), font, BaseColor.WHITE,
                                            new BaseColor(221, 221, 223));
                }

            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }
    }

    private void addRowsInTrailerHistory(PdfPTable table, List<JobExportDto> jobExportDtos) {
        try {
            for (JobExportDto jobExportDto : jobExportDtos) {
                String latoFontPath = "/Font/Lato-Regular.ttf";
                java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
                if (fontStream == null) {
                    // throw new IOException("Font file not found");
                    throw new IOException("File not found");
                }
                BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
                                                  BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
                com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 4, com.itextpdf.text.Font.NORMAL);

                addCellInPDFWithHeading(table, jobExportDto.getCreatedDate(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getPickupDateTime(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getDropDateTime(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getJobNumber(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getPriority().toString(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getPickupLocationName().concat("  ")
                                                .concat(jobExportDto.getPickupSpotName()), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                if (jobExportDto.getDropSpotName() != null) {

                    addCellInPDFWithHeading(table, jobExportDto.getDropLocationName().concat("  ")
                                                    .concat(jobExportDto.getDropSpotName()), font, BaseColor.WHITE,
                                            new BaseColor(221, 221, 223));
                } else {

                    addCellInPDFWithHeading(table, jobExportDto.getDropLocationName(), font, BaseColor.WHITE,
                                            new BaseColor(221, 221, 223));
                }

                addCellInPDFWithHeading(table, jobExportDto.getDescription(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getFleetUnitNumber(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getStatus().toString(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getAssignedName(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

                addCellInPDFWithHeading(table, jobExportDto.getCreatedByUser(), font, BaseColor.WHITE,
                                        new BaseColor(221, 221, 223));

            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }
    }

    private void addTableHeaderInTrailerHistory(PdfPTable table) {
        try {
            String latoFontPath = "/Font/Lato-SemiBold.ttf";
            java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
            if (fontStream == null) {
                throw new IOException("File not found");
            }
            BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
                                              fontStream.readAllBytes(), null);
            com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
                                                                     BaseColor.BLACK);
            Stream.of("Spot Creation DateTime", "P/U DateTime", "D/P DateTime", "Job#", "Priority", "P/U Location",
                      "D/P Location", "Notes", "Trailer #", "Status", "Assigned To", "Created By")
                    .forEach(columnTitle -> {
                        PdfPCell header = new PdfPCell();
                        header.setBorderColor(new BaseColor(221, 221, 223));
                        header.setPhrase(new Phrase(columnTitle, font));
                        header.setHorizontalAlignment(Element.ALIGN_LEFT);
                        header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        header.setFixedHeight(20f);
                        table.addCell(header);
                    });

            for (int i = 0; i < 12; i++) {
                PdfPCell borderCell = new PdfPCell();
                borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
                borderCell.setBorderColor(new BaseColor(221, 221, 223));
                borderCell.setFixedHeight(5);
                table.addCell(borderCell);
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }
    }

    private PdfPTable modifyTrailerHistoryTableInPdf(float[] columnWidths, List<JobExportDto> jobExportData,
                                                     Boolean isFirstPage) {
        PdfPTable table = new PdfPTable(columnWidths);
        if (isFirstPage == true) {
            addTableHeaderInTrailerHistory(table);
        }
        addRowsInTrailerHistory(table, jobExportData);
        table.setTotalWidth(500);

        table.setLockedWidth(true);

        return table;
    }

    private List<List<JobExportDto>> paginate(List<JobExportDto> rows, int rowsPerPage) {

        return Stream.iterate(0, i -> i + rowsPerPage).limit((long) Math.ceil((double) rows.size() / rowsPerPage))
                .map(i -> rows.subList(i, Math.min(i + rowsPerPage, rows.size()))).collect(Collectors.toList());
    }

    private void uploadJobPdfLogo(Document document, String fileName, float imageHorizontalPosition,
                                  float imageVerticalPosition, float imageWidth, float imageHeight)
            throws IOException, DocumentException {
        Image image = Image.getInstance(fileName);
        image.setAbsolutePosition(imageHorizontalPosition, imageVerticalPosition);
        image.scaleAbsoluteWidth(imageWidth);
        image.scaleAbsoluteHeight(imageHeight);
        document.add(image);
    }

    @Override
    public void updateJobPriority() {

        List<Priority> priorities = List.of(Priority.LOW, Priority.MEDIUM);
        List<Job> openJobs = jobRepository.findAllOpenPriorityJobs(priorities);
        for (Job job : openJobs) {

            if (job.getPriorityUpdatedAt().isBefore(ZonedDateTime.now().minusMinutes(30))) {

                if (job.getPriority().equals(Priority.LOW)) {

                    job.setPriority(Priority.MEDIUM);
                    job.setPriorityUpdatedAt(ZonedDateTime.now());

                } else if (job.getPriority().equals(Priority.MEDIUM)) {

                    job.setPriority(Priority.HIGH);
                    job.setPriorityUpdatedAt(ZonedDateTime.now());
                }
                jobRepository.save(job);
            }
        }
    }

    @Override
    @Transactional
    public void updateRouteStatus(String jobId, String routeId, JobStatusRequest jobStatusRequest,
                                  String userId, String timeZone) {

        Job job = jobRepository.findByUuid(jobId)
                .orElseThrow(() -> new ServiceException(ErrorCode.JOB_NOT_FOUND, jobId));

        // Completed job should not be allowed to patched.
        if (job.getStatus().equals(Status.COMPLETED)) {
            throw new ServiceException(ErrorCode.BOXTRUCK_JOB_COMPLETE_PATCH_ERROR);
        }

        // if different driver of same client tries to update state, deny access.
        if (!userId.equals(job.getAssignedTo().getUuid())) {
            throw new ServiceException(ErrorCode.DRIVER_JOB_CREATE_UPDATE_ACCESS);
        }

        if (jobStatusRequest.getNotes() != null) {
            if (!(jobStatusRequest.getNotes().isEmpty())) {
                UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
                String loggedinUserRole = RoleUtils.getRole(loggedInUser);
                jobStatusRequest.setNotes(loggedinUserRole + ":" + jobStatusRequest.getNotes());
            }
        }

        Iterator<JobRouteStep> iterator = job.getJobRouteSteps().iterator();
        ZonedDateTime statusDateTime = getStatusDateTime(jobStatusRequest.getStatusDateTime(), timeZone);

        while (iterator.hasNext()) {
            JobRouteStep jobRouteStep = iterator.next();
            if (jobRouteStep.getUuid().equals(routeId)) {
                if (JobRouteStep.StepStatus.COMPLETED.name().equals(jobStatusRequest.getStatus().name())) {

                    if (!JobRouteStep.StepStatus.IN_PROGRESS.equals(jobRouteStep.getStepStatus())) {
                        // Only In_transit job can be marked as Completed.
                        throw new ServiceException(ErrorCode.BOXTRUCK_JOB_IN_PROGRESS_PATCH_ERROR);
                    } else { // update the status
                        jobRouteStep.setStepStatus(JobRouteStep.StepStatus.COMPLETED);
                        jobRouteStep.setCompletionTime(statusDateTime);
                        jobRouteStep.setNotes(jobStatusRequest.getNotes());

                        // spot updates
                        Spot routeSpot = spotRepository.findActiveByUuid(jobRouteStep.getSpot().getUuid())
                                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND,
                                                                        jobRouteStep.getSpot().getUuid()));
                        routeSpot.setStatus(Spot.Status.EMPTY); // EMPTY after done.
                        routeSpot.setIsOccupied(false);
                        routeSpot.setLastEmptiedTime(statusDateTime);

                        routeSpot.setFleet(null);
                            /*job.getFleet().setSpot(null);
                            List<Spot> previousSpots = spotRepository.findAllByFleet(job.getFleet());
                            previousSpots.forEach(spot -> {
                                spot.setFleet(null);
                                spot.setStatus(com.ma.spoton.api.entities.Spot.Status.EMPTY);
                                spot.setLastEmptiedTime(statusDateTime);
                                spot.setIsOccupied(false);
                            });*/

                        routeSpot.getPickUpJobs().remove(job);
                        Set<Long> jobIds = routeSpot.getPickUpJobs().stream()
                                .map(BaseEntity::getId)
                                .collect(Collectors.toSet());
                        jobIds.remove(job.getId());
                        Set<Job> jobs = jobRepository.findAllByJobIds(jobIds);
                        if (JobRouteStep.StepType.PICK_UP.equals(jobRouteStep.getStepType())) {
                            routeSpot.getPickUpJobs().clear();
                            routeSpot.getPickUpJobs().addAll(jobs);
                        } else {
                            routeSpot.getDropOffJobs().clear();
                            routeSpot.getDropOffJobs().addAll(jobs);
                        }
                        spotRepository.save(routeSpot);

                        long mins = ChronoUnit.MINUTES.between(jobRouteStep.getCompletionTime(),
                                                               jobRouteStep.getStartTime());
                        log.debug("Intermediate {} activity of job {} in location {}, completed in {} minutes.",
                                  jobRouteStep.getStepType(), job.getUuid(),
                                  jobRouteStep.getLocation().getLocationName() + " - " + routeSpot.getSpotName(),
                                  mins);
                    }
                } else if (JobRouteStep.StepStatus.IN_PROGRESS.name().equals(jobStatusRequest.getStatus().name())) {
                    // Only PENDING routes can be marked as IN_TRANSIT
                    if (!JobRouteStep.StepStatus.PENDING.equals(jobRouteStep.getStepStatus())) {
                        // Only In_transit job can be marked as Completed.
                        throw new ServiceException(ErrorCode.BOXTRUCK_JOB_PENDING_PATCH_ERROR);
                    } else { // update the status
                        jobRouteStep.setStepStatus(JobRouteStep.StepStatus.IN_PROGRESS);
                        jobRouteStep.setStartTime(statusDateTime);
                        jobRouteStep.setNotes(jobStatusRequest.getNotes());

                        Spot routeSpot = spotRepository.findActiveByUuid(jobRouteStep.getSpot().getUuid())
                                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND,
                                                                        jobRouteStep.getSpot().getUuid()));
                        Set<Long> jobIds = routeSpot.getPickUpJobs().stream()
                                .map(BaseEntity::getId)
                                .collect(Collectors.toSet());
                        jobIds.remove(job.getId());
                        Set<Job> jobs = jobRepository.findAllByJobIds(jobIds);

                        if (JobRouteStep.StepType.PICK_UP.equals(jobRouteStep.getStepType())) {
                            routeSpot.getPickUpJobs().clear();
                            routeSpot.getPickUpJobs().addAll(jobs);
                        } else {
                            routeSpot.getDropOffJobs().clear();
                            routeSpot.getDropOffJobs().addAll(jobs);
                        }

                        // spot updates
                        routeSpot.setStatus(Spot.Status.OCCUPIED); // currently occupied
                        routeSpot.setIsOccupied(true);
                        routeSpot.setLastOccupiedTime(statusDateTime);

                        routeSpot.setFleet(job.getFleet());
                        /*List<Fleet> fleetBySpot = fleetRepository.findAllBySpot(routeSpot.getId());
                        fleetBySpot.forEach(fleet -> {
                            fleet.setSpot(null);
                        });
                        job.getFleet().setSpot(jobRouteStep.getSpot());*/
                        spotRepository.save(routeSpot);
                    }
                }
                break;
            }
            // if there is a pending or in_transit route prior to current patch request route, throw error.
            if (!JobRouteStep.StepStatus.COMPLETED.equals(jobRouteStep.getStepStatus())) {
                throw new ServiceException(ErrorCode.BOXTRUCK_JOB_PATCH_STATE_ERROR);
            }
        }
    }

    private ZonedDateTime getStatusDateTime(String statusDateTime, String timeZone) {
        return isNotBlank(statusDateTime)
               ? DateTimeUtils.convertStringIntoLocalDateTime(statusDateTime, BusinessConstants.FORM_DATE_TIME_FORMAT,
                                                              ZoneId.of(timeZone), ZoneId.systemDefault())
               : ZonedDateTime.now();
    }

    @Override
    public Resource exportTrailerHistorysAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone) {

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }
        BooleanExpression statusPredicate = QJob.job.status.in(List.of(Status.IN_TRANSIT, Status.COMPLETED));
        predicate = Objects.nonNull(predicate) ? statusPredicate.and(predicate) : statusPredicate;

        var jobs = jobRepository.findAll(predicate);
        List<JobExportDto> jobExportDtos = new ArrayList<>();
        jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));

        try {

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Trailer History");

            // Create header row
            Row headerRow = sheet.createRow(0);
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("SPOT CREATION DATE");
            cell1.setCellStyle(cellStyle);
            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("P/U DATE/TIME");
            cell2.setCellStyle(cellStyle);
            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("DROP DATE/TIME");
            cell3.setCellStyle(cellStyle);
            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("JOB#");
            cell4.setCellStyle(cellStyle);
            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("PRIORITY");
            cell5.setCellStyle(cellStyle);
            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("P/U LOCATION");
            cell6.setCellStyle(cellStyle);
            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("DROP LOCATION");
            cell7.setCellStyle(cellStyle);
            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("NOTES");
            cell8.setCellStyle(cellStyle);
            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("UNIT#");
            cell9.setCellStyle(cellStyle);
            Cell cell10 = headerRow.createCell(9);
            cell10.setCellValue("STATUS");
            cell10.setCellStyle(cellStyle);
            Cell cell11 = headerRow.createCell(10);
            cell11.setCellValue("ASSIGNED TO");
            cell11.setCellStyle(cellStyle);
            Cell cell12 = headerRow.createCell(11);
            cell12.setCellValue("CREATED BY");
            cell12.setCellStyle(cellStyle);

            int rowNum = 1;
            for (JobExportDto jobExportDto : jobExportDtos) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(jobExportDto.getCreatedDate());
                row.createCell(1).setCellValue(jobExportDto.getPickupDateTime());
                row.createCell(2).setCellValue(jobExportDto.getDropDateTime());
                row.createCell(3).setCellValue(jobExportDto.getJobNumber());
                row.createCell(4).setCellValue(jobExportDto.getPriority().toString());
                row.createCell(5).setCellValue(
                        jobExportDto.getPickupLocationName().concat(" ").concat(jobExportDto.getPickupSpotName()));
                if (jobExportDto.getDropSpotName() != null) {
                    row.createCell(6).setCellValue(
                            jobExportDto.getDropLocationName().concat(" ").concat(jobExportDto.getDropSpotName()));
                } else {
                    row.createCell(6).setCellValue(jobExportDto.getDropLocationName());
                }

                row.createCell(7).setCellValue(jobExportDto.getDescription());
                row.createCell(8).setCellValue(jobExportDto.getFleetUnitNumber());
                if (jobExportDto.getStatus() != null) {
                    row.createCell(9).setCellValue(jobExportDto.getStatus().toString());
                }
                row.createCell(10).setCellValue(jobExportDto.getAssignedName());
                row.createCell(11).setCellValue(jobExportDto.getCreatedByUser());

            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
        }
    }

    @Override
    public Resource exportTrailerHistoryAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone) {

        float[] columnWidths = {50f, 50f, 50f, 35f, 35f, 50f, 50f, 50f, 25f, 40f, 50f, 50f};
        PdfWriter writer;

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
        String startDate = "";
        String endDate = "";

        try {
            if (fromDate != null) {
                Date date1 = inputFormat.parse(fromDate);
                startDate = outputFormat.format(date1);
            }

            if (toDate != null) {
                Date date2 = inputFormat.parse(toDate);
                endDate = outputFormat.format(date2);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (isNotBlank(fromDate) && isNotBlank(toDate)) {
            ZonedDateTime fromDateTime =
                    DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
            ZonedDateTime toDateTime =
                    DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
                            .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
            BooleanExpression betweenDateCriteria =
                    QJob.job.createdDate.between(fromDateTime, toDateTime);
            predicate =
                    Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
        }
        BooleanExpression statusPredicate = QJob.job.status.in(List.of(Status.IN_TRANSIT, Status.COMPLETED));
        predicate = Objects.nonNull(predicate) ? statusPredicate.and(predicate) : statusPredicate;

        var jobs = jobRepository.findAll(predicate);
        List<JobExportDto> jobExportDtos = new ArrayList<>();
        jobs.forEach(job -> jobExportDtos.add(jobMapper.mapToExportDto(job, timeZone)));

        try {

            Document document = new Document();
            writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
            document.open();
            PdfContentByte pdfContentByte = writer.getDirectContent();
            uploadJobPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);
            String latoFontPath = "/Font/Lato-Regular.ttf";
            java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
            if (fontStream == null) {
                throw new IOException("Font file not found");
            }
            BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
                                              fontStream.readAllBytes(), null);
            com.itextpdf.text.Font blackFont =
                    new com.itextpdf.text.Font(bf, 8, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
            ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("Trailer History"), 290, 720,
                                       0);
            ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER,
                                       new Phrase(startDate + " to " + endDate, blackFont), 290, 700, 0);

            if (jobExportDtos.size() > 60) {

                List<JobExportDto> firstPageEntries = jobExportDtos.subList(0, 60);
                List<JobExportDto> remainingPageEntries = jobExportDtos.subList(61, jobExportDtos.size());

                PdfPTable table = modifyTrailerHistoryTableInPdf(columnWidths, firstPageEntries, true);
                table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

                List<List<JobExportDto>> paginatedRows = paginate(remainingPageEntries, 74);
                for (List<JobExportDto> pageRows : paginatedRows) {
                    document.newPage();
                    PdfPTable nextTable = modifyTrailerHistoryTableInPdf(columnWidths, pageRows, false);
                    document.add(nextTable);

                }
            } else {
                PdfPTable table = modifyTrailerHistoryTableInPdf(columnWidths, jobExportDtos, true);
                table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());
            }

            document.close();
            File pdfFile = new File("iTextTable.pdf");
            return new InputStreamResource(new FileInputStream(pdfFile));

        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
        }

    }

}