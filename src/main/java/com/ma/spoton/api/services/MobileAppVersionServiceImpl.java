package com.ma.spoton.api.services;

import java.util.List;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.jfree.util.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ma.spoton.api.dtos.MobileAppUpdateDto;
import com.ma.spoton.api.dtos.MobileAppVersionDto;
import com.ma.spoton.api.entities.MobileAppVersion;
import com.ma.spoton.api.mappers.MobileAppVersionMapper;
import com.ma.spoton.api.repositories.MobileAppVersionRepository;
import com.ma.spoton.api.requests.MobileAppVersionRequest;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MobileAppVersionServiceImpl implements MobileAppVersionService{

	@Autowired
	private MobileAppVersionRepository mobileAppVersionRepository;
	
	@Autowired
	private MobileAppVersionMapper mobileAppVersionMapper;
	
	@Override
	public MobileAppUpdateDto getMobileAppUpdatStatus(float currentVersion, String OS) {
		
		MobileAppUpdateDto mobileAppUpdateDto = new MobileAppUpdateDto();
		MobileAppVersion latestMobileAppVersion =  mobileAppVersionRepository.findByOS(OS);
		if(latestMobileAppVersion.getVersion() <= currentVersion)
		{
			mobileAppUpdateDto.setForceUpdate(false);
			mobileAppUpdateDto.setWarning(false);
		}
		else
		{
			mobileAppUpdateDto.setForceUpdate(true);
			mobileAppUpdateDto.setWarning(true);
		}
		
		return mobileAppUpdateDto;
	}

	@Override
	public void updateMobileVersion(float currentVersion,String OS) {
		// TODO Auto-generated method stub
		MobileAppVersion mobileAppVersion =  mobileAppVersionRepository.findByOS(OS);
		if(mobileAppVersion != null)
		{
//		log.info("if({},{})",currentVersion,OS);
//			mobileAppVersion.setAppVersion(currentVersion);
		    mobileAppVersion.setVersion(currentVersion);
			mobileAppVersion.setOs(OS);
			mobileAppVersionRepository.save(mobileAppVersion);	
		}
		else
		{
//			log.info("else(){},{}",currentVersion,OS);
			MobileAppVersion mobileAppVersionEntry = new MobileAppVersion();
//			mobileAppVersionEntry.setAppVersion(currentVersion);
			mobileAppVersionEntry.setVersion(currentVersion);
			mobileAppVersionEntry.setOs(OS);
			mobileAppVersionRepository.save(mobileAppVersionEntry);
		}	
		
	}

	@Override
	public List<MobileAppVersionDto> getLatestMobileAppVersion() {
		
		List<MobileAppVersion> mobileAppVersions =  mobileAppVersionRepository.findAll();
		List<MobileAppVersionDto> mobileAppVersionDtos = mobileAppVersions.stream().map(mobileAppVersion -> mobileAppVersionMapper.maptoDto(mobileAppVersion)).collect(Collectors.toList());
		return mobileAppVersionDtos;
		
	}

}
