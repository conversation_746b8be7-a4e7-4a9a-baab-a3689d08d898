package com.ma.spoton.api.services;

import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.DRIVER;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import javax.validation.ConstraintViolationException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.saml2.provider.service.authentication.DefaultSaml2AuthenticatedPrincipal;
import org.springframework.security.saml2.provider.service.authentication.Saml2AuthenticatedPrincipal;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.RoleDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.dtos.UserExportDto;
import com.ma.spoton.api.dtos.UserUpdateDTO;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.entities.QOverTime;
import com.ma.spoton.api.entities.QUser;
import com.ma.spoton.api.entities.QUserAvailability;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.entities.UserAvailabilityException.Type;
import com.ma.spoton.api.entities.UserDevice;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.ClientMapper;
import com.ma.spoton.api.mappers.LocationMapper;
import com.ma.spoton.api.mappers.OverTimeMapper;
import com.ma.spoton.api.mappers.RoleMapper;
import com.ma.spoton.api.mappers.UserDeviceMapper;
import com.ma.spoton.api.mappers.UserMapper;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.OverTimeRepository;
import com.ma.spoton.api.repositories.RoleRepository;
import com.ma.spoton.api.repositories.UserAvailabilityExceptionRepository;
import com.ma.spoton.api.repositories.UserAvailabilityRepository;
import com.ma.spoton.api.repositories.UserDeviceRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.ActivateInactiveUserRequest;
import com.ma.spoton.api.requests.ActivateUserRequest;
import com.ma.spoton.api.requests.ForgotPasswordRequest;
import com.ma.spoton.api.requests.RegisterUserDeviceRequest;
import com.ma.spoton.api.requests.ResetPasswordRequest;
import com.ma.spoton.api.requests.UpdateProfileRequest;
import com.ma.spoton.api.requests.UserRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.utils.CSVUtils;
import com.ma.spoton.api.utils.SecurityUtils;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class UserServiceImpl implements UserService {

    public final String attributeUrl = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/";

    private final PasswordEncoder encoder;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private UserDeviceRepository userDeviceRepository;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserDeviceMapper userDeviceMapper;
    @Autowired
    private OverTimeRepository overTimeRepository;
    @Autowired
    private OverTimeMapper overTimeMapper;
    @Autowired
    private UserAvailabilityRepository userAvailabilityRepository;
    @Autowired
    private UserAvailabilityExceptionRepository userAvailabilityExceptionRepository;
    @Autowired
    private LocationRepository locationRepository;
    @Autowired
    private LocationMapper locationMapper;

    @Autowired
    public UserServiceImpl(@Lazy PasswordEncoder encoder) {
        this.encoder = encoder;
    }

    private static void isUserAuthorizedToChangePassword(User user) {
        if (Boolean.TRUE.equals(user.getIsADUser())) {
            throw new ServiceException(ErrorCode.USER_UNAUTHORIZED_TO_CHANGE_PASSWORD);
        }
    }

    @Override
    @Transactional
    public User createUser(UserRequest userRequest) {
        //		log.info(">> createUser({})", userRequest);
        User user = userMapper.mapToEntity(userRequest);

        if (CollectionUtils.isNotEmpty(userRequest.getRoleIds())) {
            Set<Role> roles = roleRepository.findAllByUuidIn(userRequest.getRoleIds());
            user.getRoles().addAll(roles);
        }

        if (CollectionUtils.isNotEmpty(userRequest.getClientIds())) {
            Set<Client> clients = clientRepository.findAllByUuidIn(userRequest.getClientIds());
            user.getClients().addAll(clients);
        }

        if (CollectionUtils.isNotEmpty(userRequest.getLocationIds())) {
            Set<Location> locations = locationRepository.findAllByUuidIn(userRequest.getLocationIds());
            user.getLocations().addAll(locations);
        }

        user.setIsActive(false);
        if ((userRequest.getClientIds() != null)) {
            Client client = clientRepository.findActiveByUuid(userRequest.getClientIds().get(0)).orElseThrow(
                    () -> new ServiceException(ErrorCode.USER_NOT_FOUND, userRequest.getClientIds().get(0)));
            user.setTimeZone(client.getTimeZone());
        }
        try {
            String firstName = capitalizeFirstLetter(user.getFirstName());
            String lastName = capitalizeFirstLetter(user.getLastName());
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user = userRepository.save(user);
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while creating User! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.CREATE_UPDATE_USER_DUPLICATE_EMAIL);
        }
        notificationService.createNewUserNotification(user);
        return user;
    }

    @Override
    @Transactional
    public void updateUser(String userId, UserRequest userRequest) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
        user = userMapper.updateEntity(userRequest, user);
        if ((userRequest.getClientIds() != null)) {
            Client client = clientRepository.findActiveByUuid(userRequest.getClientIds().get(0)).orElseThrow(
                    () -> new ServiceException(ErrorCode.USER_NOT_FOUND, userRequest.getClientIds().get(0)));
            user.setTimeZone(client.getTimeZone());
        }

        if (AuthDetailsProvider.isRolePresent(ADMIN) || AuthDetailsProvider.isRolePresent(SUPERVISOR)
            || AuthDetailsProvider.isRolePresent(IT)) {

            if (CollectionUtils.isNotEmpty(userRequest.getRoleIds())) {
                Set<Role> roles = roleRepository.findAllByUuidIn(userRequest.getRoleIds());
                if (roles.stream().map(role -> role.getRoleName()).collect(Collectors.toList()).contains(IT)
                    || roles.stream().map(role -> role.getRoleName()).collect(Collectors.toList()).contains(ADMIN)
                       && AuthDetailsProvider.isRolePresent(SUPERVISOR)) {

                    throw new ServiceException(ErrorCode.INVALID_UPDATE_USER_REQUEST);
                }
                user.getRoles().clear();
                user.getRoles().addAll(roles);
            } else {
                user.getRoles().clear();
            }
        }

        if (CollectionUtils.isNotEmpty(userRequest.getClientIds())) {
            Set<Client> clients = clientRepository.findAllByUuidIn(userRequest.getClientIds());
            user.getClients().clear();
            user.getClients().addAll(clients);
        } else {
            user.getClients().clear();
        }

        if (CollectionUtils.isNotEmpty(userRequest.getLocationIds())) {
            Set<Location> locations = locationRepository.findAllByUuidIn(userRequest.getLocationIds());
            user.getLocations().clear();
            user.getLocations().addAll(locations);
        } else {
            user.getLocations().clear();
        }

    }

    @Override
    @Transactional
    public void deleteUser(String userId) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));

        user.setIsActive(false);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto getUser(String userId, String timeZone, List<String> clientIds) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));

        if (CollectionUtils.isNotEmpty(clientIds) && (CollectionUtils.isEmpty(user.getClients())
                                                      || user.getClients().stream().map(Client::getUuid)
                                                              .noneMatch(clientIds::contains))) {
            throw new ServiceException(ErrorCode.USER_NOT_FOUND, userId);
        }
        return map(user, timeZone);
    }

    @Override
    public List<String> getScheduledBuckets() {
        return Stream.of(User.BucketUsers.values())
                .map(User.BucketUsers::getValue)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<UserDto> getUsers(Predicate predicate, Pageable pageable, String timeZone,
                                           List<String> clientIds, String roleName, Boolean allUsers,
                                           String locationIds, Boolean availableUsersOnly, Boolean ignoreLoggedUser,
                                           String loggedInUserId) {

        List<String> ItAndAdmin = new ArrayList<>(Arrays.asList(RolesConstants.IT, RolesConstants.ADMIN));

        BooleanExpression mandatoryPredicate = QUser.user.clients.any().uuid.in(clientIds);
        BooleanExpression ItAdminPredicate = QUser.user.roles.any().value.in(ItAndAdmin);
        BooleanExpression spotterLocationPredicate = null;
        List<String> locationIdsList = new ArrayList<>();

        if (locationIds != null) {

            locationIdsList = Arrays.asList(locationIds.split(","));
            spotterLocationPredicate = QUser.user.locations.any().uuid.eq(locationIdsList.get(0))
                    .and(QUser.user.locations.any().uuid.eq(locationIdsList.get(1)))
                    .and(QUser.user.roles.any().value.eq(SPOTTER));
        }

        BooleanExpression driverSupervisorPredicate = QUser.user.clients.any().uuid.in(clientIds)
                .and(QUser.user.roles.any().value.in(DRIVER, RolesConstants.SUPERVISOR));

        if (CollectionUtils.isNotEmpty(clientIds) && Objects.isNull(allUsers) &&
            CollectionUtils.isEmpty(locationIdsList)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        if (CollectionUtils.isNotEmpty(clientIds) && Objects.nonNull(allUsers) &&
            CollectionUtils.isEmpty(locationIdsList)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate.or(ItAdminPredicate);
            } else {
                predicate = mandatoryPredicate.or(ItAdminPredicate).and(predicate);
            }
        }

        if (CollectionUtils.isNotEmpty(locationIdsList) && CollectionUtils.isNotEmpty(clientIds) &&
            Objects.isNull(allUsers)) {
            if (Objects.isNull(predicate)) {
                predicate = spotterLocationPredicate.or(driverSupervisorPredicate);
            } else {
                predicate = spotterLocationPredicate.or(driverSupervisorPredicate).and(predicate);
            }
        }

        if (roleName != null) {
            BooleanExpression mandatoryPredicate1 = QUser.user.roles.any().value.in(roleName);
            predicate = mandatoryPredicate1.and(predicate);
        }

        if (Boolean.TRUE.equals(ignoreLoggedUser)) {
            BooleanExpression nonLoggedInUser = QUser.user.uuid.ne(loggedInUserId);
            predicate = nonLoggedInUser.and(predicate);
        }

        if (Boolean.TRUE.equals(availableUsersOnly)) {
            Predicate availableUsersOnlyPredicate = buildPredicate(timeZone);
            predicate = ExpressionUtils.allOf(predicate, availableUsersOnlyPredicate);
        }

        Page<User> usersPage = userRepository.findAll(predicate, pageable);

        return PagedResponse.<UserDto>builder()
                .list(usersPage.stream().map(user -> map(user, timeZone)).collect(Collectors.toList()))
                .page(usersPage.getNumber()).size(usersPage.getSize())
                .totalElements(usersPage.getTotalElements()).build();
    }

    private Predicate buildPredicate(String timezone) {
        ZonedDateTime requestorNow = ZonedDateTime.now(ZoneId.of(timezone));

        // convert requestor's local time to UTC
        ZonedDateTime requestorUtcTime = requestorNow.withZoneSameInstant(ZoneOffset.UTC);

        //        ZonedDateTime requestorUtcTime = ZonedDateTime.now();
        DayOfWeek todayInUtc = requestorUtcTime.getDayOfWeek();
        LocalTime localUtcTime = requestorUtcTime.toLocalTime();

        QUser user = QUser.user;
        QUserAvailability ua = QUserAvailability.userAvailability;
        QOverTime ot = QOverTime.overTime;

        Predicate predicate =
                JPAExpressions.selectOne()
                        .from(ua)
                        .where(
                                ua.user.eq(user)
                                        .and(ua.isActive.eq(true))
                                        .and(ua.dayOfWeek.eq(todayInUtc))
                                        .and(ua.startingTime.lt(localUtcTime))
                                        .and(ua.endingTime.gt(localUtcTime))
                                        .and((ua.breakStartingTime.isNull()
                                                      .and(ua.breakEndingTime.isNull())
                                                      .or(ua.breakStartingTime.gt(localUtcTime)
                                                                  .and(ua.breakEndingTime.lt(localUtcTime))))
                                        )
                        )
                        .exists()
                        // overtime condition
                        .or(
                                JPAExpressions.selectOne()
                                        .from(ot)
                                        .where(
                                                ot.user.eq(user)
                                                        .and(ot.isActive.eq(true))
                                                        .and(ot.startingTime.lt(localUtcTime))
                                                        .and(ot.endingTime.gt(localUtcTime))
                                        )
                                        .exists()
                        );

        return predicate;
    }

    @Override
    @Transactional
    public void updateUserProfile(String userId, UpdateProfileRequest updateProfileRequest) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
        if (StringUtils.isBlank(updateProfileRequest.getTimeZone())) {
            updateProfileRequest.setTimeZone(user.getTimeZone());
        }
        if (StringUtils.isBlank(updateProfileRequest.getTimeZone())) {
            updateProfileRequest.setTimeZone(BusinessConstants.DEFAULT_TIMEZONE);
        }
        user = userMapper.updateEntity(updateProfileRequest, user);

        if (StringUtils.isNotBlank(updateProfileRequest.getNewPassword())) {
            user.setPassword(encoder.encode(updateProfileRequest.getNewPassword()));
        }
    }

    @Override
    @Transactional
    public String resetUserPassword(String userId) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));

        //The password cannot be reset for an Azure AD user.
        isUserAuthorizedToChangePassword(user);

        String tempPassword = SecurityUtils.generateOtp(6);
        user.setPassword(encoder.encode(tempPassword));
        return tempPassword;
    }

    private UserDto map(User user, String timeZone) {

        Long emptiedSinceSeconds = null;
        Long pendingOverTime = null;
        Long remainingPendingOverTime = null;
        Long pendingBreakTime = null;
        Long remainingPendingBreakTimeOnNextDay = null;
        Long breakTimeDuration = null;

        LocalTime dayEndTime = LocalTime.of(23, 59);
        LocalTime dayStartTime = LocalTime.of(0, 0);
        LocalTime noon = LocalTime.of(12, 12);

        UserAvailability userAvailability = null;
        UserAvailabilityException userAvailabilityException = null;
        UserAvailabilityException remainingUserAvailabilityExceptionOnNextDay = null;
        UserDto userDto = userMapper.mapToDto(user);

        if (CollectionUtils.isNotEmpty(user.getClients())) {
            userDto.setClients(user.getClients().stream()
                                       .map(client -> clientMapper.mapToDto(client, timeZone))
                                       .collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(user.getLocations())) {
            userDto.setLocations(user.getLocations().stream()
                                         .map(location -> locationMapper.mapToDto(location, timeZone))
                                         .collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(user.getRoles())) {
            userDto.setRoles(
                    user.getRoles().stream().map(roleMapper::mapToDto).collect(Collectors.toList()));
        }

        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(user.getTimeZone()));
        LocalTime time = now.toLocalTime();
        DayOfWeek day = now.getDayOfWeek();
        LocalDate date = now.toLocalDate();

        List<UserAvailability> userAvailabilities =
                userAvailabilityRepository.findActiveByUserAndDay(user.getUuid(), day, time);
        if (!userAvailabilities.isEmpty()) {
            userAvailability = userAvailabilities.get(0);
        }
        UserAvailability remainingUserAvailabilityOnNextDay =
                userAvailabilityRepository.findActivePreviousDayRemainingAvailability(user.getUuid(), day.plus(1),
                                                                                      dayStartTime);

        List<UserAvailabilityException> userAvailabilityExceptions =
                userAvailabilityExceptionRepository.getPresentTimeAdditionalWorkingUsers(user.getUuid(), date, time);
        if (!userAvailabilityExceptions.isEmpty()) {
            userAvailabilityException = userAvailabilityExceptions.get(0);
        }

        if (userAvailabilityException != null) {
            List<UserAvailabilityException> remainingUserAvailabilityExceptionsOnNextDay =
                    userAvailabilityExceptionRepository.findPreviousDayRemainingUserAvailabilityException(
                            user.getUuid(), date.plusDays(1), dayStartTime, userAvailabilityException.getType());
            if (!remainingUserAvailabilityExceptionsOnNextDay.isEmpty()) {
                remainingUserAvailabilityExceptionOnNextDay = remainingUserAvailabilityExceptionsOnNextDay.get(0);
            }
        }

        if (Objects.nonNull(user.getIdleSince())) {
            if (userAvailability != null) {
                if (userAvailability.getBreakStartingTime() != null && userAvailability.getBreakEndingTime() != null) {

                    //during break time
                    if (userAvailability.getBreakStartingTime().isBefore(time) && userAvailability.getBreakEndingTime()
                            .isAfter(time)) {
                        userDto.setIsOnBreak(true);
                        //no job is done during break time till now
                        if (userAvailability.getBreakStartingTime().isAfter(
                                user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone())).toLocalTime())) {
                            emptiedSinceSeconds = ChronoUnit.SECONDS.between(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime(), userAvailability.getBreakStartingTime());
                            userDto.setIdleTime(emptiedSinceSeconds);

                        }
                        //job is done during break time uptill now
                        else if (userAvailability.getBreakStartingTime().isBefore(
                                user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone())).toLocalTime())) {
                            userDto.setIdleTime(null);
                        }
                        userDto.setIsOnBreak(true);
                    }
                    //after break time
                    else if (userAvailability.getBreakEndingTime().isBefore(time)) {
                        //last job is done during break time
                        if (userAvailability.getBreakStartingTime().isBefore(
                                user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone())).toLocalTime())
                            && userAvailability.getBreakEndingTime().isAfter(
                                user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone())).toLocalTime())) {
                            //NOTE
                            //need to update code during dropoff
                            emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                            userDto.setIdleTime(emptiedSinceSeconds);
                        }
                        //last job was done before break time
                        else if (userAvailability.getBreakStartingTime().isAfter(
                                user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone())).toLocalTime())) {
                            emptiedSinceSeconds = ChronoUnit.SECONDS.between(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime(), userAvailability.getBreakStartingTime()) +
                                                  ChronoUnit.SECONDS.between(userAvailability.getBreakEndingTime(),
                                                                             time);
                            userDto.setIdleTime(emptiedSinceSeconds);

                        }
                        //last job is done after break time
                        else {
                            emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                            userDto.setIdleTime(emptiedSinceSeconds);

                        }

                    }
                    //before break time
                    else {
                        emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                        userDto.setIdleTime(emptiedSinceSeconds);
                    }
                } else {
                    emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                    userDto.setIdleTime(emptiedSinceSeconds);
                }

            }
            if (userAvailabilityException != null) {
                if (userAvailabilityException.getType().equals(Type.WORKING_DAY)) {
                    if (userAvailabilityException.getBreakStartingTime() != null
                        && userAvailabilityException.getBreakEndingTime() != null) {
                        //during break time
                        if (userAvailabilityException.getBreakStartingTime().isBefore(time)
                            && userAvailabilityException.getBreakEndingTime().isAfter(time)) {
                            userDto.setIsOnBreak(true);
                            //no job is done during break time till now
                            if (userAvailabilityException.getBreakStartingTime().isAfter(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime())) {
                                emptiedSinceSeconds = ChronoUnit.SECONDS.between(
                                        user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                                .toLocalTime(), userAvailabilityException.getBreakStartingTime());
                                userDto.setIdleTime(emptiedSinceSeconds);

                            }
                            //job is done during break time uptill now
                            else if (userAvailabilityException.getBreakStartingTime().isBefore(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime())) {
                                userDto.setIdleTime(null);
                            }
                            userDto.setIsOnBreak(true);
                        }
                        //after break time
                        else if (userAvailabilityException.getBreakEndingTime().isBefore(time)) {
                            //last job is done during break time
                            if (userAvailabilityException.getBreakStartingTime().isBefore(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime()) && userAvailabilityException.getBreakEndingTime().isAfter(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime())) {
                                //NOTE
                                //need to update code during dropoff
                                emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                                userDto.setIdleTime(emptiedSinceSeconds);
                            }
                            //last job was done before break time
                            else if (userAvailabilityException.getBreakStartingTime().isAfter(
                                    user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                            .toLocalTime())) {
                                emptiedSinceSeconds = ChronoUnit.SECONDS.between(
                                        user.getIdleSince().withZoneSameInstant(ZoneId.of(user.getTimeZone()))
                                                .toLocalTime(), userAvailabilityException.getBreakStartingTime()) +
                                                      ChronoUnit.SECONDS.between(
                                                              userAvailabilityException.getBreakEndingTime(), time);
                                userDto.setIdleTime(emptiedSinceSeconds);

                            }
                            //last job is done after break time
                            else {
                                emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                                userDto.setIdleTime(emptiedSinceSeconds);

                            }

                        }
                        //before break time
                        else {
                            emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                            userDto.setIdleTime(emptiedSinceSeconds);
                        }
                    } else {
                        emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                        userDto.setIdleTime(emptiedSinceSeconds);
                    }

                } else if (userAvailabilityException.getType().equals(Type.DAY_OFF)) {
                    userDto.setIdleTime(null);

                }
            }
            //if userAvailability is not present
            if (userAvailability == null && userAvailabilityException == null) {
                emptiedSinceSeconds = ChronoUnit.SECONDS.between(user.getIdleSince(), now);
                userDto.setIdleTime(emptiedSinceSeconds);
            }

        }

        OverTime overTime = overTimeRepository.findByUserAndDate(user.getUuid(), date);
        if (overTime != null) {

            pendingOverTime = ChronoUnit.SECONDS.between(time, overTime.getEndingTime());
            userDto.setPendingOverTime(pendingOverTime);

            if (overTime.getEndingTime().equals(dayEndTime)) {

                OverTime remainingNextDayOverTime =
                        overTimeRepository.findByUserDateAndStartTime(user.getUuid(), date.plusDays(1), dayStartTime);
                if (remainingNextDayOverTime != null) {

                    remainingPendingOverTime = ChronoUnit.SECONDS.between(remainingNextDayOverTime.getStartingTime(),
                                                                          remainingNextDayOverTime.getEndingTime());
                    pendingOverTime = pendingOverTime + remainingPendingOverTime;
                    userDto.setPendingOverTime(pendingOverTime);

                }
            }
        }

        if (userAvailability != null) {
            if (userAvailability.getBreakStartingTime() != null && userAvailability.getBreakEndingTime() != null) {
                if (userAvailability.getBreakStartingTime().isBefore(time) && userAvailability.getBreakEndingTime()
                        .isAfter(time)) {
                    pendingBreakTime = ChronoUnit.SECONDS.between(time, userAvailability.getBreakEndingTime());
                    userDto.setPendingBreakTime(pendingBreakTime);
                }

                if (userAvailability.getEndingTime().equals(dayEndTime) && remainingUserAvailabilityOnNextDay != null
                    && pendingBreakTime != null) {
                    if (remainingUserAvailabilityOnNextDay.getBreakStartingTime() != null
                        && remainingUserAvailabilityOnNextDay.getBreakEndingTime() != null) {
                        remainingPendingBreakTimeOnNextDay =
                                ChronoUnit.SECONDS.between(remainingUserAvailabilityOnNextDay.getBreakStartingTime(),
                                                           remainingUserAvailabilityOnNextDay.getBreakEndingTime());
                        pendingBreakTime = pendingBreakTime + remainingPendingBreakTimeOnNextDay;
                        userDto.setPendingBreakTime(pendingBreakTime);
                    }

                }
            }
        }

        if (userAvailabilityException != null) {
            if (userAvailabilityException.getType().equals(Type.WORKING_DAY)) {
                if (userAvailabilityException.getBreakStartingTime() != null
                    && userAvailabilityException.getBreakEndingTime() != null) {
                    if (userAvailabilityException.getBreakStartingTime().isBefore(time)
                        && userAvailabilityException.getBreakEndingTime().isAfter(time)) {
                        pendingBreakTime =
                                ChronoUnit.SECONDS.between(time, userAvailabilityException.getBreakEndingTime());
                        userDto.setPendingBreakTime(pendingBreakTime);
                    }
                }

                if (userAvailabilityException.getEndingTime().equals(dayEndTime)
                    && remainingUserAvailabilityExceptionOnNextDay != null && pendingBreakTime != null) {
                    if (remainingUserAvailabilityExceptionOnNextDay.getBreakStartingTime() != null
                        && remainingUserAvailabilityExceptionOnNextDay.getBreakEndingTime() != null) {
                        remainingPendingBreakTimeOnNextDay = ChronoUnit.SECONDS.between(
                                remainingUserAvailabilityExceptionOnNextDay.getBreakStartingTime(),
                                remainingUserAvailabilityExceptionOnNextDay.getBreakEndingTime());
                        pendingBreakTime = pendingBreakTime + remainingPendingBreakTimeOnNextDay;
                        userDto.setPendingBreakTime(pendingBreakTime);
                    }
                }

            } else if (userAvailabilityException.getType().equals(Type.DAY_OFF)) {
                userDto.setPendingBreakTime(null);
            }
        }

        return userDto;

    }

    @Override
    @Transactional
    public void createforgotPasswordToken(ForgotPasswordRequest forgotPasswordRequest) {
        User user = userRepository.findActiveUserByEmail(forgotPasswordRequest.getEmail())
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND_BY_EMAIL,
                                                        forgotPasswordRequest.getEmail()));

        //No email should be sent for the Azure AD user to change the password.
        isUserAuthorizedToChangePassword(user);

        user.setForgotPasswordToken(SecurityUtils.generateOtp(8));
        notificationService.createAndSendForgotPasswordNotification(user);
    }

    @Override
    @Transactional
    public void createResetPassword(ResetPasswordRequest resetPasswordRequest) {
        User user = userRepository.findActiveUserByForgotPasswordToken(resetPasswordRequest.getToken())
                .orElseThrow(() -> new ServiceException(ErrorCode.ERROR_RESET_PASSWORD_USER_NOT_FOUND));
        user.setForgotPasswordToken(null);
        user.setPassword(encoder.encode(resetPasswordRequest.getConfirmNewPassword()));
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportUsersAsCSV(Predicate predicate, String timeZone, List<String> clientIds) {
        BooleanExpression mandatoryPredicate = QUser.user.clients.any().uuid.in(clientIds);

        if (CollectionUtils.isNotEmpty(clientIds)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        var users = userRepository.findAll(predicate);

        List<UserExportDto> userExportDtos = new ArrayList<>();
        users.forEach(user -> {
            UserExportDto userDto = userMapper.mapToExportDto(user, timeZone);
            userDto
                    .setRoles(user.getRoles().stream().map(Role::getRoleName).collect(Collectors.joining()));
            userExportDtos.add(userDto);
        });

        try {
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName =
                    tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
            try (OutputStream outputStream = new FileOutputStream(fileName);) {
                String csvData = CSVUtils.toCSV(userExportDtos, ',', true);
                outputStream.write(csvData.getBytes());
                outputStream.flush();
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting CSV!", e);
            throw new ServiceException(ErrorCode.USER_EXPORT, e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportUsersAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds) {
        BooleanExpression mandatoryPredicate = QUser.user.clients.any().uuid.in(clientIds);

        if (CollectionUtils.isNotEmpty(clientIds)) {
            if (Objects.isNull(predicate)) {
                predicate = mandatoryPredicate;
            } else {
                predicate = mandatoryPredicate.and(predicate);
            }
        }

        var users = userRepository.findAll(predicate);

        List<UserExportDto> userExportDtos = new ArrayList<>();
        users.forEach(user -> {
            UserExportDto userDto = userMapper.mapToExportDto(user, timeZone);
            userDto
                    .setRoles(user.getRoles().stream().map(Role::getRoleName).collect(Collectors.joining()));
            userExportDtos.add(userDto);
        });

        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Users");
            Row headerRow = sheet.createRow(0);

            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());

            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // Create cells with bold values
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("User Id");
            cell1.setCellStyle(cellStyle);

            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("First Name");
            cell2.setCellStyle(cellStyle);

            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("Last Name");
            cell3.setCellStyle(cellStyle);

            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("Email");
            cell4.setCellStyle(cellStyle);

            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("Phone");
            cell5.setCellStyle(cellStyle);

            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("Time Zone");
            cell6.setCellStyle(cellStyle);

            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("Last Login Time");
            cell7.setCellStyle(cellStyle);

            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("Is Active");
            cell8.setCellStyle(cellStyle);

            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("Role");
            cell9.setCellStyle(cellStyle);
            int rowNum = 1;
            for (UserExportDto userExportDto : userExportDtos) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(userExportDto.getUserId());
                row.createCell(1).setCellValue(userExportDto.getFirstName());
                row.createCell(2).setCellValue(userExportDto.getLastName());
                row.createCell(3).setCellValue(userExportDto.getEmail());
                row.createCell(4).setCellValue(userExportDto.getPhone());
                row.createCell(5).setCellValue(userExportDto.getTimeZone());
                row.createCell(6).setCellValue(userExportDto.getLastLoginTime());
                if (userExportDto.getIsActive() == true) {
                    row.createCell(7).setCellValue("true");
                } else {
                    row.createCell(7).setCellValue("false");
                }
                row.createCell(8).setCellValue(userExportDto.getRoles());

            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }

        } catch (Exception e) {
            log.error("Error occurred while exporting EXCEL!", e);
            throw new ServiceException(ErrorCode.USER_EXPORT, e.getMessage());
        }
    }

    @Override
    @Transactional
    public void createOrUpdateUserDevice(RegisterUserDeviceRequest userDeviceRequest, String userId) {
        User user = userRepository.findActiveByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));

        if (Objects.nonNull(userDeviceRequest.getDeviceUuid())) {

            UserDevice userDevice = userDeviceRepository.findOneDeviceByUuid(userDeviceRequest.getDeviceUuid());
            if (Objects.nonNull(userDevice)) {

                userDevice = userDeviceMapper.updateEntity(userDeviceRequest, userDevice);
                userDevice.setUser(user);
            } else {

                userDevice = userDeviceMapper.mapToEntity(userDeviceRequest);
                userDevice.setUser(user);
            }
            try {
                userDeviceRepository.save(userDevice);
            } catch (Exception e) {
                log.error("Some error occurred while creating user device.", e);
                throw new ServiceException(ErrorCode.CREATE_USER_DEVICE);
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> getRolesByNames(List<String> roleNames) {
        return roleRepository.findAllByNames(roleNames);
    }

    @Override
    @Transactional(readOnly = true)
    public UserUpdateDTO getUser(String userId) {
        User user = userRepository.findByUuidWithRolesAndClients(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));

        UserUpdateDTO userDTO = new UserUpdateDTO(user);
        return userDTO;
    }

    @Override
    public void updateLastActiveTime(String userId) {
        User user = userRepository.findByUserUuid(userId);
        user.setLastActiveTime(ZonedDateTime.now());
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void sendInactiveAccountDeletionAlert() {
        int num = 0;
        int del = 0;
        List<Client> allClients = clientRepository.findAll();
        List<Client> requiredClients = new ArrayList<>();
        for (Client client : allClients) {
            if (client.getAccountDeactivation() == null) {
                client.setAccountDeactivation(false);
                clientRepository.save(client);
            }
            if (client.getAccountDeactivation() == true) {
                requiredClients.add(client);
            }
        }
        List<String> clientIds = requiredClients.stream()
                .map(client -> client.getUuid())
                .collect(Collectors.toList());
        BooleanExpression mandatoryPredicate = QUser.user.clients.any().uuid.in(clientIds);
        Iterable<User> usersIterable = userRepository.findAll(mandatoryPredicate);
        List<User> users = StreamSupport.stream(usersIterable.spliterator(), false)
                .collect(Collectors.toList());
        for (User user : users) {
            User user1 = userRepository.findOneActiveByUuid(user.getUuid());
            if (user1 != null) {
                if (user1.getLastActiveTime() == null) {
                    user1.setLastActiveTime(ZonedDateTime.now());
                    userRepository.save(user1);
                }
                Boolean deleteUserFlag = isDateRequiredDaysBeforeToday(user1.getLastActiveTime(), 35);
                if (deleteUserFlag == true) {
                    deleteUser(user1.getUuid());
                    del++;
                    continue;
                }
                Boolean sendEmailFlag = isDateRequiredDaysBeforeToday(user1.getLastActiveTime(), 30);
                if (sendEmailFlag == true) {
                    notificationService.createAndSendDeleteAccountAlertNotification(user1);
                    num++;
                }
            }
        }
    }

    @Transactional
    public boolean isDateRequiredDaysBeforeToday(ZonedDateTime inputDate, int noOfDays) {
        ZonedDateTime currentDate = ZonedDateTime.now(inputDate.getZone());
        ZonedDateTime date25DaysAgo = currentDate.minusDays(noOfDays);
        return inputDate.isBefore(date25DaysAgo);
    }

    @Override
    public UserDto getNewUser(String userId) {
        User user = userRepository.findOneInactiveByUuid(userId).
                orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
        return userMapper.mapToDto(user);
    }

    @Override
    @Transactional
    public void activateUser(ActivateUserRequest activateUserRequest) {
        // TODO Auto-generated method stub
        User user = userRepository.findOneInactiveByUuid(activateUserRequest.getUserId()).
                orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, activateUserRequest.getUserId()));
        user.setPassword(encoder.encode(activateUserRequest.getPassword()));
        user.setIsActive(true);
        userRepository.save(user);
    }

    @Override
    public void sendMailtoInactiveUser(ActivateInactiveUserRequest activateInactiveUserRequest) {
        User user = userRepository.findOneInactiveByUuid(activateInactiveUserRequest.getUserId()).
                orElseThrow(
                        () -> new ServiceException(ErrorCode.USER_NOT_FOUND, activateInactiveUserRequest.getUserId()));
        notificationService.createNewUserNotification(user);
    }

    @Override
    public String capitalizeFirstLetter(String word) {
        if (word == null || word.isEmpty()) {
            return word;
        } else {
            char firstChar = word.charAt(0);
            if (Character.isLowerCase(firstChar)) {
                return Character.toUpperCase(firstChar) + word.substring(1);
            } else {
                return word;
            }
        }
    }

    /**
     * This api is specifically designed for the client: Dynacraft
     */
    @Override
    @Transactional
    public void addUserFromAzureAD(Authentication authentication, String emailAddress, String password,
                                   String roleUser) {
        User newUser = new User();

        newUser.setFirstName(capitalizeFirstLetter(getClaimValue(authentication, "givenname")));
        newUser.setLastName(capitalizeFirstLetter(getClaimValue(authentication, "surname")));
        newUser.setEmail(emailAddress);
        newUser.setPassword(encoder.encode(password));
        newUser.setIsADUser(true);

        Client client = clientRepository.findUsingUuid("d55063f2-3d5b-4678-a91e-f6219d9c0015");
        newUser.getClients().add(client);
        newUser.setTimeZone(client.getTimeZone());

        Set<Role> roles = new HashSet<>(roleRepository.findAllByNames(List.of(RolesConstants.CLIENT)));
        newUser.getRoles().add(roles.stream().findFirst().get());

        userRepository.save(newUser);

    }

    public String getClaimValue(Authentication authentication, String claim) {
        Saml2AuthenticatedPrincipal saml2AuthenticatedPrincipal =
                (DefaultSaml2AuthenticatedPrincipal) authentication.getPrincipal();
        return saml2AuthenticatedPrincipal.getAttribute(attributeUrl.concat(claim)).stream()
                .map(String::valueOf)
                .findAny()
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NAME_NOT_FOUND, getClaimName(claim)));
    }

    private String getClaimName(String claim) {
        String claimName = null;
        switch (claim) {
            case "givenname":
                claimName = "First Name";
                break;
            case "surname":
                claimName = "Last Name";
                break;
            default:
                break;
        }
        return claimName;
    }

    @Override
    @Transactional
    public void resetIdleTime() {

        Set<String> roles = new HashSet<>();
        roles.add(DRIVER);
        roles.add(SPOTTER);

        List<User> users = userRepository.findAllByRole(roles);
        for (User user : users) {
            ZonedDateTime now = ZonedDateTime.now(ZoneId.of(user.getTimeZone()));
            DayOfWeek day = now.getDayOfWeek();
            LocalTime time = now.toLocalTime();
            LocalDate date = now.toLocalDate();
            UserAvailability presentUserAvailability = null;
            String presentuserAvailabilityId = null;
            String presentuserAvailabilityExceptionId = null;
            UserAvailabilityException presentUserAvailabilityException = null;
            Boolean onShift = false;

            List<UserAvailability> userAvailabilities =
                    userAvailabilityRepository.findAllActiveByUserAndDay(user.getUuid(), day);
            for (UserAvailability userAvailability : userAvailabilities) {
                if (userAvailability.getStartingTime().isBefore(time) && userAvailability.getEndingTime()
                        .isAfter(time)) {
                    presentuserAvailabilityId = userAvailability.getUuid();
                    break;
                }
            }

            if (presentuserAvailabilityId == null) {
                for (UserAvailability userAvailability : userAvailabilities) {
                    if (userAvailability.getEndingTime().isBefore(time)) {
                        //We are taking the last finished availability because the user may be in overtime
                        presentuserAvailabilityId = userAvailability.getUuid();
                    }
                }
            }

            if (presentuserAvailabilityId != null) {
                presentUserAvailability = userAvailabilityRepository.findByAvailabilityId(presentuserAvailabilityId);
            }

            if (presentUserAvailability != null) {
                if (presentUserAvailability.getEndingTime().isBefore(time)) {
                    OverTime overTime = overTimeRepository.findByUserAndDate(user.getUuid(), date);
                    if (overTime == null) {
                        user.setIdleSince(null);
                        user.setIsIdleClear(true);
                        userRepository.save(user);
                    } else if (overTime.getEndingTime().isBefore(time) && overTime.getDate().isEqual(date)) {
                        user.setIdleSince(null);
                        user.setIsIdleClear(true);
                        userRepository.save(user);
                    }
                } else if ((presentUserAvailability.getStartingTime().equals(time)
                            || presentUserAvailability.getStartingTime().isBefore(time)) && user.getIsIdleClear()
                                   .equals(true)) {

                    user.setIdleSince(now);
                    user.setIsIdleClear(false);
                    userRepository.save(user);
                } else if (presentUserAvailability.getStartingTime().isAfter(time)) {
                    user.setIdleSince(null);
                    user.setIsIdleClear(true);
                    userRepository.save(user);
                } else if ((presentUserAvailability.getStartingTime().equals(time)
                            || presentUserAvailability.getStartingTime().isBefore(time))) {
                    onShift = true;
                }

            }

            List<UserAvailabilityException> userAvailabilityExceptions =
                    userAvailabilityExceptionRepository.getAllPresentDayAdditionalWorkingUsers(user.getUuid(), date);
            for (UserAvailabilityException userAvailabilityException : userAvailabilityExceptions) {
                if (userAvailabilityException.getStartingTime().isBefore(time)
                    && userAvailabilityException.getEndingTime().isAfter(time)) {
                    presentuserAvailabilityExceptionId = userAvailabilityException.getUuid();
                    break;
                }
            }
            if (presentuserAvailabilityExceptionId == null) {
                for (UserAvailabilityException userAvailabilityException : userAvailabilityExceptions) {
                    if (userAvailabilityException.getEndingTime().isBefore(time)) {
                        presentuserAvailabilityExceptionId = userAvailabilityException.getUuid();
                    }
                }
            }
            if (presentuserAvailabilityExceptionId != null) {
                presentUserAvailabilityException =
                        userAvailabilityExceptionRepository.findByAvailabilityExceptionId(presentuserAvailabilityId);
            }

            if (presentUserAvailabilityException != null) {
                if (presentUserAvailabilityException.getType().equals(Type.WORKING_DAY)) {
                    if (presentUserAvailabilityException.getEndingTime().isBefore(time) && onShift == false) {
                        OverTime overTime = overTimeRepository.findByUserAndDate(user.getUuid(), date);
                        if (overTime == null) {
                            user.setIdleSince(null);
                            user.setIsIdleClear(true);
                            userRepository.save(user);
                        } else if (overTime.getEndingTime().isBefore(time) && overTime.getDate().isEqual(date)) {
                            user.setIdleSince(null);
                            user.setIsIdleClear(true);
                            userRepository.save(user);
                        }
                    } else if ((presentUserAvailabilityException.getStartingTime().equals(time)
                                || presentUserAvailabilityException.getStartingTime().isBefore(time))
                               && user.getIsIdleClear().equals(true)) {
                        user.setIdleSince(now);
                        user.setIsIdleClear(false);
                        userRepository.save(user);
                    }
                } else if (presentUserAvailabilityException.getType().equals(Type.DAY_OFF)) {
                    if (presentUserAvailabilityException.getStartingTime().isBefore(time)
                        && presentUserAvailabilityException.getEndingTime().isAfter(time)) {
                        user.setIdleSince(null);
                        user.setIsIdleClear(true);
                        userRepository.save(user);

                    }
                }
            }

            List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
            if (presentUserAvailability == null && presentUserAvailabilityException == null && allOverTime.isEmpty()) {
                user.setIdleSince(null);
                user.setIsIdleClear(true);
                userRepository.save(user);
            }

            if (presentUserAvailabilityException != null) {
                if (presentUserAvailability == null && presentUserAvailabilityException.getType()
                        .equals(Type.DAY_OFF)) {
                    user.setIdleSince(null);
                    user.setIsIdleClear(true);
                    userRepository.save(user);
                }
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<RoleDto> getRoles(List<String> clientIds, String client) {
        if ((clientIds == null || clientIds.isEmpty()) && client != null) {
            clientIds = List.of(client);
        }
        List<Role> roles = new ArrayList<>();
        List<Role> allRoles = roleRepository.findAllByOrderByIdAsc();
        if (clientIds != null && !clientIds.isEmpty()) {
            for (String clients : clientIds) {
                for (Role role : allRoles) {
                    Role tempRole = roleRepository.findRoleById(role.getId());
                    if (tempRole != null) {
                        Role foundRole = roleRepository.findRolesByClientIds(clients, role.getId());
                        if (foundRole != null) {
                            roles.add(role);
                        }
                    } else {
                        roles.add(role);
                    }
                }
            }

        } else {
            roles.addAll(roleRepository.findAllUnassignedRoles());
        }

        if (!AuthDetailsProvider.isRolePresent(ADMIN) && !AuthDetailsProvider.isRolePresent(IT)) {
            roles = roles.stream()
                    .filter(role -> !role.getRoleName().equalsIgnoreCase(RolesConstants.IT) && !role.getRoleName()
                            .equalsIgnoreCase(RolesConstants.ADMIN))
                    .collect(Collectors.toList());
        }

        return roles.stream()
                .map(roleMapper::mapToDto)
                .collect(Collectors.toList());
    }
}
