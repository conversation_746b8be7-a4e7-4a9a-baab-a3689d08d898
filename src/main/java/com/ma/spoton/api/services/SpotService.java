package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.dtos.FleetStatusCountDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.requests.SpotRequest;
import com.querydsl.core.types.Predicate;

public interface SpotService {

    void createSpot(String clientId, SpotRequest spotRequest);

    void updateSpot(String clientId, String spotId, SpotRequest spotRequest);

    void deleteSpot(String clientId, String spotId);

    SpotDto getSpot(String clientId, String spotId, String timeZone);

    PagedResponse<SpotDto> getSpots(String clientId, Predicate predicate, Pageable pageable,
                                    String timeZone, String locationIds, String lastUpdated, String fleet_status);

    Resource exportSpotsAsCSV(String clientId, Predicate predicate, String timeZone);

    Resource exportSpotsAsEXCEL(String clientId, Predicate predicate, String timeZone);

    PagedResponse<SpotDto> getDropdownSpots(String clientId, Predicate predicate, Pageable pageable,
                                            String timeZone, String fleet_status);

    void activateSpot(String clientId, String spotId);

    List<FleetStatusCountDto> countOfFleetStatus(String clientId, String locationId);
}
