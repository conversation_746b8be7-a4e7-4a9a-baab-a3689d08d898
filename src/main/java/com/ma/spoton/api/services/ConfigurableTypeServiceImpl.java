package com.ma.spoton.api.services;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.cache.configurabletype.ConfigurableCacheLoaderFactory;
import com.ma.spoton.api.cache.configurabletype.ConfigurableTypeCacheLoader;
import com.ma.spoton.api.config.ConfigurableTypeMapperRegistry;
import com.ma.spoton.api.constants.ConfigurableType;
import com.ma.spoton.api.dtos.ConfigurableEntityDTO;
import com.ma.spoton.api.dtos.ConfigurationResponseDTO;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.ConfigurableTypeEntity;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.ConfigurableTypeRepository;
import com.ma.spoton.api.requests.ConfigurationSaveRequest;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class ConfigurableTypeServiceImpl<T extends ConfigurableTypeEntity>
        implements ConfigurableTypeService<T> {

    private final ConfigurableTypeRepository<T> repository;
    private final Class<T> typeClass;
    private final ConfigurableType type;
    private final Consumer<T> deleteFunction;

    @Autowired
    private ConfigurableTypeMapperRegistry configurableTypeMapperRegistry;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ConfigurableCacheLoaderFactory configurableCacheLoaderFactory;

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(readOnly = true)
    public <D extends ConfigurableEntityDTO> ConfigurationResponseDTO<D> getValues(String clientId) {
        ConfigurableTypeCacheLoader<T> cacheLoader = configurableCacheLoaderFactory.getCacheLoader(type.name());
        List<T> values = cacheLoader.getValues(clientId);

        List<D> dtoList = values.stream()
                .map(t -> (D) configurableTypeMapperRegistry.mapToDto(typeClass, t))
                .collect(Collectors.toList());

        ConfigurationResponseDTO<D> response = new ConfigurationResponseDTO<>();
        response.setClientId(clientId);
        response.setType(type.name());
        response.setValues(dtoList);
        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<ConfigurationResponseDTO<ConfigurableEntityDTO>> getAllClientValues(Pageable pageable) {
        Page<Client> allClients = clientRepository.findAll(pageable);

        List<ConfigurationResponseDTO<ConfigurableEntityDTO>> result = allClients.stream()
                .map(client -> getValues(client.getUuid()))
                .collect(Collectors.toList());

        return PagedResponse.<ConfigurationResponseDTO<ConfigurableEntityDTO>>builder()
                .list(result)
                .page(allClients.getNumber())
                .size(allClients.getSize())
                .totalElements(allClients.getTotalElements())
                .build();
    }

    @Override
    @Transactional
    public void createIfNotExists(Set<String> values, boolean global) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }

        Set<T> existing = repository.findByValueIn(values);
        Set<String> existingValues = existing.stream().map(T::getValue).collect(Collectors.toSet());

        values.stream()
                .filter(v -> !existingValues.contains(v))
                .forEach(v -> addValue(v, null, global));
    }

    @Override
    @Transactional
    public void clearValues(String clientId) {
        if (StringUtils.isBlank(clientId)) {
            return;
        }

        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

        Set<T> clientValues = new HashSet<>(client.getConfigurableValues(type));
        // Remove associations (this deletes from join table)
        for (T entity : clientValues) {
            client.getConfigurableValues(type).remove(entity);
            entity.getClients().remove(client);
            repository.save(entity); // join table updated
        }

        clientRepository.save(client);

        // Remove orphan non-global entities safely
        Set<T> orphans = repository.findAllOrphanNonGlobalEntities();
        for (T entity : orphans) {
            if (entity.getClients().isEmpty()) { // only delete if no other clients reference it
                deleteFunction.accept(entity);
            }
        }

        // Clear cache
        configurableCacheLoaderFactory.getCacheLoader(type.name()).deleteAllData(clientId);
    }

    @Override
    @Transactional
    public void saveValues(ConfigurationSaveRequest request) {
        Set<String> newValues = request.getValues();
        if (CollectionUtils.isEmpty(newValues)) {
            return;
        }

        String clientId = request.getClientId();
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

        ConfigurableTypeCacheLoader<T> cacheLoader = configurableCacheLoaderFactory.getCacheLoader(type.name());

        Set<T> currentValues = repository.findByClientUuid(clientId);
        Map<String, T> currentMap = currentValues.stream()
                .collect(Collectors.toMap(T::getValue, e -> e));

        // Handle deletions
        Set<String> removed = currentMap.keySet().stream()
                .filter(val -> !newValues.contains(val))
                .collect(Collectors.toSet());

        for (String val : removed) {
            T entity = currentMap.get(val);
            client.getConfigurableValues(type).remove(entity);
            entity.getClients().remove(client);
            repository.save(entity);               // remove the mapping
            if (entity.getClients().isEmpty()) {
                deleteFunction.accept(entity);  // delete the entity;
            }
            cacheLoader.deleteData(clientId, entity);
        }

        // Handle additions
        for (String val : newValues) {
            T entity = repository.findByValue(val).orElse(null);
            if (entity == null) {
                // reuse addValue() for creating entity + mapping
                addValue(val, clientId, false);
            } else {
                // ensure mapping exists
                if (!entity.getClients().contains(client)) {
                    entity.getClients().add(client);
                    client.getConfigurableValues(type).add(entity);
                    repository.save(entity);
                    cacheLoader.addToClientCache(clientId, val);
                }
            }
        }
    }

    private T addValue(String value, String clientId, boolean global) {
        Client client = null;
        if (StringUtils.isNotBlank(clientId)) {
            client = clientRepository.findActiveByUuid(clientId)
                    .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        }

        try {
            T entity = typeClass.getDeclaredConstructor().newInstance();
            entity.setValue(value);
            entity.setGlobal(global);
            repository.save(entity);

            if (client != null) {
                client.getConfigurableValues(type).add(entity);
                entity.getClients().add(client);
                clientRepository.save(client);

                configurableCacheLoaderFactory.getCacheLoader(type.name()).addToClientCache(clientId, value);
            }
            return entity;
        } catch (Exception e) {
            throw new ServiceException(ErrorCode.CONFIGURABLE_TYPE_ADD_ERROR, typeClass.getSimpleName());
        }
    }
}
