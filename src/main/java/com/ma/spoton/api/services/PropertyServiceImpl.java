package com.ma.spoton.api.services;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.dtos.PropertyDto;
import com.ma.spoton.api.entities.Property;
import com.ma.spoton.api.entities.Property.AccessType;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.PropertyMapper;
import com.ma.spoton.api.repositories.PropertyRepository;
import com.ma.spoton.api.requests.PropertyRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyServiceImpl implements PropertyService {

  private final PropertyRepository propertyRepository;

  @Autowired
  private PropertyMapper propertyMapper;

  @Override
  @Transactional
  public void createProperty(PropertyRequest propertyRequest) {
//    log.info(">> createProperty({})", propertyRequest);
    propertyRepository.save(propertyMapper.mapToEntity(propertyRequest));
  }

  @Override
  @Transactional
  public PropertyDto getPropertyById(String propertyId, String timezone) {
//    log.info(">> getPropertyById({})", propertyId);
    return propertyMapper.mapToDto(findProperty(propertyId), timezone);
  }

  @Override
  @Transactional
  public void updateProperty(String propertyId, PropertyRequest propertyRequest) {
//    log.info(">> updateProperty({})", propertyId, propertyRequest);
    var property = findProperty(propertyId);
    propertyMapper.updateEntity(propertyRequest, property);
    propertyRepository.save(property);
  }

  @Override
  @Transactional
  public void deleteProperty(String propertyId) {
//    log.info(">> deleteProperty({})", propertyId);
    propertyRepository.delete(findProperty(propertyId));
  }

  @Override
  @Transactional
  public List<PropertyDto> getProperties(String timezone, boolean showArchived,
      boolean showAuditDetails) {
//    log.info(">> getProperties({}, {}, {})", timezone, showArchived, showAuditDetails);
    List<Property> propertyList = showArchived ? propertyRepository.findAll()
        : propertyRepository.findAllByIsActive(!showArchived);
    return showAuditDetails
        ? propertyList.stream().map(property -> propertyMapper.mapToDto(property, timezone))
            .collect(toList())
        : propertyList.stream().map(property -> propertyMapper.mapToPropertyDto(property))
            .collect(toList());
  }

  @Override
  @Transactional
  public Map<String, Object> getPropertyData() {
//    log.info(">> getPropertyData()");
    List<Property> properties =
        propertyRepository.findAllByIsActiveAndAccessType(true, AccessType.PUBLIC);
    return properties.stream().collect(toMap(Property::getKey, Property::getValue));
  }

  @Override
  @Transactional
  public Property findProperty(String propertyId) {
//    log.info(">> findProperty({})", propertyId);
    return propertyRepository.findPropertyByUuid(propertyId)
        .orElseThrow(() -> new ServiceException(ErrorCode.PROPERTY_NOT_FOUND, propertyId));
  }

  @Override
  @Transactional
  public Property findPropertyByKey(String key) {
    Optional<Property> optional = propertyRepository.findPropertyByKey(key);
    return optional.isPresent() ? optional.get() : null;
  }

  @Override
  @Transactional
  public Property findPropertyByKey(String key, String defaultValue) {
    Optional<Property> optional = propertyRepository.findPropertyByKey(key);
    return optional.isPresent() ? optional.get() : new Property(key, defaultValue);
  }

}
