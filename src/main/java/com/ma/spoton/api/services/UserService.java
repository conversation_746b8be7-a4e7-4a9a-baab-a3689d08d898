package com.ma.spoton.api.services;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.RoleDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.dtos.UserUpdateDTO;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.requests.ActivateInactiveUserRequest;
import com.ma.spoton.api.requests.ActivateUserRequest;
import com.ma.spoton.api.requests.ForgotPasswordRequest;
import com.ma.spoton.api.requests.RegisterUserDeviceRequest;
import com.ma.spoton.api.requests.ResetPasswordRequest;
import com.ma.spoton.api.requests.UpdateProfileRequest;
import com.ma.spoton.api.requests.UserRequest;
import com.querydsl.core.types.Predicate;

public interface UserService {

    User createUser(UserRequest userRequest);

    void updateUser(String userId, UserRequest userRequest);

    void deleteUser(String userId);

    UserDto getUser(String userId, String timeZone, List<String> clientIds);

    PagedResponse<UserDto> getUsers(Predicate predicate, Pageable pageable, String timeZone,
                                    List<String> clientIds, String roleName, Boolean allUsers, String locationIds,
                                    Boolean availableUsersOnly, Boolean ignoreLoggedUser, String loggedInUserId);

    List<String> getScheduledBuckets();

    void updateUserProfile(String userId, UpdateProfileRequest updateProfileRequest);

    String resetUserPassword(String userId);

    void createforgotPasswordToken(ForgotPasswordRequest forgotPasswordRequest);

    void createResetPassword(ResetPasswordRequest resetPasswordRequest);

    Resource exportUsersAsCSV(Predicate predicate, String timeZone, List<String> clientIds);

    Resource exportUsersAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds);

    void createOrUpdateUserDevice(RegisterUserDeviceRequest userDeviceRequest, String userId);

    List<Role> getRolesByNames(List<String> roleNames);

    UserUpdateDTO getUser(String userId);

    void updateLastActiveTime(String userId);

    void sendInactiveAccountDeletionAlert();

    public boolean isDateRequiredDaysBeforeToday(ZonedDateTime inputDate, int noOfDays);

    UserDto getNewUser(String userId);

    void activateUser(ActivateUserRequest activateUserRequest);

    void sendMailtoInactiveUser(ActivateInactiveUserRequest activateInactiveUserRequest);

    String capitalizeFirstLetter(String word);

    void addUserFromAzureAD(Authentication authentication, String emailAddress, String password, String roleUser);

    void resetIdleTime();

    List<RoleDto> getRoles(List<String> clientIds, String client);
}
