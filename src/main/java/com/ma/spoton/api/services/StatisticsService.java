package com.ma.spoton.api.services;

import java.util.List;
import java.util.Optional;
import com.ma.spoton.api.dtos.JobStatisticsDto;
import com.ma.spoton.api.dtos.MessageStatisticsDto;
import com.ma.spoton.api.dtos.TotalStatisticsDto;

public interface StatisticsService {

  TotalStatisticsDto getTotals(List<String> clientIds);

  JobStatisticsDto getJobStatistics(Optional<String> fromDateTimeOptional,
      Optional<String> toDateTimeOptional, String timeZone, List<String> clientIds);

  MessageStatisticsDto getMessageStatistics(Optional<String> fromDateTimeOptional,
      Optional<String> toDateTimeOptional, String timeZone, String userId);

}
