package com.ma.spoton.api.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserPreferences;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.UserPreferencesRepository;
import com.ma.spoton.api.repositories.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class UserPreferencesServiceImpl implements UserPreferencesService {

    private final UserPreferencesRepository userPreferencesRepository;
    private final UserRepository userRepository;

    private final ObjectMapper objectMapper;

    @Autowired
    public UserPreferencesServiceImpl(UserPreferencesRepository userPreferencesRepository,
                                      UserRepository userRepository,
                                      ObjectMapper objectMapper) {
        this.userPreferencesRepository = userPreferencesRepository;
        this.objectMapper = objectMapper;
        this.userRepository = userRepository;
    }

    @Override
    public void saveUserWidgetPreferences(String userId, List<String> widgets) {
        try {
            User user = userRepository.findByUuid(userId)
                    .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
            UserPreferences preferences = userPreferencesRepository.findByUserId(user.getId())
                    .orElseGet(() -> {
                        UserPreferences newPreferences = new UserPreferences();
                        User user1 = new User();
                        user1.setId(user.getId()); // Explicitly setting the ID
                        newPreferences.setUser(user1);
                        return newPreferences;
                    });

            preferences.setWidgetPreferences(objectMapper.writeValueAsString(widgets));
            userPreferencesRepository.save(preferences);

        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to save widget preferences", e);
        }
    }

    @Override
    public void saveLayout(String userId, String layout) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
        UserPreferences preferences = userPreferencesRepository.findByUserId(user.getId())
                .orElseGet(() -> {
                    UserPreferences newPreferences = new UserPreferences();
                    User user1 = new User();
                    user1.setId(user.getId()); // Explicitly setting the ID
                    newPreferences.setUser(user1);
                    return newPreferences;
                });

        preferences.setLayout(layout);
        userPreferencesRepository.save(preferences);
    }

    @Override
    public List<String> getUserWidgetPreferences(String userId) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
        return userPreferencesRepository.findByUserId(user.getId())
                .map(preferences -> {
                    try {
                        return objectMapper.readValue(preferences.getWidgetPreferences(), new TypeReference<List<String>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("Failed to retrieve widget preferences", e);
                    }
                })
                .orElse(Collections.emptyList());
    }

    @Override
    public String getLayout(String userId) {
        User user = userRepository.findByUuid(userId)
                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
       Optional<UserPreferences> optionalUserPreferences =userPreferencesRepository.findByUserId(user.getId());
       if(optionalUserPreferences.isPresent()) {
           return optionalUserPreferences.get().getLayout();
       }
       else return "";
    }


    @Transactional
    @Override
    public void removePreferences(String userId) {

            User user = userRepository.findByUuid(userId)
                    .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userId));
            userPreferencesRepository.deleteByUserId(user.getId());
    }
}
