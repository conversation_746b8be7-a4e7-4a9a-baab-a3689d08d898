package com.ma.spoton.api.services;

import java.util.List;
import java.util.Map;

import com.ma.spoton.api.dtos.DayStats;
import com.ma.spoton.api.dtos.InTransitSpots;
import com.ma.spoton.api.dtos.SpotCountsDTO;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.dtos.SpotIdNameDTO;
import com.ma.spoton.api.dtos.WeeklyTotalSpotsDTO;

public interface DashboardService {

    Map<String, Integer> getAvgMovesOfClient(String clientId,
                                             String timeZone,
                                             Boolean refreshCache);

    DayStats getHourlyAvgMoves(String clientId,
                               String timeZone,
                               Boolean refreshCache);

    /**
     * This function returns all In_Transit spots for a given clientId.
     *
     * @param clientId     Client UUID
     * @param timeZone     User login timezone (can be a non-client like IT, ADMIN..)
     * @param refreshCache boolean flag to fetch data from cache if true or query DB otherwise.
     * @return {@link InTransitSpots} object
     */
    InTransitSpots getInTransitSpots(
            String clientId,
            String timeZone,
            Boolean refreshCache);

    /**
     * This function returns Total spots completed in the given week (week start) or the previous week (default)
     *
     * @param clientId     Client UUID
     * @param timeZone     User login timezone (can be a non-client like IT, ADMIN..)
     * @param weekStart    Optional param , start of the week (monday date) in yyyy-MM-dd format.
     * @param refreshCache boolean flag to fetch data from cache if true or query DB otherwise.
     * @return {@link WeeklyTotalSpotsDTO} object.
     */
    WeeklyTotalSpotsDTO getWeeklyTotalSpots(String clientId, String timeZone,
                                            String weekStart, Boolean refreshCache);

    /**
     * This function returns list of all active Spots (spotId, spotName) of the given location uuid.
     *
     * @param locationId   Location id
     * @param refreshCache boolean flag to fetch data from cache if true or query DB otherwise.
     * @return {@link SpotDto} object.
     */
    List<SpotIdNameDTO> getAllSpotsOfLocation(String locationId, Boolean refreshCache);

    /**
     * @param clientId     Client UUID
     * @param timeZone     User login timezone (can be a non-client like IT, ADMIN..)
     * @param spotId       Spot Id.
     * @param daysBefore   Number of day's data to be fetched (7 by default)
     * @param refreshCache boolean flag to fetch data from cache if true or query DB otherwise.
     * @return {@link SpotCountsDTO} object.
     */
    SpotCountsDTO getSpotCountsOfDays(String clientId, String timeZone, String spotId,
                                      Integer daysBefore, Boolean refreshCache);
}
