package com.ma.spoton.api.services;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface FileService {

  void upload(byte[] fileData, String path, String fileName, String contentType) throws IOException;

  void delete(String path, String fileName);

  InputStream download(String path, String fileName);

  void copy(String sourcePath, String destinationPath);

  void upload(InputStream fileData, String path, String fileName, String contentType)
      throws IOException;
  
  void createFolderAndSaveBol(String folderName,List<MultipartFile> files,String jobId,String type);
  
  void entryExitBillOfLandingImage(String folderName, Long guardEntryExitId, MultipartFile file);
  
 
}
