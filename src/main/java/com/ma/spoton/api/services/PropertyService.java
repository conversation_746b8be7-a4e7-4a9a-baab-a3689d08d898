package com.ma.spoton.api.services;

import java.util.List;
import java.util.Map;
import com.ma.spoton.api.dtos.PropertyDto;
import com.ma.spoton.api.entities.Property;
import com.ma.spoton.api.requests.PropertyRequest;

public interface PropertyService {

  void createProperty(PropertyRequest propertyRequest);

  void updateProperty(String propertyId, PropertyRequest propertyRequest);

  Property findProperty(String propertyId);

  void deleteProperty(String propertyId);

  List<PropertyDto> getProperties(String timezone, boolean showArchived, boolean showAuditDetails);

  Map<String, Object> getPropertyData();

  Property findPropertyByKey(String key);

  PropertyDto getPropertyById(String propertyId, String timezone);

  Property findPropertyByKey(String key, String defaultValue);

}
