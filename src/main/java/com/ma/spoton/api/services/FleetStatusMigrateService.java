package com.ma.spoton.api.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.config.FleetStatusMigrator;

@Component
public class FleetStatusMigrateService {

    @Autowired
    private FleetStatusMigrator fleetStatusMigrator;

    @Transactional
    public void migrate(String tableName) {
        fleetStatusMigrator.migrate(tableName);
    }
}
