package com.ma.spoton.api.services;

import java.util.Set;

import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.constants.ConfigurableType;
import com.ma.spoton.api.dtos.ConfigurableEntityDTO;
import com.ma.spoton.api.dtos.ConfigurationResponseDTO;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.ConfigurableTypeEntity;
import com.ma.spoton.api.requests.ConfigurationSaveRequest;

public interface ConfigurableTypeService<T extends ConfigurableTypeEntity> {

    ConfigurableType getType();

    <D extends ConfigurableEntityDTO> ConfigurationResponseDTO<D> getValues(String clientId);

    PagedResponse<ConfigurationResponseDTO<ConfigurableEntityDTO>> getAllClientValues(Pageable pageable);

    void saveValues(ConfigurationSaveRequest configurationSaveRequest);

    void clearValues(String clientId);

    void createIfNotExists(Set<String> values, boolean global);
}
