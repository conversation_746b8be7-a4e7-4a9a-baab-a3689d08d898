package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ma.spoton.api.entities.UserDevice;
import com.ma.spoton.api.repositories.UserDeviceRepository;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class UserDeviceServiceImpl implements UserDeviceService{

	@Autowired
	UserDeviceRepository userdevicerepository;
	
		

	@Override
	public void deleteDeviceRegistrationId(String deviceUuid) {
		
	   UserDevice userDevice=userdevicerepository.findOneDeviceByUuid(deviceUuid);
	   if(userDevice != null) {
		   
		   userDevice.setDeviceRegistrationId(null);
		   userdevicerepository.save(userDevice);
	   }
	}



	@Override
	public void updateDeviceRegistrationId(String deviceUuid,String deviceRegistrationId) {
		
		UserDevice userdevice=userdevicerepository.findOneDeviceByUuid(deviceUuid);
		if(userdevice!=null)
		{
		userdevice.setDeviceRegistrationId(deviceRegistrationId);
		userdevicerepository.save(userdevice);
		}
		
		
	}

}
