package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.config.SwaggerConfig;
import com.ma.spoton.api.dtos.FleetAgeReportDto;
import com.ma.spoton.api.dtos.FleetCarrierUpdate;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.FleetMergeDto;
import com.ma.spoton.api.dtos.GuardEntryExitDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.TrailerLogDto;
import com.ma.spoton.api.requests.ClientFleetRequest;
import com.ma.spoton.api.requests.FleetRequest;
import com.ma.spoton.api.requests.TrailerAuditReportExportRequest;
import com.querydsl.core.types.Predicate;

public interface FleetService {

    FleetDto createFleet(FleetRequest fleetRequest, List<String> clientIds);

    void updateFleet(String fleetId, FleetRequest fleetRequest);

    void deleteFleet(String fleetId);

    FleetDto getFleet(String fleetId, String timeZone, List<String> clientIds);

    PagedResponse<FleetDto> getFleets(Predicate predicate, Pageable pageable, String timeZone, List<String> clientIds,
                                      String locationIds, String unit_sequenceNumber, String sortDirection,
                                      String fleet_status);

    Page<String> getUniqueCarrierPage(String carrier, Pageable pageable);

    void assignFleets(String clientId, ClientFleetRequest assignFleetRequest);

    void assignFleetsAfterMerging(FleetMergeDto fleetMergeDto);

    void unassignFleets(String clientId, ClientFleetRequest clientFleetRequest);

    PagedResponse<GuardEntryExitDto> getFleetEntryExits(Predicate predicate, Pageable pageable,
                                                        String timeZone, List<String> clientIds, String fromDate,
                                                        String toDate);

    Resource exportFleetsAsCSV(Predicate predicate, String timeZone, List<String> clientIds, String fleet_status);

    Resource exportFleetsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String locationIds,
                                 String fleet_status);

    Resource exportReportFleetsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds,
                                       TrailerAuditReportExportRequest trailerAuditReportExportRequest);

    Resource exportFleetEntryExitsAsCSV(Predicate predicate, String timeZone, List<String> clientIds);

    Resource exportFleetEntryExitsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String fromDate,
                                          String toDate);

    Resource exportEntryExitsReportAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds,
                                           String fromDate, String toDate);

    Resource exportEntryExitsReportAsPDF(Predicate predicate, String timeZone, List<String> clientIds, String fromDate,
                                         String toDate);

    void deactivateFleet(String fleetId);

    void activateFleet(String fleetId);

    boolean mergeFleet(FleetMergeDto fleetMergeDto);

    List<String> getUniqueCarrier(String carrier);

    List<String> getUniqueCarrier();

    int updateCarrierInFleet(FleetCarrierUpdate fleetCarrierUpdate);

    Resource exportFleetsAsPDF(Predicate predicate, String timeZone, List<String> clientIds, String locationIds,
                               String fleet_status);

    Resource exportReportFleetsAsPDF(Predicate predicate, String timeZone, List<String> clientIds,
                                     TrailerAuditReportExportRequest trailerAuditReportExportRequest);

    PagedResponse<TrailerLogDto> getTrailerLogs(Predicate predicate, Pageable pageable, String timeZone,
                                                List<String> clientIds, String fromDate, String toDate);

    Resource exportTrailerHistorysAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone);

    Resource exportTrailerHistoryAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone);

    PagedResponse<FleetAgeReportDto> getFleetAgeReport(Predicate predicate, SwaggerConfig.PageableSwagger pageable,
                                                       String timeZone,
                                                       String clientId);

    Resource exportFleetAgeReportAsExcel(Predicate predicate, String timeZone, String clientId);

    Resource exportFleetAgeReportAsPdf(Predicate predicate, String timeZone, String clientId);
}
