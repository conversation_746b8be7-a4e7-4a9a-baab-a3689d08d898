package com.ma.spoton.api.services;

import com.ma.spoton.api.controllers.TurnAroundTimeController;
import com.ma.spoton.api.dtos.DockTurnAroundTimeDTO;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Service
public class DockTurnAroundTimeService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private LocationRepository locationRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(DockTurnAroundTimeService.class);


    // Cache to store dwell time results
    private static class TurnAroundTimeCache {
        String clientId;
        List<DockTurnAroundTimeDTO> turnAroundTimes;

        TurnAroundTimeCache(String clientId, List<DockTurnAroundTimeDTO> turnAroundTimes) {
            this.clientId = clientId;
            this.turnAroundTimes = turnAroundTimes;
        }

        boolean isValid(String clientId) {
            return this.clientId.equals(clientId);
        }
    }

    // Thread-safe cache using ConcurrentHashMap
    private final ConcurrentHashMap<String, TurnAroundTimeCache> turnAroundTimeCacheMap = new ConcurrentHashMap<>();


    public List<DockTurnAroundTimeDTO> calculateAverageTurnAroundTime(String clientId, boolean refreshCache) {

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateCacheKey(clientId);
        if (!refreshCache) {
            TurnAroundTimeCache cache = turnAroundTimeCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(clientId)) {
                return cache.turnAroundTimes;
            }
        }

        // Fetch dock turn around time data
        List<JobRepository.DockTurnAroundTimeProjection> turnAroundTimes = jobRepository.findDockTurnAroundTimeForClient(clientId);
        LOGGER.info("Fetched Records size: {}", turnAroundTimes.size());

        // Calculate total turn around time and count per location
        Map<Long, Double> totalTurnAroundHours = new HashMap<>();
        Map<Long, Integer> turnAroundCounts = new HashMap<>();

        for (JobRepository.DockTurnAroundTimeProjection dockTurnAroundTimeProjection : turnAroundTimes) {
            if (dockTurnAroundTimeProjection.getSpotOccupiedTime() != null && dockTurnAroundTimeProjection.getSpotCreatedTime() != null) {
                long turnAroundMinutes = ChronoUnit.MINUTES.between(dockTurnAroundTimeProjection.getSpotOccupiedTime(), dockTurnAroundTimeProjection.getSpotCreatedTime());
                double turnAroundHours = turnAroundMinutes / 60.0;
                Long locationId = dockTurnAroundTimeProjection.getLocationId();

                totalTurnAroundHours.merge(locationId, turnAroundHours, Double::sum);
                turnAroundCounts.merge(locationId, 1, Integer::sum);
            }
        }

        // Fetch all locations for the client
        List<Location> activeLocations = locationRepository.findActiveLocationsByClientId(clientId);
        List<DockTurnAroundTimeDTO> result = new ArrayList<>();

        // Calculate average turn around time per location
        for (Location location : activeLocations) {
            DockTurnAroundTimeDTO dto = new DockTurnAroundTimeDTO();
            dto.setLocationId(location.getId());
            dto.setLocationName(location.getLocationName());
            Double totalHours = totalTurnAroundHours.getOrDefault(location.getId(), 0.0);
            Integer count = turnAroundCounts.getOrDefault(location.getId(), 0);

            double averageTurnAroundTime = (count > 0) ? (totalHours / count ) : 0.0;
            dto.setAverageTurnAroundTimeHours((double)Math.round(averageTurnAroundTime));
            result.add(dto);
        }

        // Cache the result
        turnAroundTimeCacheMap.put(cacheKey, new TurnAroundTimeCache(clientId, result));

        return result;
    }


    private String generateCacheKey(String clientId) {
        return clientId;
    }
}
