//package com.ma.spoton.api.services;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
//import javax.validation.ConstraintViolationException;
//
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.dao.DataIntegrityViolationException;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.ma.spoton.api.dtos.CarrierDto;
//import com.ma.spoton.api.dtos.FleetDto;
//import com.ma.spoton.api.dtos.PagedResponse;
//import com.ma.spoton.api.entities.Carriers;
//import com.ma.spoton.api.entities.Client;
//import com.ma.spoton.api.entities.Fleet;
//import com.ma.spoton.api.entities.QFleet;
//import com.ma.spoton.api.entities.QJob;
//import com.ma.spoton.api.entities.QSpot;
//import com.ma.spoton.api.entities.Job.FleetStatus;
//import com.ma.spoton.api.entities.QCarriers;
//import com.ma.spoton.api.exception.ErrorCode;
//import com.ma.spoton.api.exception.ServiceException;
//import com.ma.spoton.api.mappers.CarriersMapper;
//import com.ma.spoton.api.repositories.CarriersRepository;
//import com.ma.spoton.api.repositories.FleetRepository;
//import com.ma.spoton.api.requests.CarriersRequest;
//import com.ma.spoton.api.requests.ClientRequest;
//import com.querydsl.core.types.Predicate;
//import com.querydsl.core.types.dsl.BooleanExpression;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//@Service
//public class CarriersServiceImpl implements CarriersService{
//
//	@Autowired
//	private CarriersMapper carriersMapper;
//	
//	@Autowired
//	private CarriersRepository carriersRepository;
//	
//	@Autowired
//	private FleetRepository fleetRepository;
//	
//	@Override
//	@Transactional
//	public CarrierDto createCarrier(CarriersRequest carriersRequest) {
//	    Carriers carrier = carriersMapper.mapToEntity(carriersRequest);
////	    client.setBol(false);
////	    client.setDvir(false);
//	    Carriers savedCarrier = null;
//	    try {
//	    	savedCarrier = carriersRepository.save(carrier);
//	    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
//	      log.error("Error occurred while creating carrier! Reason : {}", e.getMessage());
//	      throw new ServiceException(ErrorCode.DUPLICATE_CARRIER);
//	    }
//	    return carriersMapper.mapToDto(savedCarrier);
//	}
//	
//	@Override
//	@Transactional(readOnly = true)
//	public PagedResponse<CarrierDto> getCarriers(Predicate predicate, Pageable pageable) {
//
//		Page<Carriers> carriersPage = carriersRepository.findAll(predicate, pageable);
//
//
//		return PagedResponse.<CarrierDto>builder()
//			.list(carriersPage.stream().map(carrier -> carriersMapper.mapToDto(carrier))
//				.collect(Collectors.toList()))
//			.page(carriersPage.getNumber()).size(carriersPage.getSize())
//			.totalElements(carriersPage.getTotalElements()).build();
//	}
//	
//	@Override
//	@Transactional
//	public void deleteCarrier(String carrierId) {
//		Carriers carriers = carriersRepository.findByUuid(carrierId)
//				.orElseThrow(() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND, carrierId));
//		long fleetCount = fleetRepository.count(QFleet.fleet.carriers.eq(carriers));
//		if (fleetCount > 0) {
//			carriers.setIsActive(false);
//		} else {
//			carriersRepository.delete(carriers);
//		}
//	}
//	
//	@Override
//	@Transactional
//	public void updateCarrier(String carrierId, CarriersRequest carriersRequest) {
//		Carriers carrier = carriersRepository.findByUuid(carrierId)
//				.orElseThrow(() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND, carrierId));
//		carrier.setCarrier(carriersRequest.getCarrier());
//		carriersRepository.save(carrier);
//	}
//	
//	
//}
