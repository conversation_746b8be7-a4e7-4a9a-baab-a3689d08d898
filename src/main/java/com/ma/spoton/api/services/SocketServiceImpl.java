package com.ma.spoton.api.services;



import org.jfree.util.Log;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Service
public class SocketServiceImpl extends TextWebSocketHandler {

	private static Set<WebSocketSession> sessions = ConcurrentHashMap.newKeySet();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
    	 
    	sessions.add(session);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        
    	sessions.remove(session);
    }
    
    public void sendMessageToClient(String message) {
        try {
            if (sessions.size() > 0) {
            	
                TextMessage textMessage = new TextMessage(message);
                for (WebSocketSession session : sessions) {
                    if (session.isOpen()) {
                    	
                    	session.sendMessage(textMessage);
                    }
              }
            }
            else {
                System.out.println("Session is null or not open.");
            }
        } catch (IOException e) {
            System.out.println("Error occurred while sending message: " + e.getMessage());
        }
    }
}
