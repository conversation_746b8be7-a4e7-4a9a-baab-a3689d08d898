package com.ma.spoton.api.services;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.constants.MessageMode;
import com.ma.spoton.api.dtos.MessageDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.ContactUs;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.Message.Status;
import com.ma.spoton.api.entities.QMessage;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.AuditMapper;
import com.ma.spoton.api.mappers.ContactUsMapper;
import com.ma.spoton.api.mappers.JobMapper;
import com.ma.spoton.api.mappers.MessageMapper;
import com.ma.spoton.api.repositories.ContactUsRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.MessageRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.ContactUsRequest;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.requests.MessageStatusRequest;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.handler.TextWebSocketHandler;
//import org.springframework.messaging.simp.SimpMessagingTemplate;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

  @Autowired
  private MessageRepository messageRepository;
  
  @Autowired
  private ContactUsRepository contactUsRepository;

  @Autowired
  private UserRepository userRepository;

  @Autowired
  private LocationRepository locationRepository;

  @Autowired
  private SpotRepository spotRepository;

  @Autowired
  private FleetRepository fleetRepository;

  @Autowired
  private MessageMapper messageMapper;
  
  @Autowired
  private ContactUsMapper contactUsMapper;

  @Autowired
  private AuditMapper auditMapper;
  
  @Autowired
  private JobMapper jobMapper;
  
  @Autowired
  private NotificationService notificationService;
  
  @Autowired
  private SocketServiceImpl socketServiceImpl;
  
  @Override
  @Transactional
  public void createMessage(String fromUserId, MessageRequest messageRequest) {
//    log.info(">> createMessage({}, {})", fromUserId, messageRequest);
   
    List<User> toUsers = userRepository.findAllActiveByUserIds(messageRequest.getToUserIds());
    if (toUsers.isEmpty()) {
      throw new ServiceException(ErrorCode.CREATE_MESSAGE_NO_TO_USER);
    }

    User fromUser = userRepository.findByUuid(fromUserId)
        .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, fromUserId));

    Location clientLocation = StringUtils.isNotBlank(messageRequest.getClientLocationId())
        ? locationRepository.findActiveByUuid(messageRequest.getClientLocationId())
            .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                messageRequest.getClientLocationId()))
        : null;

    Fleet fleet = StringUtils.isNotBlank(messageRequest.getFleetId())
        ? fleetRepository.findByUuid(messageRequest.getFleetId()).orElseThrow(
            () -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, messageRequest.getFleetId()))
        : null;

    Location pickupLocation = StringUtils.isNotBlank(messageRequest.getPickupLocationId())
        ? locationRepository.findByUuid(messageRequest.getPickupLocationId())
            .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                messageRequest.getPickupLocationId()))
        : null;

    Spot pickupSpot = StringUtils.isNotBlank(messageRequest.getPickupSpotId())
        ? spotRepository.findByUuid(messageRequest.getPickupSpotId()).orElseThrow(
            () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, messageRequest.getPickupSpotId()))
        : null;

    Location dropLocation = StringUtils.isNotBlank(messageRequest.getDropLocationId())
        ? locationRepository.findByUuid(messageRequest.getDropLocationId())
            .orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND,
                messageRequest.getDropLocationId()))
        : null;

    Spot dropSpot = StringUtils.isNotBlank(messageRequest.getDropSpotId())
        ? spotRepository.findByUuid(messageRequest.getDropSpotId()).orElseThrow(
            () -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, messageRequest.getDropSpotId()))
        : null;

    toUsers.forEach(toUser -> {
      	
      Message message = messageMapper.mapToEntity(messageRequest);
      message.setFromUser(fromUser);
      message.setToUser(toUser);
      message.setStatus(Status.NEW);
      message.setClientLocation(clientLocation);
      message.setFleet(fleet);
      message.setPickupLocation(pickupLocation);
      message.setPickupSpot(pickupSpot);
      message.setDropLocation(dropLocation);
      message.setDropSpot(dropSpot);
      messageRepository.save(message);
      notificationService.createNewMessageNotification(message);
    });
    socketServiceImpl.sendMessageToClient("Message received");

  }

  @Override
  @Transactional(readOnly = true)
  public PagedResponse<MessageDto> getMessages(MessageMode messageMode, Predicate predicate,
      Pageable pageable, String userId, String timeZone) {
//    log.info(">> getMessages({}, {}, {}, {}, {})", messageMode, predicate, pageable, userId,
//        timeZone);

    BooleanExpression mandatoryPredicate =
        QMessage.message.fromUser.uuid.eq(userId).or(QMessage.message.toUser.uuid.eq(userId));
    if (MessageMode.INBOX.equals(messageMode)) {
      mandatoryPredicate = QMessage.message.toUser.uuid.eq(userId);
    } else if (MessageMode.OUTBOX.equals(messageMode)) {
      mandatoryPredicate = QMessage.message.fromUser.uuid.eq(userId);
    }

    if (Objects.nonNull(predicate)) {
      predicate = mandatoryPredicate.and(predicate);
    } else {
      predicate = mandatoryPredicate;
    }

    Page<Message> messagesPage = messageRepository.findAll(predicate, pageable);
    return PagedResponse.<MessageDto>builder().list(messagesPage.stream().map(message -> {
      MessageDto dto = messageMapper.mapToDto(message, timeZone);
      dto.setAudit(auditMapper.mapToDto(message, timeZone));
      if(dto.getJob() != null)
    	  dto.setJob(jobMapper.mapToDto(message.getJob(), timeZone));
//      dto.getJob().setAudit(auditMapper.mapToDto(message.getJob(), timeZone));
      return dto;
    }).collect(Collectors.toList())).page(messagesPage.getNumber()).size(messagesPage.getSize())
        .totalElements(messagesPage.getTotalElements()).build();
  }

  @Override
  @Transactional(readOnly = true)
  public MessageDto getMessage(String messageId, String userId, String timeZone) {
//    log.info(">> getMessage({}, {}, {})", messageId, userId, timeZone);
    Message message = messageRepository.findByUuidAndUserUuid(messageId, userId)
        .orElseThrow(() -> new ServiceException(ErrorCode.MESSAGE_NOT_FOUND, messageId));
    MessageDto dto = messageMapper.mapToDto(message, timeZone);
    dto.setAudit(auditMapper.mapToDto(message, timeZone));
    return dto;
  }

  @Override
  @Transactional
  public void updateMessageStatus(String messageId, MessageStatusRequest messageStatusRequest,
      String toUserId) {
//    log.info(">> updateMessageStatus({}, {}, {})", messageId, messageStatusRequest, toUserId);
    Message message = messageRepository.findByUuidAndToUserUuid(messageId, toUserId)
        .orElseThrow(() -> new ServiceException(ErrorCode.MESSAGE_NOT_FOUND, messageId));
    message.setStatus(messageStatusRequest.getStatus());
    socketServiceImpl.sendMessageToClient("Message received");
  }

  @Override
  public void deleteMessage(String messageId, Optional<String> fromUserId) {
//    log.info(">> deleteMessage({}, {})", messageId, fromUserId);
    Message message = null;
    if (fromUserId.isPresent()) {
      message = messageRepository.findByUuidAndFromUserUuid(messageId, fromUserId.get())
          .orElseThrow(() -> new ServiceException(ErrorCode.MESSAGE_NOT_FOUND, messageId));
    } else {
      message = messageRepository.findByUuid(messageId)
          .orElseThrow(() -> new ServiceException(ErrorCode.MESSAGE_NOT_FOUND, messageId));
    }
    messageRepository.delete(message);
  }

  @Override
  @Transactional
  public void contactEmail(String fromUserId, ContactUsRequest contactUsRequest) {
	  
	  User fromUser = userRepository.findByUuid(fromUserId)
		        .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, fromUserId));
	  
	  ContactUs contact = contactUsMapper.mapToEntity(contactUsRequest);
	  contact.setFromUser(fromUser);
	  
	  contactUsRepository.save(contact);
      notificationService.createContactUsEmailNotification(contact);
	  
  }

}
