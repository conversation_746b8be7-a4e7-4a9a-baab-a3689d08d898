package com.ma.spoton.api.services;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.dtos.OverTimeDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.OverTimeMapper;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.OverTimeRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.querydsl.core.types.Predicate;

@Service
public class OverTimeServiceImpl implements OverTimeService{

	
	@Autowired
	private ClientRepository clientRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private OverTimeRepository overTimeRepository;
	
	@Autowired
	private OverTimeMapper overTimeMapper;
	
	@Autowired
	private JobRepository jobRepository;
	
	@Override
	@Transactional
	public void addOvertimeForUser(String userId) {
			
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);		
		int overTimeHours;
		Float remainingTimeInMinutes;
		
		List<OverTime> alreadyExistingOverTimes = overTimeRepository.findByUser(userId);
		if(!alreadyExistingOverTimes.isEmpty())
		{
			throw new ServiceException(ErrorCode.OVERTIME_ERROR, userId);
		}
		
		User user = userRepository.findByUserUuid(userId);
		user.setIsOnOverTime(true);
		if(user.getIdleSince() == null)
		{
			List<Job> jobs = jobRepository.findAllLatestInTransitJobsByUserId(userId);
			if(jobs.isEmpty())
			{
				user.setIdleSince(ZonedDateTime.now());
				user.setIsIdleClear(false);
			}	
		}
		List<Client> userClients = clientRepository.findAllByUserId(user.getId());
		
		Float clientOverTime = userClients.stream().findFirst().get().getOverTime();
		if(clientOverTime !=  null)
		{
			if(clientOverTime > 0)
			{
				 overTimeHours = clientOverTime.intValue();
				 remainingTimeInMinutes = (clientOverTime - overTimeHours) * 60;	
			}
			else
			{
				throw new ServiceException(ErrorCode.ADD_CLIENT_OVERTIME, userId);
			}
		}
		else
		{
			throw new ServiceException(ErrorCode.ADD_CLIENT_OVERTIME, userId);
		}
		
		
		OverTime overTime = new OverTime();
		String userTimeZone = user.getTimeZone();
    	ZonedDateTime now = ZonedDateTime.now(ZoneId.of(userTimeZone));
    	LocalDate date = now.toLocalDate();
    	LocalTime startTime = now.toLocalTime();
    	LocalTime endTime;
    	overTime.setDate(date);
    	overTime.setUser(user);
    	overTime.setStartingTime(startTime);
    	endTime = startTime.plusHours(overTimeHours);
    	endTime = endTime.plusMinutes(remainingTimeInMinutes.intValue());
    	if(endTime.isAfter(startTime))
    	{
    		overTime.setEndingTime(endTime);
    	}
    	else
    	{
    		overTime.setEndingTime(dayEndTime);
    		
    		OverTime remainingOverTime = new OverTime();
    		remainingOverTime.setDate(date.plusDays(1));
    		remainingOverTime.setUser(user);
    		remainingOverTime.setStartingTime(dayStartTime);
            remainingOverTime.setEndingTime(endTime);
            overTimeRepository.save(remainingOverTime);
    		
    	}	
    	overTimeRepository.save(overTime);	
	}

	@Override
	@Transactional
	public void deleteOverTimeForUser(String userId) {
		
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);	
				
		User user = userRepository.findByUserUuid(userId);
		
		ZonedDateTime now = ZonedDateTime.now(ZoneId.of(user.getTimeZone())); 
	    DayOfWeek day = now.getDayOfWeek(); 
	    LocalTime time = now.toLocalTime();
	    LocalDate date = now.toLocalDate();
		
		user.setIsOnOverTime(false);
		user.setIdleSince(null);
		user.setIsIdleClear(true);
		OverTime overTime = overTimeRepository.findByUserAndDate(userId,date);
		if(overTime.getEndingTime().equals(dayEndTime))
		{
			OverTime remainingNextDayOverTime = overTimeRepository.findByUserDateAndStartTime(userId, overTime.getDate().plusDays(1), dayStartTime);
			if(remainingNextDayOverTime != null)
			{
				overTimeRepository.delete(remainingNextDayOverTime);
			}			
		}
		overTimeRepository.delete(overTime);		
	}

	@Override
	@Transactional
	public PagedResponse<OverTimeDto> getOverTimeUsers(String timeZone,Predicate predicate, Pageable pageable) {
		// TODO Auto-generated method stub
		Set<OverTime> unWantedOvertimeUsers = new HashSet<>();
		
			ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timeZone));
			LocalTime time = now.toLocalTime();
			LocalDate date = now.toLocalDate();
//			BooleanExpression startingTimePredicate = QOverTime.overTime.startingTime.before(time);
//			BooleanExpression endingTimePredicate = QOverTime.overTime.endingTime.after(time);
//			BooleanExpression datePredicate = QOverTime.overTime.date.eq(date);
//			predicate = startingTimePredicate.and(endingTimePredicate).and(datePredicate).and(predicate);
			
			Page<OverTime> overTimePage = overTimeRepository.findAll(predicate, pageable);
			return PagedResponse.<OverTimeDto>builder()
			        .list(overTimePage.stream()
			            .map(overTime -> overTimeMapper.mapToDto(overTime))
			            .collect(Collectors.toList()))
			        .page(overTimePage.getNumber()).size(overTimePage.getSize())
			        .totalElements(overTimePage.getTotalElements()).build();
		
	
	}

	@Override
	@Transactional
	public void deleteExpiredOverTimeUsers() {
		
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);
				
		List<Client> clients = clientRepository.findAllActiveClients();
		for(Client client : clients)
		{
			String timeZone = client.getTimeZone();
			ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timeZone));
	    	LocalTime time = now.toLocalTime();
	    	LocalDate date = now.toLocalDate();
	    	
	    	List<OverTime> expiredOverTimeUsers = overTimeRepository.getExpiredOverTimeUsers(date, time, client.getUuid());
	    	for(OverTime overTime : expiredOverTimeUsers)
	    	{
	    		User user = userRepository.findByUserUuid(overTime.getUser().getUuid());	    		    			
	    		OverTime remainingOverTime = overTimeRepository.findByUserAndDate(user.getUuid(), date.plusDays(1));
	    		if(remainingOverTime == null)
	    		{
	    			user.setIsOnOverTime(false);
	    	    	user.setIdleSince(null);
	    	    	user.setIsIdleClear(true);		
	    		}  				    					
	    		
	    		userRepository.save(user);
	    	}
	    	
	    	List<OverTime> previousDayOverTimeUsers = overTimeRepository.findAllByClientAndDate(date.minusDays(1), client.getUuid());
	    	for(OverTime overTime : previousDayOverTimeUsers)
	    	{
	    		User user = userRepository.findByUserUuid(overTime.getUser().getUuid());
	    		OverTime remainingOverTime = overTimeRepository.findByUserAndDate(user.getUuid(), date);
	    		if(remainingOverTime == null)
	    		{
	    			user.setIsOnOverTime(false);
		    		user.setIdleSince(null);
		    		user.setIsIdleClear(true);
	    		}
	    		userRepository.save(user);
	    	}
	    	overTimeRepository.deleteAll(previousDayOverTimeUsers);
	    	overTimeRepository.deleteAll(expiredOverTimeUsers);			
		
		}	
	}
}
