package com.ma.spoton.api.services;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.jfree.util.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAvailabilityDto;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.entities.QUserAvailability;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.UserAvailabilityMapper;
import com.ma.spoton.api.repositories.OverTimeRepository;
import com.ma.spoton.api.repositories.UserAvailabilityRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.UserAvailabilityRequest;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserAvailabilityServiceImpl implements UserAvailabilityService {

	@Autowired
	private UserAvailabilityMapper userAvailabilityMapper;

	@Autowired
	private UserAvailabilityRepository userAvailabilityRepository;

	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private OverTimeRepository overTimeRepository;

	@Override
	@Transactional
	public UserAvailability addUserAvailability(UserAvailabilityRequest userAvailabilityRequest) {

		UserAvailability userAvailability = null;
		UserAvailability userAvailability1 = null;
		UserAvailability userAvailability2 = null;
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);
		String userAvailabilityIdA = null;
		String userAvailabiltiyIdB = null;
		String userAvailabilityId1 = null;
		String userAvailabilityId2 = null;
		String unwanteduserAvailabilityId = null;
		Boolean isPreviousDayRemainingJob = false;

		// Checking whether the UserAvailabilityRequest starts and ends on the same day
		if (userAvailabilityRequest.getStartingTime().isBefore(userAvailabilityRequest.getEndingTime())) {
						
			log.info("userAvaiability request ({},{})",userAvailabilityRequest.getStartingTime(),userAvailabilityRequest.getBreakStartingTime());
			// Fetching all UserAvailabilities of that particular user in the same day
			List<UserAvailability> userAvailabilities = userAvailabilityRepository
					.findAllByUserAndDay(userAvailabilityRequest.getUserId(), userAvailabilityRequest.getDayOfWeek());
			for (UserAvailability userAvailabilityA : userAvailabilities) {
				// Checking whether a UserAvailability exists in that particular day and it
				// should not be the remaining work of previous day
				if (userAvailabilityA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE)) {
					userAvailabilityIdA = userAvailabilityA.getUuid();
					break;
				}
			}
//			if (userAvailabilityIdA == null) {
//				// Checking whether a UserAvailability exists in that particular day and it
//				// should not be the remaining work of previous day{case 2}
//				for (UserAvailability userAvailabilityA : userAvailabilities) {
//					// Checking if userAvailabilityA starts on 00:00am
//					if (userAvailabilityA.getStartingTime().equals(dayStartTime)) {
//						// Getting the list of previous day user availabilities
//						List<UserAvailability> previousDayUserAvailabilities = userAvailabilityRepository
//								.findAllByUserAndDay(userAvailabilityRequest.getUserId(),
//										userAvailabilityRequest.getDayOfWeek().minus(1));
//						for (UserAvailability userAvailabilityB : previousDayUserAvailabilities) {
//							// Checking if userAvailabiltyA is the remaining work of userAvailabilityB done
//							// on next day
//							if (userAvailabilityB.getEndingTime().equals(dayEndTime)) {
//								isPreviousDayRemainingJob = true;
//							}
//
//						}
//						if (isPreviousDayRemainingJob == false) {
//							userAvailabilityIdA = userAvailabilityA.getUuid();
//							break;
//						}
//
//					}
//				}
//			}

			if (userAvailabilityIdA != null) {
				userAvailability = userAvailabilityRepository.findByAvailabilityId(userAvailabilityIdA);
			}

			// if there is no UserAvailability present
			if (userAvailability == null) {
				userAvailability = userAvailabilityMapper.mapToEntity(userAvailabilityRequest);
				User user = userRepository.findByUuid(userAvailabilityRequest.getUserId()).orElseThrow(
						() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityRequest.getUserId()));
				userAvailability.setUser(user);
				userAvailability.setIsActive(userAvailabilityRequest.getActive());
			} else {

				// Checking whether the user availability ending time is 23:59pm
				if (userAvailability.getEndingTime().equals(dayEndTime)) {
					// Fetching all the next day user availabilities
					List<UserAvailability> nextDayUserAvailabilities = userAvailabilityRepository.findAllByUserAndDay(
							userAvailabilityRequest.getUserId(), userAvailabilityRequest.getDayOfWeek().plus(1));
					for (UserAvailability userAvailabilityB : nextDayUserAvailabilities) {
						// Checking whether it is a continuation of the previous day availability
						if (userAvailabilityB.getStartingTime().equals(dayStartTime) && userAvailabilityB.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
							unwanteduserAvailabilityId = userAvailabilityB.getUuid();
						}
					}

					// Deleting unwanted UserAvailability
					if (unwanteduserAvailabilityId != null) {
						UserAvailability unWantedAvailability = userAvailabilityRepository
								.findByAvailabilityId(unwanteduserAvailabilityId);
						userAvailabilityRepository.delete(unWantedAvailability);

					}
				}

				userAvailability.setDayOfWeek(userAvailabilityRequest.getDayOfWeek());
				userAvailability.setStartingTime(userAvailabilityRequest.getStartingTime());
				userAvailability.setEndingTime(userAvailabilityRequest.getEndingTime());
				userAvailability.setIsActive(userAvailabilityRequest.getActive());
				if (userAvailabilityRequest.getBreakStartingTime() != null
						&& userAvailabilityRequest.getBreakEndingTime() != null) {
					userAvailability.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
					userAvailability.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
				}
				else if(userAvailabilityRequest.getBreakStartingTime() == null 
						&& userAvailabilityRequest.getBreakEndingTime() == null)
				{
					userAvailability.setBreakStartingTime(null);
					userAvailability.setBreakEndingTime(null);
				}

			}

			userAvailability.setIsPreviousDayRemainingWork(false);
			return userAvailabilityRepository.save(userAvailability);

		}

		// For user Availability Request which starts on a day and ends next day
		else {

			List<UserAvailability> userAvailabilities1 = userAvailabilityRepository
					.findAllByUserAndDay(userAvailabilityRequest.getUserId(), userAvailabilityRequest.getDayOfWeek());
			List<UserAvailability> userAvailabilities2 = userAvailabilityRepository.findAllByUserAndDay(
					userAvailabilityRequest.getUserId(), userAvailabilityRequest.getDayOfWeek().plus(1));

			if (userAvailabilities1.size() > 0) {
				for (UserAvailability userAvailabilityA : userAvailabilities1) {
					if (userAvailabilityA.getEndingTime().equals(dayEndTime) && userAvailabilityA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE)) {
						userAvailabilityId1 = userAvailabilityA.getUuid();
					}
				}
			}

			if (userAvailabilities2.size() > 0) {
				for (UserAvailability userAvailabilityB : userAvailabilities2) {
					if (userAvailabilityB.getStartingTime().equals(dayStartTime) && userAvailabilityB.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
						userAvailabilityId2 = userAvailabilityB.getUuid();
					}
				}
			}

			if (userAvailabilityId1 != null) {
				userAvailability1 = userAvailabilityRepository.findByAvailabilityId(userAvailabilityId1);
			}

			if (userAvailabilityId2 != null) {
				userAvailability2 = userAvailabilityRepository.findByAvailabilityId(userAvailabilityId2);
			}

			// If User Availabilities already in DB starts in one day and ends next day
			if (userAvailability1 != null && userAvailability2 != null) {

				userAvailability1.setStartingTime(userAvailabilityRequest.getStartingTime());
				userAvailability1.setIsActive(userAvailabilityRequest.getActive());

				userAvailability2.setEndingTime(userAvailabilityRequest.getEndingTime());
				userAvailability2.setIsActive(userAvailabilityRequest.getActive());
				
				
				if(userAvailabilityRequest.getBreakStartingTime() != null && userAvailabilityRequest.getBreakEndingTime() != null)
				{
					//Break time is within same day
					if(userAvailabilityRequest.getBreakStartingTime().isBefore(userAvailabilityRequest.getBreakEndingTime()))
					{
						//Break time is in second day
						if(userAvailabilityRequest.getBreakStartingTime().isBefore(noon) && userAvailabilityRequest.getBreakEndingTime().isBefore(noon))
						{
							userAvailability2.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
							userAvailability2.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
							
							userAvailability1.setBreakStartingTime(null);
							userAvailability1.setBreakEndingTime(null);
						}
						
						//Break time is in first day
						if(userAvailabilityRequest.getBreakStartingTime().isAfter(noon) && userAvailabilityRequest.getBreakEndingTime().isAfter(noon))
						{
							userAvailability1.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
							userAvailability1.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
							
							userAvailability2.setBreakStartingTime(null);
							userAvailability2.setBreakEndingTime(null);
						}			
					}
					//Break time is split b/w two days
					else
					{
						
						userAvailability1.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
						userAvailability1.setBreakEndingTime(dayEndTime);
						
						userAvailability2.setBreakStartingTime(dayStartTime);
						userAvailability2.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());			
					}
				}
				else if(userAvailabilityRequest.getBreakStartingTime() == null && userAvailabilityRequest.getBreakEndingTime() == null)
				{
					
					userAvailability1.setBreakStartingTime(null);
					userAvailability1.setBreakEndingTime(null);
					
					userAvailability2.setBreakStartingTime(null);
					userAvailability2.setBreakEndingTime(null);
				}
				
				

			}
			// If User Availability already in DB starts and ends on same day or useravailability not in database
			else {

				userAvailabilityId1 = null;
				if (userAvailabilities1.size() > 0) {
					for (UserAvailability userAvailabilityA : userAvailabilities1) {
						
						//user availability not continuation of previous day
						if(userAvailabilityA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE))
						{
							userAvailabilityId1 = userAvailabilityA.getUuid();
							break;
						}
						
					}
					
//					if(userAvailabilityId1 == null)
//					{
//						
//						for(UserAvailability userAvailabilityA : userAvailabilities1)
//						{
//							//user availability not continuation of previous day{case 2}
//							if(userAvailabilityA.getStartingTime().equals(dayStartTime))
//							{	
//								List<UserAvailability> previousDayUserAvailabilities = userAvailabilityRepository.findAllByUserAndDay(userAvailabilityA.getUser().getUuid(), userAvailabilityA.getDayOfWeek().minus(1));
//	                            for(UserAvailability userAvailabilityB : previousDayUserAvailabilities)
//	                            {
//	                            	if(userAvailabilityB.getEndingTime().equals(dayEndTime))
//	                            	{
//	                            		isPreviousDayRemainingJob = true;
//	                            		break;
//	                            	}	
//	                            }
//	                            if(isPreviousDayRemainingJob == false)
//	                            {
//	                            	userAvailabilityId1 = userAvailabilityA.getUuid();
//	                            }       
//							}
//						}						
//					}
				}
				if (userAvailabilityId1 != null) {
					userAvailability1 = userAvailabilityRepository.findByAvailabilityId(userAvailabilityId1);
				}
				// If User Availability already in DB starts and ends on same day
				if (userAvailability1 != null) {
					userAvailability1.setStartingTime(userAvailabilityRequest.getStartingTime());
					userAvailability1.setEndingTime(dayEndTime);
					userAvailability1.setIsActive(userAvailabilityRequest.getActive());
				}
				// If User Availability doesnot exist
				else if (userAvailability1 == null) {
					userAvailability1 = userAvailabilityMapper.mapToEntity(userAvailabilityRequest);
					User user = userRepository.findByUuid(userAvailabilityRequest.getUserId()).orElseThrow(
							() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityRequest.getUserId()));
					userAvailability1.setUser(user);
					userAvailability1.setEndingTime(dayEndTime);
					userAvailability1.setIsActive(userAvailabilityRequest.getActive());
				}

				userAvailability2 = userAvailabilityMapper.mapToEntity(userAvailabilityRequest);
				User user = userRepository.findByUuid(userAvailabilityRequest.getUserId()).orElseThrow(
						() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityRequest.getUserId()));
				userAvailability2.setUser(user);
				userAvailability2.setDayOfWeek(userAvailabilityRequest.getDayOfWeek().plus(1));
				userAvailability2.setStartingTime(dayStartTime);
				userAvailability2.setEndingTime(userAvailabilityRequest.getEndingTime());
				userAvailability2.setIsActive(userAvailabilityRequest.getActive());

				if (userAvailabilityRequest.getBreakStartingTime() != null
						&& userAvailabilityRequest.getBreakEndingTime() != null) {
					//Break time on same day
					if (userAvailabilityRequest.getBreakStartingTime()
							.isBefore(userAvailabilityRequest.getBreakEndingTime())) {
						//break time on day1
						if (userAvailabilityRequest.getBreakStartingTime().isAfter(noon)
								&& userAvailabilityRequest.getBreakEndingTime().isAfter(noon)) {
							userAvailability1.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
							userAvailability1.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
							
							userAvailability2.setBreakStartingTime(null);
							userAvailability2.setBreakEndingTime(null);
						}
						//break time on day2
						else if (userAvailabilityRequest.getBreakStartingTime().isBefore(noon)
								&& userAvailabilityRequest.getBreakEndingTime().isBefore(noon)) {
							userAvailability2.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
							userAvailability2.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
							
							userAvailability1.setBreakStartingTime(null);
							userAvailability1.setBreakEndingTime(null);
						}
					} 
					//break time b/w 2 days
					else if (userAvailabilityRequest.getBreakStartingTime()
							.isAfter(userAvailabilityRequest.getBreakEndingTime())) {
						userAvailability1.setBreakStartingTime(userAvailabilityRequest.getBreakStartingTime());
						userAvailability1.setBreakEndingTime(dayEndTime);

						userAvailability2.setBreakStartingTime(dayStartTime);
						userAvailability2.setBreakEndingTime(userAvailabilityRequest.getBreakEndingTime());
					}
				}
				else if(userAvailabilityRequest.getBreakStartingTime() == null
						&& userAvailabilityRequest.getBreakEndingTime() == null)
				{				
					userAvailability1.setBreakStartingTime(null);
					userAvailability1.setBreakEndingTime(null);
					
					userAvailability2.setBreakStartingTime(null);
					userAvailability2.setBreakEndingTime(null);										
				}
			}
					
			userAvailability2.setIsPreviousDayRemainingWork(Boolean.TRUE);
			userAvailability1.setIsPreviousDayRemainingWork(Boolean.FALSE);
			userAvailabilityRepository.save(userAvailability2);
			return userAvailabilityRepository.save(userAvailability1);

		}

	}

	@Override
	@Transactional
	public List<UserAvailabilityDto> getUserAvailability(String userId) {

		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);

		// For 3rd shift users
		List<UserAvailability> thirdShiftUserAvailabilities = new ArrayList<>();
		List<UserAvailability> mergedUserAvailabilities = new ArrayList<>();
		List<UserAvailability> unWantedUserAvailabilities = new ArrayList<>();
		

		List<UserAvailability> userAvailabilities = userAvailabilityRepository.findByUserId(userId);
		for (UserAvailability userAvailability : userAvailabilities) {
			if (userAvailability.getStartingTime().equals(dayStartTime) && userAvailability.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)
					|| userAvailability.getEndingTime().equals(dayEndTime)) {
				thirdShiftUserAvailabilities.add(userAvailability);

			}
		}
		userAvailabilities.removeAll(thirdShiftUserAvailabilities);

		for (UserAvailability userAvailability1 : thirdShiftUserAvailabilities) {
			if (userAvailability1.getStartingTime().equals(dayStartTime)) {
				DayOfWeek day = userAvailability1.getDayOfWeek();
				for (UserAvailability userAvailability2 : thirdShiftUserAvailabilities) {
					if (userAvailability2.getEndingTime().equals(dayEndTime)
							&& userAvailability2.getDayOfWeek().equals(day.minus(1))) {
						UserAvailability mergedUserAvailability = new UserAvailability();
						mergedUserAvailability.setStartingTime(userAvailability2.getStartingTime());
						mergedUserAvailability.setEndingTime(userAvailability1.getEndingTime());
						mergedUserAvailability.setDayOfWeek(userAvailability2.getDayOfWeek());
						mergedUserAvailability.setUser(userAvailability2.getUser());
						mergedUserAvailability.setIsActive(userAvailability2.getIsActive());
						mergedUserAvailability.setUuid(userAvailability2.getUuid());
						
						if(userAvailability1.getBreakStartingTime() != null && userAvailability1.getBreakEndingTime() != null
								&& userAvailability2.getBreakStartingTime() == null && userAvailability2.getBreakEndingTime() == null)
						{
							
							mergedUserAvailability.setBreakStartingTime(userAvailability1.getBreakStartingTime());
							mergedUserAvailability.setBreakEndingTime(userAvailability1.getBreakEndingTime());
							
						}
						else if(userAvailability1.getBreakStartingTime() == null && userAvailability1.getBreakEndingTime() == null
								&& userAvailability2.getBreakStartingTime() != null && userAvailability2.getBreakEndingTime() != null)

						{
							mergedUserAvailability.setBreakStartingTime(userAvailability2.getBreakStartingTime());
							mergedUserAvailability.setBreakEndingTime(userAvailability2.getBreakEndingTime());
						}
						else if(userAvailability1.getBreakStartingTime() != null && userAvailability1.getBreakEndingTime() != null
								&& userAvailability2.getBreakStartingTime() != null && userAvailability2.getBreakEndingTime() != null)
						{	
							mergedUserAvailability.setBreakStartingTime(userAvailability2.getBreakStartingTime());
							mergedUserAvailability.setBreakEndingTime(userAvailability1.getBreakEndingTime());	
						}
						log.info("mergedUserAvailability({})",mergedUserAvailability);
						mergedUserAvailabilities.add(mergedUserAvailability);
						unWantedUserAvailabilities.add(userAvailability1);
						unWantedUserAvailabilities.add(userAvailability2);
					}
				}
			}
		}
		log.info("mergedUserAvailabilities({})",mergedUserAvailabilities);
		thirdShiftUserAvailabilities.removeAll(unWantedUserAvailabilities);
		userAvailabilities.addAll(thirdShiftUserAvailabilities);
		userAvailabilities.addAll(mergedUserAvailabilities);

		return userAvailabilities.stream().map(userAvailability -> userAvailabilityMapper.mapToDto(userAvailability))
				.collect(Collectors.toList());
	}

	@Override
	@Transactional
	public void deleteUserAvailability(String userAvailabilityId) {

		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);

		UserAvailability userAvailability = userAvailabilityRepository.findByAvailabilityId(userAvailabilityId);
		ZonedDateTime now = ZonedDateTime.now(ZoneId.of(userAvailability.getUser().getTimeZone()));
		DayOfWeek day = now.getDayOfWeek();
		LocalTime time = now.toLocalTime();
		LocalDate date = now.toLocalDate();

		if (day.equals(userAvailability.getDayOfWeek()) && time.isAfter(userAvailability.getStartingTime())
				&& time.isBefore(userAvailability.getEndingTime())) {
			User user = userRepository.findByUserUuid(userAvailability.getUser().getUuid());
			List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
			if(allOverTime.isEmpty())
			{
				user.setIdleSince(null);
				user.setIsIdleClear(true);
				userRepository.save(user);
			}		
		}

		if (userAvailability.getEndingTime().equals(dayEndTime)) {
			List<UserAvailability> nextDayUserAvailabilities = userAvailabilityRepository
					.findAllByUserAndDay(userAvailability.getUser().getUuid(), userAvailability.getDayOfWeek().plus(1));
			for (UserAvailability userAvailability1 : nextDayUserAvailabilities) {
				if (userAvailability1.getStartingTime().equals(dayStartTime) && userAvailability1.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
					if (day.equals(userAvailability1.getDayOfWeek())
							&& time.isAfter(userAvailability1.getStartingTime())
							&& time.isBefore(userAvailability1.getEndingTime())) {
						User user = userRepository.findByUserUuid(userAvailability1.getUser().getUuid());
						List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
						if(allOverTime.isEmpty())
						{
							user.setIdleSince(null);
							user.setIsIdleClear(true);
							userRepository.save(user);
						}	
					}

					userAvailabilityRepository.delete(userAvailability1);
				}
			}
		}
		userAvailabilityRepository.delete(userAvailability);
	}

	@Override
	@Transactional
	public void activateDeactivateUserAvailability(String userAvailabiltyId, Boolean active) {

		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);

		UserAvailability userAvailability = userAvailabilityRepository.findByAvailabilityId(userAvailabiltyId);
		userAvailability.setIsActive(active);
		
		ZonedDateTime now = ZonedDateTime.now(ZoneId.of(userAvailability.getUser().getTimeZone()));
		DayOfWeek day = now.getDayOfWeek();
		LocalTime time = now.toLocalTime();
		LocalDate date = now.toLocalDate();
			
		if (active.equals(false)) {
			if (day.equals(userAvailability.getDayOfWeek()) && time.isAfter(userAvailability.getStartingTime())
					&& time.isBefore(userAvailability.getEndingTime())) {
				User user = userRepository.findByUserUuid(userAvailability.getUser().getUuid());
				List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
				if(allOverTime.isEmpty())
				{
					user.setIdleSince(null);
					user.setIsIdleClear(true);
					userRepository.save(user);
				}	
			}
		} else {
			if (day.equals(userAvailability.getDayOfWeek()) && time.isAfter(userAvailability.getStartingTime())
					&& time.isBefore(userAvailability.getEndingTime())) {
				User user = userRepository.findByUserUuid(userAvailability.getUser().getUuid());
				if (user.getIdleSince() == null) {
					user.setIdleSince(now);
					user.setIsIdleClear(false);
					userRepository.save(user);
				}
			}

		}
		userAvailabilityRepository.save(userAvailability);
		
		if (userAvailability.getEndingTime().equals(dayEndTime)) {
			List<UserAvailability> nextDayUserAvailabilities = userAvailabilityRepository
					.findAllByUserAndDay(userAvailability.getUser().getUuid(), userAvailability.getDayOfWeek().plus(1));
			for (UserAvailability userAvailability1 : nextDayUserAvailabilities) {
				if (userAvailability1.getStartingTime().equals(dayStartTime) && userAvailability1.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {

					userAvailability1.setIsActive(active);
					userAvailabilityRepository.save(userAvailability1);
					
					if (active.equals(false)) {
						if (day.equals(userAvailability1.getDayOfWeek()) && time.isAfter(userAvailability1.getStartingTime())
								&& time.isBefore(userAvailability1.getEndingTime())) {
							User user = userRepository.findByUserUuid(userAvailability1.getUser().getUuid());
							List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
							if(allOverTime.isEmpty())
							{
								user.setIdleSince(null);
								user.setIsIdleClear(true);
								userRepository.save(user);
							}	
						}
					} else {
						if (day.equals(userAvailability1.getDayOfWeek()) && time.isAfter(userAvailability1.getStartingTime())
								&& time.isBefore(userAvailability1.getEndingTime())) {
							User user = userRepository.findByUserUuid(userAvailability1.getUser().getUuid());
							if (user.getIdleSince() == null) {
								user.setIdleSince(now);
								user.setIsIdleClear(false);
								userRepository.save(user);
							}
						}
					}								
				}
			}
		}
	}
}
