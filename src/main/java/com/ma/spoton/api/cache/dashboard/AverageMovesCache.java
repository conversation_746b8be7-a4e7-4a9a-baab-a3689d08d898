package com.ma.spoton.api.cache.dashboard;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * Cache model for average moves
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AverageMovesCache extends BaseCache{

    private Map<String, Integer> avgMoves;

    public AverageMovesCache(String clientId,
                             String timeZone,
                             Map<String, Integer> avgMoves) {
        super(clientId, timeZone);
        this.avgMoves = avgMoves;
    }
}