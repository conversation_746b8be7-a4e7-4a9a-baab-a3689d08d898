package com.ma.spoton.api.cache.dashboard;

import com.ma.spoton.api.dtos.InTransitSpots;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Cache for in transit spots for a particular client.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InTransitSpotsCache extends BaseCache {
    private InTransitSpots spots;

    public InTransitSpotsCache(String clientId,
                          String timeZone,
                          InTransitSpots spots) {
        super(clientId, timeZone);
        this.spots = spots;
    }
}
