package com.ma.spoton.api.cache.dashboard;

import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections4.MapUtils;

import com.ma.spoton.api.dtos.SpotCountsDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Cache for in transit spots for a particular client.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpotCountsCache extends BaseCache {

    private ConcurrentHashMap<String, SpotCountsDTO> spotCountsMap;

    public SpotCountsCache(String clientId,
                           String timeZone,
                           String mapKey,
                           SpotCountsDTO spotCountsDTO) {
        super(clientId, timeZone);
        if (MapUtils.isNotEmpty(spotCountsMap)) {
            spotCountsMap.put(mapKey, spotCountsDTO);
        } else {
            spotCountsMap = new ConcurrentHashMap<>();
            spotCountsMap.put(mapKey, spotCountsDTO);
        }
    }
}
