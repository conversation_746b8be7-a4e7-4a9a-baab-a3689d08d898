package com.ma.spoton.api.cache.dashboard;

import com.ma.spoton.api.dtos.DayStats;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Cache model for hourly average moves
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HourlyAverageMovesCache extends BaseCache {

    private DayStats stats;

    public HourlyAverageMovesCache(String clientId,
                                   String timeZone,
                                   DayStats stats) {
        super(clientId, timeZone);
        this.setStats(stats);
    }
}