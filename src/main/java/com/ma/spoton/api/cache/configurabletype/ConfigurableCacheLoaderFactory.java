package com.ma.spoton.api.cache.configurabletype;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ma.spoton.api.entities.ConfigurableTypeEntity;

@Component
public class ConfigurableCacheLoaderFactory {

    private final Map<String, ConfigurableTypeCacheLoader<?>> cacheLoadersMap = new HashMap<>();

    public ConfigurableCacheLoaderFactory(List<ConfigurableTypeCacheLoader<?>> cacheLoaders) {
        cacheLoaders.forEach(cl -> cacheLoadersMap.put(cl.getRegistryType(), cl));
    }

    @SuppressWarnings("unchecked")
    public <T extends ConfigurableTypeEntity>
    ConfigurableTypeCacheLoader<T> getCacheLoader(String type) {
        return (ConfigurableTypeCacheLoader<T>) cacheLoadersMap.get(type);
    }
}
