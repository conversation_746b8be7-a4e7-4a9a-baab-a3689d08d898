package com.ma.spoton.api.cache.configurabletype;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.ma.spoton.api.entities.ConfigurableTypeEntity;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.ConfigurableTypeRepository;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

// TODO: Add new CacheLoader implementation for new configurableType

@Getter
@Slf4j
public abstract class ConfigurableTypeCacheLoader<T extends ConfigurableTypeEntity> {

    // load client specific values
    private final LoadingCache<String, Set<T>> clientCache = CacheBuilder.newBuilder()
            .build(new CacheLoader<>() {
                @Override
                public Set<T> load(@NotNull String clientId) {
                    try {
                        log.debug("Calling db to fetch the client specific values.");
                        return getRepository().findByClientUuid(clientId);
                    } catch (Exception e) {
                        log.error("Error while fetching fleet status from db");
                        return new HashSet<>();
                    }
                }
            });
    private final LoadingCache<String, T> globalCache = CacheBuilder.newBuilder()
            .build(new CacheLoader<>() {
                @Override
                public T load(@NotNull String status) throws Exception {
                    log.debug("Calling db to fetch the global values.");
                    return getRepository().findByValue(status)
                            .orElse(null);
                }
            });
    @Autowired
    private ClientRepository clientRepository;

    protected abstract String getRegistryType();

    protected abstract ConfigurableTypeRepository<T> getRepository();

    public abstract void loadGlobalData();

    public List<T> getValues(String clientId) {
        // first time when getValues is called with invalid cientId, don't store them in cache
        // in other methods, we dont need this check as the validity of clientId is checked in
        // respective callers of the method add/update/delete.
        if (isValidClientOrNoClientId(clientId)) {
            List<T> values = new ArrayList<>();
            try {
                values.addAll(clientCache.get(clientId));
                values.addAll(globalCache.asMap().values());
            } catch (ExecutionException e) { // TODO: wrap in serviceException
                throw new RuntimeException(e);
            }
            return values;
        } else {
            throw new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId);
        }
    }

    public void addToClientCache(String clientId, String value) {
        Optional<T> optionalT = getRepository().findByValue(value);
        if (optionalT.isPresent()) {
            boolean containsEntity = clientContainsEntity(clientId, optionalT.get());
            if (containsEntity) {
                try {
                    clientCache.get(clientId).add(optionalT.get());
                } catch (ExecutionException e) { // TODO: wrap in serviceException
                    throw new RuntimeException(e);
                }
            }
        }
    }

    protected abstract boolean clientContainsEntity(String clientId, T t);

    public void deleteData(String clientId, T data) {
        try {
            clientCache.get(clientId).remove(data);
        } catch (ExecutionException e) { // TODO: wrap in serviceException
            throw new RuntimeException(e);
        }
    }

    public void deleteAllData(String clientId) {
        clientCache.invalidate(clientId);
    }

    protected boolean isValidClientOrNoClientId(String clientId) {
        return StringUtils.isBlank(clientId) || clientRepository.findByUuid(clientId).isPresent();
    }
}
