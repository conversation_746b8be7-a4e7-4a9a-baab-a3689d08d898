package com.ma.spoton.api.cache.configurabletype;

import static com.ma.spoton.api.constants.ConfigurableType.ROLES;

import java.util.Set;
import java.util.concurrent.ExecutionException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.repositories.RoleRepository;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Component
@Slf4j
public class RolesCacheLoader extends ConfigurableTypeCacheLoader<Role> {

    @Autowired
    private RoleRepository repository;

    @Override
    protected String getRegistryType() {
        return ROLES.name();
    }

    @Override
    public void loadGlobalData() {
        ROLES.getInitialValues()
                .forEach(s -> {
                    try {
                        getGlobalCache().get(s);
                    } catch (ExecutionException e) { // TODO: wrap in serviceException
                        throw new RuntimeException(e);
                    }
                });
    }

    @Override
    protected boolean clientContainsEntity(String clientId, Role role) {
        // Fetch client mappings
        Set<Role> clientEntities = repository.findByClientUuid(clientId);

        // Check if a mapping exists for this type entity
        return clientEntities.stream()
                .anyMatch(c -> c.getUuid().equals(role.getUuid()));
    }
}
