package com.ma.spoton.api.cache.configurabletype;

import static com.ma.spoton.api.constants.ConfigurableType.FLEET_STATUS;
import static com.ma.spoton.api.constants.ConfigurableType.FleetStatusConstants.NIL;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.repositories.FleetStatusRepository;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter(AccessLevel.PROTECTED)
@Component
@Slf4j
public class FleetStatusCacheLoader extends ConfigurableTypeCacheLoader<FleetStatus> {

    @Autowired
    private FleetStatusRepository repository;

    @Override
    public void loadGlobalData() {
        FLEET_STATUS.getInitialValues()
                .forEach(s -> {
                    try {
                        getGlobalCache().get(s);
                    } catch (ExecutionException e) { // TODO: wrap in serviceException
                        throw new RuntimeException(e);
                    }
                });
    }

    @Override
    // Needs override to remove nil from status
    public List<FleetStatus> getValues(String clientId) {

        if (isValidClientOrNoClientId(clientId)) {
            List<FleetStatus> globalStatuses = getGlobalCache().asMap().values()
                    .stream()
                    .filter(status -> status != null && !NIL.equals(status.getValue()))
                    .collect(Collectors.toList());
            List<FleetStatus> clientStatuses = new ArrayList<>();
            if (StringUtils.isNotBlank(clientId)) {
                try {
                    clientStatuses.addAll(getClientCache().get(clientId));
                } catch (ExecutionException e) { // TODO: wrap in serviceException
                    throw new RuntimeException(e);
                }
            }
            List<FleetStatus> statuses = new ArrayList<>(globalStatuses);
            statuses.addAll(clientStatuses);

            return statuses;
        } else {
            throw new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId);
        }
    }

    @Override
    protected boolean clientContainsEntity(String clientId, FleetStatus fleetStatus) {
        // Fetch client mappings
        Set<FleetStatus> clientEntities = repository.findByClientUuid(clientId);

        // Check if a mapping exists for this type entity
        return clientEntities.stream()
                .anyMatch(c -> c.getUuid().equals(fleetStatus.getUuid()));
    }

    @Override
    protected String getRegistryType() {
        return FLEET_STATUS.name();
    }

    public FleetStatus getGlobalStatus(String status) {
        try {
            return getGlobalCache().get(status);
        } catch (ExecutionException e) { // TODO: wrap in serviceException
            throw new RuntimeException(e);
        }
    }

    public FleetStatus getFleetStatus(String clientId, String status) {
        return getValues(clientId).stream()
                .filter(st -> st.getValue().equals(status))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCode.FLEET_STATUS_NOT_FOUND, status));
    }
}
