package com.ma.spoton.api.cache.dashboard;

import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections4.MapUtils;

import com.ma.spoton.api.dtos.WeeklyTotalSpotsDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyTotalSpotsCache extends BaseCache {

    private ConcurrentHashMap<String, WeeklyTotalSpotsDTO> weeklyTotalSpotsMap;

    public WeeklyTotalSpotsCache(String clientId,
                                 String timeZone,
                                 String weekStartTime,
                                 WeeklyTotalSpotsDTO weeklyTotalSpotsDTO) {
        super(clientId, timeZone);
        if (MapUtils.isNotEmpty(weeklyTotalSpotsMap)) {
            weeklyTotalSpotsMap.put(weekStartTime, weeklyTotalSpotsDTO);
        } else {
            weeklyTotalSpotsMap = new ConcurrentHashMap<>();
            weeklyTotalSpotsMap.put(weekStartTime, weeklyTotalSpotsDTO);
        }
    }
}
