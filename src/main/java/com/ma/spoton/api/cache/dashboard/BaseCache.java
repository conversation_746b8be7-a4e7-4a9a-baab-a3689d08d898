package com.ma.spoton.api.cache.dashboard;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Base cache class for all the dashboard cacheable objects.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseCache {
    private String clientId;
    private String timeZone;

    public boolean isValid(String clientId, String timeZone) {
        return this.clientId.equals(clientId) &&  this.timeZone.equals(timeZone);
    }
}
