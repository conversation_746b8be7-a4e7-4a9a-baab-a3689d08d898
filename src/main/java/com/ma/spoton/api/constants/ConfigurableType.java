package com.ma.spoton.api.constants;

import static com.ma.spoton.api.constants.ConfigurableType.FleetStatusConstants.EMPTY;
import static com.ma.spoton.api.constants.ConfigurableType.FleetStatusConstants.FULL;
import static com.ma.spoton.api.constants.ConfigurableType.FleetStatusConstants.NIL;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.ADMIN;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.CLIENT;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.DRIVER;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.GUARD;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.IT;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.SPOTTER;
import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants.SUPERVISOR;

import java.util.Set;

import lombok.Getter;

@Getter
public enum ConfigurableType {

    FLEET_STATUS(Set.of(NIL, EMPTY, FULL)), // same as Trailer status
    ROLES(Set.of(ADMIN, CLIENT, DRIVER, GUARD, IT, SPOTTER, SUPERVISOR));

    private final Set<String> initialValues;

    ConfigurableType(Set<String> initialValues) {
        this.initialValues = initialValues;
    }

    // TODO : add new configurable type here.

    public static class FleetStatusConstants {

        public static final String NIL = "NIL";
        public static final String EMPTY = "EMPTY";
        public static final String FULL = "FULL";
    }

    public static class RolesConstants {

        public static final String ADMIN = "ADMIN";
        public static final String CLIENT = "CLIENT";
        public static final String DRIVER = "DRIVER";
        public static final String GUARD = "GUARD";
        public static final String IT = "IT";
        public static final String SPOTTER = "SPOTTER";
        public static final String SUPERVISOR = "SUPERVISOR";
        public static final String TRAFFIC = "TRAFFIC";
        public static final String SECURITY = "SECURITY";
        public static final String MATERIAL_HANDLER = "MATERIAL_HANDLER";
        public static final String TEAM_LEAD = "TEAM_LEAD";
        public static final String PLANNING = "PLANNING";
    }
}
