package com.ma.spoton.api.constants;

import static lombok.AccessLevel.PRIVATE;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = PRIVATE)
public class SystemRoles {

  public static final String IT = "ROLE_IT";

  public static final String ADMIN = "ROLE_ADMIN";

  public static final String CLIENT = "ROLE_CLIENT";

  public static final String SUPERVISOR = "ROLE_SUPERVISOR";

  public static final String DRIVER = "ROLE_DRIVER";

  public static final String GUARD = "ROLE_GUARD";

  public static final String SPOTTER = "ROLE_SPOTTER";
  
  public static final String TRAFFIC = "ROLE_TRAFFIC";
  
  public static final String MATERIAL_HANDLER = "ROLE_MATERIAL_HANDLER";
  
  public static final String TEAM_LEAD = "ROLE_TEAM_LEAD";
  
  public static final String SECURITY = "ROLE_SECURITY";
  
  public static final String PLANNING = "ROLE_PLANNING";

}
