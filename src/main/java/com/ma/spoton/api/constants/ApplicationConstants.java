package com.ma.spoton.api.constants;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;

@Component
@ConfigurationProperties(prefix = "application")
@Data
public class ApplicationConstants {

  private String corsUrl;
  private String corsUrlAlias;
  private String adminPortalBaseUrl;
  private String apiBaseUrl;
  private String context;
  private String version;
  private String clientId;
  private String clientSecret;
  private String[] scopes;
  private String[] grantTypes;
  private int accessTokenValidityInSeconds;
  private int refreshTokenValidityInSeconds;
  private String[] publicEndpoints;
  private int swaggerDocVersion;
  private String baseAdminPortalUrl;
  private String resetPasswordUrl;
  private Long resetPasswordLinkExpiredInHours;
  private String emailFromName;
  private String emailFromAddress;
  private String stripeSecretKey;
  private String stripePublishableKey;
  private String jwtSigningKey;
  private String jwtPublicKey;
  private String clientSecretValue;

}
