package com.ma.spoton.api;

import static java.util.stream.Collectors.toMap;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.ma.spoton.api.constants.ApplicationConstants;
import com.ma.spoton.api.constants.PropertyConstants;
import com.ma.spoton.api.dtos.PushMessageRequest;
import com.ma.spoton.api.entities.Property;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.repositories.PropertyRepository;
import com.ma.spoton.api.services.EmailService;
import com.ma.spoton.api.services.FCMPushNotificationServiceImpl;
import com.ma.spoton.api.services.PushNotificationService;
import com.ma.spoton.api.services.SendGridEmailServiceImpl;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication
@EnableScheduling
@EnableTransactionManagement
@EnableJpaAuditing(dateTimeProviderRef = "auditingDateTimeProvider",
    auditorAwareRef = "auditorProvider")
public class SpotOnApiApplication {

  @Autowired
  private PropertyRepository propertyRepository;

  @Autowired
  private ApplicationConstants appConstants;
  

  public static void main(String[] args) {
    SpringApplication.run(SpotOnApiApplication.class, args);
  }

  @Bean
  public DateTimeProvider auditingDateTimeProvider() {
    return () -> Optional.of(ZonedDateTime.now());
  }

  @Bean
  public AuditorAware<User> auditorProvider() {
    return new AuditorAwareImpl();
  }

  @Bean
  public EmailService emailService() {
    Map<String, Object> propertyMap = propertyRepository.findAllByIsActive(true).stream()
        .collect(toMap(Property::getKey, Property::getValue));
    return new SendGridEmailServiceImpl(
        propertyMap.getOrDefault(PropertyConstants.SENDGRID_API_KEY, "DUMMY_KEY").toString(),
        propertyMap.getOrDefault(PropertyConstants.FROM_EMAIL, appConstants.getEmailFromAddress())
            .toString(),
        propertyMap.getOrDefault(PropertyConstants.FROM_NAME, appConstants.getEmailFromName())
            .toString());
  }

  @Bean
  public PushNotificationService pushNotificationService() {
//	  log.info(">>>>>>>>>>>>>>>>>DUMMY_KEY({})");
	  
    Map<String, Object> propertyMap = propertyRepository.findAllByIsActive(true).stream()
        .collect(toMap(Property::getKey, Property::getValue));
    return new FCMPushNotificationServiceImpl(
        (String) propertyMap.getOrDefault(PropertyConstants.FCM_API_KEY, "AAAAmEY1JH8:APA91bH5ktPuuPHb6jP58eFGqpQJMHGV7f0DhJcQ2UCwDR3-OEB98ZJ1vuROy3WUra_IefsjWh5XencukpCRatBDrrEm_xMQonSpLH2wIGUINfRWCBCmHt9GkTFPvHNqpsztqmPoJcaE"));
  }
}
