package com.ma.spoton.api.entities;

import java.util.Set;

import javax.persistence.MappedSuperclass;

import lombok.Getter;
import lombok.Setter;

// TODO : add new Entity class corresponding to new configurable type.

@Getter
@Setter
@MappedSuperclass
public abstract class ConfigurableTypeEntity extends BaseEntity {

    private String value;

    private Boolean global = false;

    public abstract Set<Client> getClients();
}
