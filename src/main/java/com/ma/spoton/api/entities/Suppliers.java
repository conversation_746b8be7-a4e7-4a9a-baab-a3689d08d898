package com.ma.spoton.api.entities;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "suppliers", indexes = {@Index(name = "suppliers_idx1_supplier", columnList = "supplier", unique = true)})
@Getter
@Setter
@ToString(callSuper = true)
public class Suppliers extends BaseEntity {

	@Column(name = "supplier", length = 100, nullable = false)
	private String supplier;
	
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "suppliers__fleets", joinColumns = @JoinColumn(name = "suppliers_id"),
	    inverseJoinColumns = @JoinColumn(name = "fleet_id"))
	private Set<Fleet> fleets = new HashSet<>();
	
	@ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinTable(
	    name = "suppliers__clients",
	    joinColumns = @JoinColumn(name = "supplier_id"),   // singular: supplier_id
	    inverseJoinColumns = @JoinColumn(name = "client_id")
	)
	private Set<Client> clients = new HashSet<>();
	
}
