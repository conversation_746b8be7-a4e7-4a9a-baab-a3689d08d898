package com.ma.spoton.api.entities;

import java.time.ZonedDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "tbl_notifications")
@Getter
@Setter
@ToString(callSuper = true)
public class Notification extends BaseEntity {


  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "from_user")
  private User fromUser;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "to_user", nullable = false)
  private User toUser;

  @Column(nullable = false)
  @Enumerated(EnumType.STRING)
  private Channel channel;

  @Column
  private String messageTitle;

  @Column
  @Lob
  private String messageBody;

  @Column
  private String messageReferrenceId;

  @Column(nullable = false)
  @Enumerated(EnumType.STRING)
  private DeliveryStatus deliveryStatus;

  @Column
  private String failureReason;

  @Column
  private ZonedDateTime startTime;
  
  @Column
  private String toUserEmail;

  @Column
  private ZonedDateTime completedTime;

  public enum DeliveryStatus {
    PENDING, DELIVERED, RECEIVED, FAILED;
  }

  public enum Channel {
    SMS, EMAIL, PUSH;
  }
}
