package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "contactUs")
@Getter
@Setter
@ToString(callSuper = true)
public class ContactUs extends BaseEntity {
	
	@Lob
	@Column(nullable = false)
	private String subject;
	
	@Lob
	@Column(nullable = false)
	private String messageBody;
	
	@Column
	private String type;
	
	@ManyToOne
	@JoinColumn(name = "from_user_id")
	private User fromUser;
	
}
