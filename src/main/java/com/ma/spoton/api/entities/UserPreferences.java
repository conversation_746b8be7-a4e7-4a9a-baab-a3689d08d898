package com.ma.spoton.api.entities;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Entity
@Table(name = "user_preferences", uniqueConstraints = @UniqueConstraint(columnNames = {"user_id"}))
@Getter
@Setter
@ToString(callSuper = true, exclude = {"user"})
public class UserPreferences extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "widget_preferences", columnDefinition = "TEXT")
    private String widgetPreferences;

    @Column(name = "layout", columnDefinition = "TEXT")
    private String layout;

}
