package com.ma.spoton.api.entities;

import java.time.DayOfWeek;
import java.time.LocalTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_availabilities")
@Getter
@Setter
@ToString(callSuper = true, exclude = {"user"})
public class UserAvailability extends BaseEntity{

	@ManyToOne
	@JoinColumn(name = "user_id", nullable = false)
	private User user;
	
	@Enumerated(EnumType.STRING)
	@Column(name = "day_of_week", length = 30, nullable = false)
	private DayOfWeek dayOfWeek;
	
	@Column(name = "starting_time", length = 30, nullable = false)
	private LocalTime startingTime;
	
	@Column(name = "ending_time", length = 30, nullable = false)
	private LocalTime endingTime;
	
	@Column(name = "break_starting_time", length = 30)
	private LocalTime breakStartingTime;
	
	@Column(name = "break_ending_time", length = 30)
	private LocalTime breakEndingTime;
	
	@Column(name = "is_previous_day_remaining_work", nullable = false, columnDefinition = "boolean default false")
	private Boolean isPreviousDayRemainingWork;
	
}
