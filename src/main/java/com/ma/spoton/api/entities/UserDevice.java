package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_devices",
    uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "device_registration_id"}))
@Getter
@Setter
@ToString(callSuper = true, exclude = {"user"})
public class UserDevice extends BaseEntity {

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", nullable = false)
  private User user;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", length = 10, nullable = false)
  private DeviceType deviceType;

  @Column(name = "device_registration_id", length = 200)
  private String deviceRegistrationId;

  @Column(length = 100)
  private String deviceName;

  @Column(length = 100)
  private String deviceModel;

  public enum DeviceType {
    ANDROID, IPHONE, OTHER;
  }

  @Column(name = "device_uuid", length = 200)
  private String deviceUuid;

}
