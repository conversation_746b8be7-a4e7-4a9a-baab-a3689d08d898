package com.ma.spoton.api.entities;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "fleet_status")
@Getter
@Setter
public class FleetStatus extends ConfigurableTypeEntity {

    @ManyToMany(mappedBy = "fleetStatuses", cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    private Set<Client> clients = new HashSet<>();
}
