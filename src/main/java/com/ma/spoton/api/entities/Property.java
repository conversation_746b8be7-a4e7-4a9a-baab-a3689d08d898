package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Index;
import javax.persistence.Lob;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "tbl_properties",
    indexes = @Index(name = "tbl_properties_idx1__key", columnList = "_key", unique = true))
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
public class Property extends BaseEntity implements java.io.Serializable {

  /**
   *  
   */
  private static final long serialVersionUID = 1266479721627557888L;

  public Property(String key, String value) {
    this.key = key;
    this.value = value;
  }

  @Column(name = "_key", length = 100, nullable = false, unique = true)
  private String key;

  @Lob
  @Column(name = "value", nullable = false)
  private String value;

  @Enumerated(EnumType.STRING)
  @Column(name = "access_type")
  private AccessType accessType;

  @Enumerated(EnumType.STRING)
  @Column(name = "type")
  private PropertyType type;

  public enum AccessType {
    PUBLIC, PRIVATE;
  }

  public enum PropertyType {
    TEXT, LONGTEXT, BOOLEAN;
  }

}
