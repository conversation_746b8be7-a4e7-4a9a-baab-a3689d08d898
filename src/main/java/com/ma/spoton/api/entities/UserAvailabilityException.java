package com.ma.spoton.api.entities;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_availability_exceptions")
@Getter
@Setter
@ToString(callSuper = true, exclude = {"user"})
public class UserAvailabilityException extends BaseEntity{

	@ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "date", nullable = false)
    private LocalDate date;
	
    @Enumerated(EnumType.STRING)
    @Column(name = "type", length = 20, nullable = false)
    private Type type;
    
    private LocalTime startingTime;
    
    private LocalTime endingTime;
    
    @Column(name = "break_starting_time", length = 30)
	private LocalTime breakStartingTime;
	
	@Column(name = "break_ending_time", length = 30)
	private LocalTime breakEndingTime;
    
    public enum Type {
        DAY_OFF, WORKING_DAY
    }
    
    @Column(name = "is_previous_day_remaining_work", nullable = false, columnDefinition = "boolean default false")
	private Boolean isPreviousDayRemainingWork;
}
