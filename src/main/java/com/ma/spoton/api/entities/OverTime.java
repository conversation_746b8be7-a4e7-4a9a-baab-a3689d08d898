package com.ma.spoton.api.entities;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Entity
@Table(name = "over_time")
@Getter
@Setter
@ToString(callSuper = true, exclude = {"user"})
public class OverTime extends BaseEntity{

	
	@ManyToOne
	@JoinColumn(name = "user_id", nullable = false)
	private User user;
	 
	@Column(name = "date", nullable = false)
	private LocalDate date;
	
	@Column(name = "starting_time", length = 30, nullable = false)
	private LocalTime startingTime;
	
	@Column(name = "ending_time", length = 30, nullable = false)
	private LocalTime endingTime;
}
