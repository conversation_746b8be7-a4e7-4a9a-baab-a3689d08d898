package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "messages")
@Getter
@Setter
@ToString(callSuper = true)
public class Message extends BaseEntity {

  @ManyToOne
  @JoinColumn(name = "from_user_id", nullable = false)
  private User fromUser;

  @ManyToOne
  @JoinColumn(name = "to_user_id", nullable = false)
  private User toUser;

  @Lob
  @Column(nullable = false)
  private String messageBody;

  @ManyToOne
  @JoinColumn(name = "client_location_id")
  private Location clientLocation;

  @ManyToOne
  @JoinColumn(name = "fleet_id")
  private Fleet fleet;

  // pickup

  @ManyToOne
  @JoinColumn(name = "pickup_location_id")
  private Location pickupLocation;

  @ManyToOne
  @JoinColumn(name = "pickup_spot_id")
  private Spot pickupSpot;


  // drop

  @ManyToOne
  @JoinColumn(name = "drop_location_id")
  private Location dropLocation;

  @ManyToOne
  @JoinColumn(name = "drop_spot_id")
  private Spot dropSpot;


  @Column(length = 30, nullable = false)
  @Enumerated(EnumType.STRING)
  private Type type;

  @Enumerated(EnumType.STRING)
  @Column(length = 30, nullable = false)
  private Status status;
  
  @Column
  private String sequenceAsn;
  
  @ManyToOne
  @JoinColumn(name = "job_id")
  private Job job;

  public enum Type {
    TRAILER_TRUCK_MOVE, HOT_TRAILER, INFORMATION;
  }

  public enum Status {
    NEW, READ;
  }

}
