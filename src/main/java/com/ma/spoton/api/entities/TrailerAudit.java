package com.ma.spoton.api.entities;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "trailer_audit")
@Getter
@Setter
@ToString(callSuper = true)
public class TrailerAudit extends BaseEntity{

	
	private String area;
	
	private String slot;
	
	@ManyToOne
	@JoinColumn(name = "location_id")
	private Location location;
	
	@ManyToOne
	@JoinColumn(name = "fleet_id")
	private Fleet fleet;
	
	private String trailerStatus;
	
	@Lob
	private String notes;
	
	@ManyToOne
	@JoinColumn(name = "client_id")
	private Client client;
	
	@ManyToOne
	@JoinColumn(name = "spot_id")
	private Spot spot;
	
	
}
