package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "trailer_log")
@Getter
@Setter
@ToString(callSuper = true)
public class TrailerLog extends BaseEntity{
	
	@Column
	@Lob
	private String actions;
	
	@ManyToOne
	@JoinColumn(name = "fleet_id")
	private Fleet fleet;
	
	@ManyToOne
	@JoinColumn(name = "job_id")
	private Job job;
	
	public Job getJob() {
        return job;
    }
}
