package com.ma.spoton.api.entities;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "users",
	indexes = {@Index(name = "users_idx1_email", columnList = "email", unique = true)})
@Getter
@Setter
@ToString(callSuper = true, exclude = {"password", "roles", "tempPassword"})
public class User extends BaseEntity {

	@Column(length = 100, nullable = false)
	private String firstName;

	@Column(length = 100, nullable = false)
	private String lastName;

	@Column(length = 100, nullable = false)
	private String email;

	@Column(length = 15)
	private String phone;

	@Column(length = 200)
	private String password;

	@Column(columnDefinition = "varchar(30) default 'US/Eastern'")
	private String timeZone;

	@Column
	private String forgotPasswordToken;

	@Column
	private ZonedDateTime forgotPasswordLinkInitialTime;

	@Column
	private ZonedDateTime lastLoginTime;


	@Transient
	private String tempPassword;

	@ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinTable(name = "users__roles", joinColumns = @JoinColumn(name = "user_id"),
		inverseJoinColumns = @JoinColumn(name = "role_id"))
	private Set<Role> roles = new HashSet<>();

	@ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinTable(name = "users__clients", joinColumns = @JoinColumn(name = "user_id"),
		inverseJoinColumns = @JoinColumn(name = "client_id"))
	private Set<Client> clients = new HashSet<>();

	@OneToMany(mappedBy = "user")
	private Set<UserDevice> devices = new HashSet<>();

	@Column
	private ZonedDateTime lastActiveTime;

	@Column(name = "is_ad_user", nullable = false, columnDefinition = "boolean default false")
	private Boolean isADUser = false;
	
	private ZonedDateTime idleSince;
	
	private Boolean isOnOverTime;
	
	private Boolean isIdleClear;
	
	@ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinTable(name = "users__locations", joinColumns = @JoinColumn(name = "user_id"),
		inverseJoinColumns = @JoinColumn(name = "location_id"))
	private Set<Location> locations = new HashSet<>();

	public enum BucketUsers {
		bucketDrivers("Bucket System Driver"),
		bucketSpotters("Bucket System Spotter");

		private final String value;

		BucketUsers(String value) {
			this.value = value;
		}

		public String getValue() {
			return value;
		}
	}

}
