package com.ma.spoton.api.entities;

import java.time.ZonedDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Table(name = "job_route_steps")
@Entity
@Getter
@Setter
@ToString(callSuper = true)
public class JobRouteStep extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "job_id", nullable = false)
    private Job job;

    @ManyToOne
    @JoinColumn(name = "location_id", nullable = false)
    private Location location;

    @ManyToOne
    @JoinColumn(name = "spot_id", nullable = false)
    private Spot spot;

    @Enumerated(EnumType.STRING)
    private StepType stepType;

    @Column
    private Integer stepOrder;

    @Enumerated(EnumType.STRING)
    private StepStatus stepStatus;

    @Column
    private ZonedDateTime startTime;

    @Column
    private ZonedDateTime completionTime;

    @Column
    private String notes;

    public enum StepType {
        PICK_UP, DROP
    }

    public enum StepStatus {
        PENDING, IN_PROGRESS, COMPLETED;

        public int getPhaseOrder() {
            switch (this) {
                case COMPLETED:
                    return 0;
                case IN_PROGRESS:
                    return 1;
                case PENDING:
                    return 2;
                default:
                    throw new IllegalArgumentException("Unknown status: " + this);
            }
        }
    }
}
