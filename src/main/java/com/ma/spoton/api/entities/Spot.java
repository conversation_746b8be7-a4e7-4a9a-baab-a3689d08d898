package com.ma.spoton.api.entities;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "spots",
    indexes = {@Index(name = "spots_idx1_location_id_spot_name",
        columnList = "location_id, spot_name", unique = true)})
@Getter
@Setter
@ToString(callSuper = true)
public class Spot extends BaseEntity {

  @ManyToOne
  @JoinColumn(name = "location_id", nullable = false)
  private Location location;

  @Column(name = "spot_name", length = 100, nullable = false)
  private String spotName;

  @Column(length = 30, nullable = false)
  @Enumerated(EnumType.STRING)
  private SpotType type;

  @Enumerated(EnumType.STRING)
  @Column(length = 30, nullable = false)
  private Status status;

  @Column(nullable = false)
  private Double latitude;

  @Column(nullable = false)
  private Double longitude;

  @ManyToOne
  @JoinColumn(name = "fleet_id")
  private Fleet fleet;

  @Column
  @Lob
  private String remarks;
  
  private ZonedDateTime lastOccupiedTime;
  
  private ZonedDateTime lastEmptiedTime;
  
  private Boolean isOccupied;
  
  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  @JoinTable(name = "spots__pickUpJobs", joinColumns = @JoinColumn(name = "spot_id"),
      inverseJoinColumns = @JoinColumn(name = "job_id"))
  private Set<Job> pickUpJobs;
  
  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  @JoinTable(name = "spots__dropOffJobs", joinColumns = @JoinColumn(name = "spot_id"),
      inverseJoinColumns = @JoinColumn(name = "job_id"))
  private Set<Job> dropOffJobs;
  
  @Lob
  private String notes;

  public enum SpotType {
    DOCK, SPOT, CURB;
  }

  public enum Status {
    EMPTY, OCCUPIED, TO_BE_EMPTY, TO_BE_OCCUPIED;
  }
  
//  private String locationName;

}
