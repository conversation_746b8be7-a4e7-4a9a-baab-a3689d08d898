package com.ma.spoton.api.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "duplicate_trailers")
@Getter
@Setter
@ToString(callSuper = true)
public class DuplicateTrailers extends BaseEntity{

	  @Column(name = "unit_number", length = 30, nullable = false)
	  private String unitNumber;
	  
}
