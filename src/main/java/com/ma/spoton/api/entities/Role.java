package com.ma.spoton.api.entities;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.AttributeOverride;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "roles",
        uniqueConstraints = @UniqueConstraint(name = "roles_uk1", columnNames = "role_name"))
@Getter
@Setter
@ToString(callSuper = true, exclude = "users")
@AttributeOverride(name = "value", column = @Column(name = "role_name", length = 50, nullable = false))
public class Role extends ConfigurableTypeEntity {

    @ManyToMany(mappedBy = "roles")
    private Set<User> users;

    @ManyToMany(mappedBy = "roles", cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    private Set<Client> clients = new HashSet<>();

    // Expose roleName getter/setter for legacy code
    @Transient
    public String getRoleName() {
        return getValue(); // delegate to Base class
    }

    public void setRoleName(String roleName) {
        setValue(roleName);
    }
}
