//package com.ma.spoton.api.entities;
//
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.Index;
//import javax.persistence.Table;
//
//import lombok.Getter;
//import lombok.Setter;
//import lombok.ToString;
//
//@Entity
//@Table(name = "carriers",
//	indexes = {@Index(name = "carriers_idx1_carrier", columnList = "carrier", unique = true)})
//@Getter
//@Setter
//@ToString(callSuper = true)
//public class Carriers extends BaseEntity {
//
//	@Column(name = "carrier", length = 100, nullable = false)
//	private String carrier;
//	
//}
