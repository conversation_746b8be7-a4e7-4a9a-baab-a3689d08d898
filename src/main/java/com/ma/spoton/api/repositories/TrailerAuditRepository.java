package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.QTrailerAudit;
import com.ma.spoton.api.entities.TrailerAudit;

@Repository
public interface TrailerAuditRepository extends JpaRepository<TrailerAudit, Long>, QuerydslPredicateExecutor<TrailerAudit>{

	 @Query("select t from TrailerAudit t where t.fleet.id in :fleetIds order by t.createdDate desc")
	  List<TrailerAudit> findAllLatestByFleetIds(Set<Long> fleetIds);

}
