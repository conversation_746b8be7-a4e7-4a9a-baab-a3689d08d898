package com.ma.spoton.api.repositories;

import java.util.Set;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.QSpot;
import com.ma.spoton.api.entities.Spot.Status;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;

public class SpotRepositoryImpl implements SpotRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Long countByStatusAndPredicate(Status status, Predicate predicate) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        QSpot spot = QSpot.spot;

        return queryFactory.select(spot.count())
                .from(spot)
                .where(spot.status.eq(status).and(predicate))
                .fetchOne();
    }

    @Override
    public Long countByStatusAndFleetStatus(Status status, FleetStatus fleetStatus, Predicate predicate) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        QSpot spot = QSpot.spot;

        return queryFactory
                .select(spot.count())
                .from(spot)
                .where(spot.status.eq(status)
                               .and(spot.fleet.fleetStatus.eq(fleetStatus))
                               .and(predicate))
                .fetchOne();
    }

    @Override
    public Long countByStatusAndFleetStatusIn(Status status, Set<FleetStatus> clientFleetStatuses,
                                              Predicate predicate) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        QSpot spot = QSpot.spot;

        return queryFactory
                .select(spot.count())
                .from(spot)
                .where(spot.status.eq(status)
                               .and(spot.fleet.fleetStatus.in(clientFleetStatuses))
                               .and(predicate))
                .fetchOne();
    }

    @Override
    public Long countSpotsWithPickupJobs(Predicate predicate) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        QSpot spot = QSpot.spot;
        QJob job = QJob.job;

        return queryFactory
                .select(spot.countDistinct())
                .from(spot)
                .join(spot.pickUpJobs, job)
                .where(job.assignedAt.isNotNull()
                               .and(predicate))
                .fetchOne();
    }

    @Override
    public Long countSpotsWithDropOffJobs(Predicate predicate) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        QSpot spot = QSpot.spot;
        QJob job = QJob.job;

        return queryFactory
                .select(spot.countDistinct())
                .from(spot)
                .join(spot.dropOffJobs, job)
                .where(job.assignedAt.isNotNull()
                               .and(predicate))
                .fetchOne();
    }
}
