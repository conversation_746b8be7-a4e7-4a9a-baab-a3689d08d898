package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.QGuardEntryExit;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface GuardEntryExitRepository extends JpaRepository<GuardEntryExit, Long>,
    QuerydslPredicateExecutor<GuardEntryExit>, QuerydslBinderCustomizer<QGuardEntryExit> {

  Optional<GuardEntryExit> findByUuid(String clientId);
  
  @Query("select g from GuardEntryExit g where g.fleet.id in :fleetIds order by g.createdDate desc")
  List<GuardEntryExit> findAllLatestByFleetIds(Set<Long> fleetIds);


  @Override
  public default void customize(QuerydslBindings bindings, QGuardEntryExit root) {
    bindings.bind(String.class)
        .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    bindings.excluding(root.id, root.location.id, root.fleet.id, root.createdBy,
        root.lastModifiedBy);
  }
}
