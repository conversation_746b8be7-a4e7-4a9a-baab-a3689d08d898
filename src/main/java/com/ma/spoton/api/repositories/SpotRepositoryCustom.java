package com.ma.spoton.api.repositories;

import java.util.Set;

import com.ma.spoton.api.entities.FleetStatus;
import com.ma.spoton.api.entities.Spot.Status;
import com.querydsl.core.types.Predicate;

public interface SpotRepositoryCustom {

    Long countByStatusAndPredicate(Status status, Predicate predicate);

    Long countByStatusAndFleetStatus(Status status, FleetStatus fleetStatus, Predicate predicate);

    Long countByStatusAndFleetStatusIn(Status status, Set<FleetStatus> clientFleetStatuses, Predicate predicate);

    Long countSpotsWithPickupJobs(Predicate predicate);

    Long countSpotsWithDropOffJobs(Predicate predicate);
}
