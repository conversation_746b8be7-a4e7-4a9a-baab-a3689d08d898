package com.ma.spoton.api.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.DuplicateTrailers;

/**
 * DAO queries for dashboards
 */
@Repository
public interface DashboardRepository extends JpaRepository<DuplicateTrailers, Long> {

    /**
     * Daily average moves for the given number of weeks
     */
    @Query(value = "SELECT COUNT(*) DIV :numberOfWeeks AS avgMoves, " +
                   "DAYNAME(j.drop_date_time) AS weekDay " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "WHERE j.status = 'COMPLETED' AND j.drop_date_time IS NOT NULL AND j.is_box_truck = false " +
                   "AND COALESCE(j.drop_date_time, 0) >= DATE_SUB(CURDATE(), INTERVAL :numberOfWeeks * 7 DAY) " +
                   "AND c.uuid = :clientId " +
                   "group BY weekDay " +
                   "order by weekDay", nativeQuery = true)
    List<DashboardRepository.DailyAvgMoves> getDailyAverage(@Param("numberOfWeeks") int numberOfWeeks,
                                                            @Param("clientId") String clientId);

    @Query(value = "SELECT " +
                   "daily_counts.dropDay AS dropDay, " +
                   "daily_counts.dropHour AS dropHour, " +
                   "COALESCE(daily_counts.dailyJobCount, 0) AS dailyJobCount, " +
                   "dailyTotal.totalJobsPerDay AS totalJobsPerDay " +
                   "FROM ( " +
                   "    SELECT " +
                   "    DAYNAME(CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone)) AS dropDay, " +
                   "    DATE_FORMAT(CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone), '%l %p') AS dropHour, " +
                   "    DATE(CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone)) AS dropDate, " +
                   "    COUNT(*) AS dailyJobCount " +
                   "    FROM jobs j " +
                   "    JOIN users u ON j.assigned_to_user_id = u.id " +
                   "    JOIN users__clients uc ON u.id = uc.user_id " +
                   "    JOIN clients c ON uc.client_id = c.id " +
                   "    WHERE j.status = 'COMPLETED' AND j.drop_date_time IS NOT NULL AND j.is_box_truck = false " +
                   "    AND CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone) BETWEEN " +
                   "    DATE_SUB(STR_TO_DATE(:userDate, '%Y-%m-%d'), INTERVAL (:numberOfDays - 1) DAY) " +
                   "    AND DATE_ADD(STR_TO_DATE(:userDate, '%Y-%m-%d'), INTERVAL 1 DAY) " +
                   "    AND c.uuid = :clientId " +
                   "    GROUP BY dropDay, dropHour, dropDate " +
                   ") AS daily_counts " +
                   "JOIN ( " +
                   "    SELECT " +
                   "    DAYNAME(CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone)) AS dropDay, " +
                   "    COUNT(*) AS totalJobsPerDay " +
                   "    FROM jobs j " +
                   "    JOIN users u ON j.assigned_to_user_id = u.id " +
                   "    JOIN users__clients uc ON u.id = uc.user_id " +
                   "    JOIN clients c ON uc.client_id = c.id " +
                   "    WHERE j.status = 'COMPLETED' AND j.drop_date_time IS NOT NULL AND j.is_box_truck = false " +
                   "    AND CONVERT_TZ(j.drop_date_time, 'UTC', :userTimeZone) BETWEEN " +
                   "    DATE_SUB(STR_TO_DATE(:userDate, '%Y-%m-%d'), INTERVAL (:numberOfDays - 1) DAY) " +
                   "    AND DATE_ADD(STR_TO_DATE(:userDate, '%Y-%m-%d'), INTERVAL 1 DAY) " +
                   "    AND c.uuid = :clientId " +
                   "    GROUP BY dropDay " +
                   ") AS dailyTotal ON daily_counts.dropDay = dailyTotal.dropDay " +
                   "GROUP BY daily_counts.dropDay, daily_counts.dropHour, daily_counts.dailyJobCount, " +
                   " dailyTotal.totalJobsPerDay " +
                   "ORDER BY FIELD(daily_counts.dropDay, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', " +
                   "'Friday', 'Saturday', 'Sunday'), " +
                   "STR_TO_DATE(daily_counts.dropHour, '%l %p')",
            nativeQuery = true)
    List<HourlyAvgMoves> getDailyHourlyAvgMoves(
            @Param("clientId") String clientId,
            @Param("userDate") String userDate,
            @Param("numberOfDays") int numberOfDays,
            @Param("userTimeZone") String userTimeZone);

    // TODO: get session.time_zone instead of UTC if the DB is in different timezone
    @Query(value =
            "SELECT " +
            "DATE_FORMAT(" +
            "   CONVERT_TZ(j.pickup_date_time, 'SYSTEM', :userTimeZone), '%M %d, %Y %h:%i %p'" +
            ") AS spotPickupTime, j.priority, " +
            "CONCAT(l2.location_name, ' - ', COALESCE(s2.spot_name, '')) AS pickupLocation, " +
            "CONCAT(l1.location_name, ' - ', COALESCE(s1.spot_name, '')) AS dropLocation, " +
            "f.unit_number AS trailer, CONCAT(u.first_name, ' ' , u.last_name) AS assignedTo " +
            "FROM jobs j " +
            "JOIN locations l1 ON j.drop_location_id = l1.id " +
            "JOIN locations l2 ON j.pickup_location_id = l2.id " +
            "LEFT JOIN spots s1 ON j.drop_spot_id = s1.id " +
            "LEFT JOIN spots s2 ON j.pickup_spot_id = s2.id " +
            "JOIN users u ON j.assigned_to_user_id = u.id " +
            "JOIN users__clients uc ON u.id = uc.user_id " +
            "JOIN clients c ON uc.client_id = c.id " +
            "JOIN fleets f ON j.fleet_id = f.id " +
            "WHERE j.status = 'IN_TRANSIT' AND c.uuid = :clientId AND j.pickup_date_time IS NOT NULL " +
            "   AND j.is_box_truck = false " +
            "ORDER BY j.pickup_date_time DESC, " +
            "CASE " +
            "  WHEN j.priority = 'HIGH' THEN 1 " +
            "  WHEN j.priority = 'MEDIUM' THEN 2 " +
            "  ELSE 3 " +
            "END", nativeQuery = true)
    List<InTransitSpot> getInTransitSpots(
            @Param("clientId") String clientId,
            @Param("userTimeZone") String userTimeZone);

    @Query(value = "SELECT COUNT(*) AS total, " +
                   "DATE_FORMAT(CONVERT_TZ(:startUtc, 'UTC', :userTimeZone), '%m/%d') AS weekStart, " +
                   "DATE_FORMAT(CONVERT_TZ(:endUtc, 'UTC', :userTimeZone), '%m/%d') AS weekEnd " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "WHERE j.status = 'COMPLETED' AND j.drop_date_time IS NOT NULL " +
                   "    AND j.drop_date_time BETWEEN :startUtc AND :endUtc " +
                   "    AND c.uuid = :clientId AND j.is_box_truck = false", nativeQuery = true)
    WeeklyTotalSpots getWeeklyTotalSpots(
            @Param("clientId") String clientId,
            @Param("userTimeZone") String userTimeZone,
            @Param("startUtc") String startUtc,
            @Param("endUtc") String endUtc);

    @Query(value = "SELECT s.uuid AS spotId, s.spot_name AS spotName " +
                   "FROM spots s " +
                   "JOIN locations l on l.id = s.location_id " +
                   "WHERE s.is_active = 1 AND l.uuid = :locationId", nativeQuery = true)
    List<SpotIdName> getSpotsByLocation(@Param("locationId") String locationId);

    /**
     * Breakdown of the filter query :- CONVERT_TZ(UTC_TIMESTAMP(), 'UTC', :userTimeZone) - current datetime of user.
     * DATE(...) will extract date part. DATE_SUB(...) will subtract 7 days (by default). CONVERT_TZ(...) will convert
     * that date (which will be considered as 00:00:00) to UTC timestamp to check with drop_date_time BETWEEN this
     * datetime and utc_timestamp() (which is nothing but current timestamp)
     */
    @Query(value = "SELECT" +
                   "  COUNT(*) AS allSpotTotal," +
                   "  COALESCE(SUM(CASE " +
                   "                WHEN j.pickup_spot_id = s.id OR j.drop_spot_id = s.id THEN 1 " +
                   "                ELSE 0 " +
                   "            END), 0) AS particularSpotTotal, " +
                   "  (CASE " +
                   "    WHEN COUNT(*) = 0 THEN 0.00" +
                   "    ELSE ROUND(" +
                   "            COALESCE(SUM(CASE " +
                   "                            WHEN j.pickup_spot_id = s.id OR j.drop_spot_id = s.id THEN 1 " +
                   "                            ELSE 0 " +
                   "                        END), 0) / COUNT(*) * 100, 2" +
                   "        )" +
                   "  END) AS percentage " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "JOIN spots s ON s.uuid = :spotId " +
                   "WHERE j.status = 'COMPLETED'" +
                   "  AND c.uuid = :clientId AND j.is_box_truck = false " +
                   "  AND j.drop_date_time IS NOT NULL " +
                   "  AND j.drop_date_time BETWEEN " +
                   "    CONVERT_TZ(DATE_SUB(DATE(CONVERT_TZ(UTC_TIMESTAMP(), 'UTC', :userTimeZone))," +
                   "                INTERVAL COALESCE(:days, 7) DAY), :userTimeZone, 'UTC')" +
                   "    AND UTC_TIMESTAMP()", nativeQuery = true)
    SpotCounts getSpotCounts(
            @Param("clientId") String clientId,
            @Param("spotId") String spotId,
            @Param("userTimeZone") String userTimeZone,
            @Param("days") Integer daysBefore
    );

    interface DailyAvgMoves {

        int getAvgMoves();   // maps to avg_moves

        String getWeekDay(); // maps to week_day
    }

    interface HourlyAvgMoves {

        String getDropDay();          // drop_day

        String getDropHour();         // drop_hour

        int getDailyJobCount();       // avg_jobs_per_day_hour

        int getTotalJobsPerDay();     // totalJobsPerDay
    }

    interface InTransitSpot {

        String getSpotPickupTime();

        String getPriority();

        String getPickupLocation();

        String getDropLocation();

        String getTrailer();

        String getAssignedTo();
    }

    interface WeeklyTotalSpots {

        int getTotal();

        String getWeekStart();

        String getWeekEnd();
    }

    interface SpotIdName {

        String getSpotId();

        String getSpotName();
    }

    interface SpotCounts {

        int getAllSpotTotal();

        int getParticularSpotTotal();

        String getPercentage();
    }
}