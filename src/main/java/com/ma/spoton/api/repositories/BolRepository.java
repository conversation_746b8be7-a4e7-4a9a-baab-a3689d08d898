package com.ma.spoton.api.repositories;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.Bol;
import com.ma.spoton.api.entities.Message;

@Repository
public interface BolRepository extends JpaRepository<Bol, Long> {
	
	@Query("select b from Bol b where b.job.id = :jobId")
	  Optional<Bol> findByJobId(Long jobId);

}
