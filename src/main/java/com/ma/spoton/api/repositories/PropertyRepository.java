package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.Property;
import com.ma.spoton.api.entities.Property.AccessType;

@Repository
public interface PropertyRepository extends JpaRepository<Property, Long> {

  Optional<Property> findPropertyByUuid(String propertyId);

  List<Property> findAllByIsActive(boolean status);

  List<Property> findAllByIsActiveAndAccessType(boolean status, AccessType accessType);

  Optional<Property> findPropertyByKey(String key);
}
