package com.ma.spoton.api.repositories;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.UserAvailability;

@Repository
public interface UserAvailabilityRepository
        extends JpaRepository<UserAvailability, Long>, QuerydslPredicateExecutor<UserAvailability> {

    @Query("select ua from UserAvailability ua join ua.user u join u.roles r join u.clients c where ua.dayOfWeek = :today and ua.startingTime < :presentTime and ua.endingTime > :presentTime and r.value = :roleName and c.uuid = :clientId and u.isActive = '1' and ua.isActive = '1'")
    List<UserAvailability> findPresentUserAvailabilities(DayOfWeek today, LocalTime presentTime, String roleName,
                                                         String clientId);

    @Query("select ua from UserAvailability ua where ua.uuid = :userAvailabilityId")
    UserAvailability findByAvailabilityId(String userAvailabilityId);

    @Query("select ua from UserAvailability ua join ua.user u where ua.dayOfWeek = :day and u.uuid = :userId")
    List<UserAvailability> findAllByUserAndDay(String userId, DayOfWeek day);

    @Query("select ua from UserAvailability ua join ua.user u where ua.dayOfWeek = :day and u.uuid = :userId")
    UserAvailability findByUserAndDay(String userId, DayOfWeek day);

    @Query("select ua from UserAvailability ua join ua.user u where ua.dayOfWeek = :day and u.uuid = :userId and ua.isActive='1' and ua.startingTime < :presentTime and ua.endingTime > :presentTime order by ua.createdDate desc")
    List<UserAvailability> findActiveByUserAndDay(String userId, DayOfWeek day, LocalTime presentTime);

    @Query("select ua from UserAvailability ua join ua.user u where ua.dayOfWeek = :day and u.uuid = :userId and ua.isActive='1' order by ua.startingTime asc")
    List<UserAvailability> findAllActiveByUserAndDay(String userId, DayOfWeek day);

    @Query("select ua from UserAvailability ua join ua.user u where u.uuid = :userId")
    List<UserAvailability> findByUserId(String userId);

    @Query("select ua from UserAvailability ua join ua.user u where ua.dayOfWeek = :day and u.uuid = :userId and ua.isActive='1' and ua.startingTime = :startTime and ua.isPreviousDayRemainingWork ='1'")
    UserAvailability findActivePreviousDayRemainingAvailability(String userId, DayOfWeek day, LocalTime startTime);
}
