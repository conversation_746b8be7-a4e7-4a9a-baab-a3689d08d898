package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserDevice;

@Repository
public interface UserDeviceRepository extends JpaRepository<UserDevice, Long> {
	
  Optional<User> findByUuid(String userId);

  Optional<UserDevice> findByUserAndDeviceRegistrationId(User user, String deviceRegistrationId);

 @Query("SELECT u FROM UserDevice u WHERE u.deviceUuid = :deviceUuid")
 List<UserDevice> findDeviceByUuid(String deviceUuid);
 
 @Query("SELECT u FROM UserDevice u WHERE u.deviceUuid = :deviceUuid")
 UserDevice findOneDeviceByUuid(String deviceUuid);
 
 UserDevice findByDeviceRegistrationId(String deviceRegistrationId);
 
 @Query("select u from UserDevice u where u.user.id = :userId")
 List<UserDevice> findByUserId(Long userId);
  
}