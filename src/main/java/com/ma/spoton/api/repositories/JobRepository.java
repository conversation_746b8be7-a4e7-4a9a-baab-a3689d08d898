package com.ma.spoton.api.repositories;


import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.entities.Job.Priority;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.entities.QJob;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface JobRepository extends JpaRepository<Job, Long>, QuerydslPredicateExecutor<Job>,
                                       QuerydslBinderCustomizer<QJob> {

    Optional<Job> findByUuid(String spotId);

    @Query("select j from Job j where j.fleet.id in :fleetIds order by j.createdDate desc")
    List<Job> findAllLatestByFleetIds(Set<Long> fleetIds);

    @Query("select j from Job j where j.fleet.id in :fleetIds"
           + " and j.status in ('IN_TRANSIT', 'COMPLETED') order by j.createdDate desc")
    List<Job> findAllLatestCompletedOrInTransitByFleetIds(Set<Long> fleetIds);

    @Query("select j from Job j where j.pickupSpot.id in :spotIds "
           + "and j.status in ('IN_TRANSIT', 'COMPLETED') order by j.createdDate desc")
    List<Job> findAllLatestByPickupSpotIds(Set<Long> spotIds);

    @Query("select j from Job j where j.assignedTo.uuid = :userId order by j.createdDate desc")
    List<Job> findAllLatestInTransitJobsByUserId(String userId);

    @Query("select j from Job j where j.dropSpot.id in :spotIds "
           + "and j.status in ('COMPLETED') order by j.dropDateTime desc")
    List<Job> findAllLatestCompletedByDropSpotIds(Set<Long> spotIds);

    @Query("select j from Job j where j.dropSpot.id in :spotIds order by j.createdDate desc")
    List<Job> findAllLatestByDropSpotIds(Set<Long> spotIds);

    List<Job> findAllByFleetAndStatusIn(Fleet fleet, List<Status> statuses);

    @Query("select j from Job j where j.assignedTo.uuid = :userId and j.status in ('IN_TRANSIT', 'OPEN')")
    List<Job> findInCompleteJobsByUser(String userId);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE'and j.queuePosition = (SELECT MAX(j2.queuePosition) FROM Job j2 where j2.pickupSpot.location.client.uuid = :clientId and j2.bucket = :bucket)")
    Job findLastInQueueByClient(String clientId, Bucket bucket);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' and j.queuePosition = (SELECT MIN(j2.queuePosition) FROM Job j2 where j2.pickupSpot.location.client.uuid = :clientId and j2.bucket = :bucket)")
    Job findFirstInQueueByClient(String clientId, Bucket bucket);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' order by j.queuePosition asc")
    List<Job> findAllInQueueByClient(String clientId, Bucket bucket);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' and j.queuePosition = :queuePosition")
    Job findByClientBucketPosition(String clientId, Bucket bucket, Long queuePosition);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' and j.queuePosition >= :dropIndex and j.queuePosition < :dragIndex")
    List<Job> findJobsAfterQueuePosition(String clientId, Bucket bucket, Long dropIndex, Long dragIndex);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' and j.queuePosition <= :dropIndex and j.queuePosition > :dragIndex")
    List<Job> findJobsBeforeQueuePosition(String clientId, Bucket bucket, Long dropIndex, Long dragIndex);

    @Query("SELECT j FROM Job j WHERE j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket and j.status = 'QUEUE' and j.queuePosition > :queuePosition order by j.queuePosition asc")
    List<Job> findAllJobsAfterQueuePosition(String clientId, Bucket bucket, Long queuePosition);

    @Query("SELECT j FROM Job j WHERE j.bucket IN :buckets and j.status = 'OPEN' and j.assignedAt < :time")
    List<Job> findExpiredOpenBucketJobs(List<Bucket> buckets, ZonedDateTime time);

    @Query("SELECT j FROM Job j WHERE j.bucket IN :buckets and j.status = 'OPEN' and j.isBoxTruckOrVan = false order by j.assignedAt asc")
    List<Job> findAllOpenBucketJobs(List<Bucket> buckets);

    @Query("SELECT j FROM Job j WHERE j.bucket IN :buckets AND j.status = 'SCHEDULED' ORDER BY j.assignedAt ASC")
    List<Job> findAllScheduledBucketJobs(List<Bucket> buckets);

    @Query("SELECT MAX(j.queuePosition) FROM Job j where j.pickupSpot.location.client.uuid = :clientId and j.bucket = :bucket")
    Long findLastJobPositionInQueueByClient(String clientId, Bucket bucket);

    @Query("SELECT j FROM Job j WHERE j.status = 'OPEN' and j.priority IN :priorities and j.isBoxTruckOrVan = false order by createdDate desc")
    List<Job> findAllOpenPriorityJobs(List<Priority> priorities);

    @Query("SELECT j from Job j WHERE j.id IN :jobIds")
    Set<Job> findAllByJobIds(Set<Long> jobIds);

    @Override
    public default void customize(QuerydslBindings bindings, QJob root) {
        bindings.bind(String.class)
                .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
        bindings.excluding(root.id, root.pickupSpot.id, root.dropSpot.id, root.createdBy,
                           root.lastModifiedBy);
    }

    //  @Query("SELECT j FROM Job j WHERE j.pickupSpot.id IN :spotIds OR j.dropSpot.id IN :spotIds AND j.pickupDateTime >= :startDate OR j.dropDateTime >= :startDate")
    @Query("SELECT j FROM Job j WHERE (j.pickupSpot.id IN :spotIds OR j.dropSpot.id IN :spotIds) AND (j.pickupDateTime >= :startDate OR j.dropDateTime >= :startDate)")
    List<Job> findAllLatestBySpotIds(Set<Long> spotIds, @Param("startDate") ZonedDateTime startDate);

    @Query("SELECT j FROM Job j WHERE j.status = 'COMPLETED' AND j.isBoxTruckOrVan = false " +
           "AND j.dropDateTime BETWEEN :fromDate AND :toDate " +
           "ORDER BY j.dropDateTime DESC")
    Page<Job> findCompletedJobsByDateRange(ZonedDateTime fromDate, ZonedDateTime toDate, Pageable pageable);


    @Query(value = "SELECT COALESCE(SUM(TIMESTAMPDIFF(MINUTE, j.pickup_date_time, j.drop_date_time)), 0) " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__roles ur ON u.id = ur.user_id " +
                   "JOIN roles r ON ur.role_id = r.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "JOIN locations l ON j.pickup_location_id = l.id " +
                   "WHERE j.status = 'COMPLETED' AND j.is_box_truck = false " +
                   "AND r.role_name = :roleName " +
                   "AND j.pickup_date_time IS NOT NULL " +
                   "AND j.drop_date_time IS NOT NULL " +
                   "AND j.drop_date_time BETWEEN :fromDate AND :toDate " +
                   "AND c.uuid = :clientId",
            nativeQuery = true)
    Integer findOverallAvgMoveTime(@Param("fromDate") ZonedDateTime fromDate,
                                   @Param("toDate") ZonedDateTime toDate,
                                   @Param("roleName") String roleName,
                                   @Param("clientId") String clientId);

    @Query(value = "SELECT COUNT(*) " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__roles ur ON u.id = ur.user_id " +
                   "JOIN roles r ON ur.role_id = r.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "WHERE j.status = 'COMPLETED' AND j.is_box_truck = false " +
                   "AND j.drop_date_time BETWEEN :fromDate AND :toDate " +
                   "AND r.role_name = :roleName " +
                   "AND c.uuid = :clientId",
            nativeQuery = true)
    int findTotalMovesByRoleAndDate(@Param("fromDate") ZonedDateTime fromDate,
                                    @Param("toDate") ZonedDateTime toDate,
                                    @Param("roleName") String roleName,
                                    @Param("clientId") String clientId);

    @Query(value = "SELECT COALESCE(SUM(TIMESTAMPDIFF(MINUTE, j.created_date, j.drop_date_time)), 0) " +
                   "FROM jobs j " +
                   "JOIN users u ON j.assigned_to_user_id = u.id " +
                   "JOIN users__roles ur ON u.id = ur.user_id " +
                   "JOIN roles r ON ur.role_id = r.id " +
                   "JOIN users__clients uc ON u.id = uc.user_id " +
                   "JOIN clients c ON uc.client_id = c.id " +
                   "JOIN locations l ON j.pickup_location_id = l.id " +
                   "WHERE j.status = 'COMPLETED' AND j.is_box_truck = false " +
                   "AND r.role_name = :roleName " +
                   "AND j.created_date IS NOT NULL " +
                   "AND j.drop_date_time IS NOT NULL " +
                   "AND j.drop_date_time BETWEEN :fromDate AND :toDate " +
                   "AND c.uuid = :clientId",
            nativeQuery = true)
    Integer findOverallAvgMoveTurnTime(@Param("fromDate") ZonedDateTime fromDate,
                                       @Param("toDate") ZonedDateTime toDate,
                                       @Param("roleName") String roleName,
                                       @Param("clientId") String clientId);


    @Query("SELECT j1.fleet.id AS fleetId, j1.dropLocation.id AS locationId, " +
           "j1.dropDateTime AS dropTime, j2.pickupDateTime AS pickupTime " +
           "FROM Job j1 JOIN Job j2 ON j1.fleet = j2.fleet " +
           "JOIN j1.fleet.clients c " +
           "WHERE c.uuid = :clientId " +
           "AND j1.status = 'COMPLETED' AND j2.status = 'COMPLETED' " +
           "AND j1.isBoxTruckOrVan = false AND j2.isBoxTruckOrVan = false " +
           "AND j1.dropLocation = j2.pickupLocation " +
           "AND j1.dropDateTime >= :startTime AND j2.pickupDateTime <= :endTime " +
           "AND j2.pickupDateTime > j1.dropDateTime " +
           "ORDER BY j1.fleet.id, j1.dropDateTime")
    List<DwellTimeProjection> findDwellTimesForClient(
            @Param("clientId") String clientId,
            @Param("startTime") ZonedDateTime startTime,
            @Param("endTime") ZonedDateTime endTime);

    @Query("select j.createdDate as spotCreatedTime, s.lastOccupiedTime as spotOccupiedTime, s.location.id as locationId"
           + " from Job j join j.pickupSpot s on j.pickupSpot.id = s.id where j.pickupSpot.location.client.uuid = :clientId"
           + " AND s.lastOccupiedTime IS NOT NULL AND (j.pickupLocation.id = s.location.id)"
           + " AND (j.status = 'OPEN' or j.status = 'QUEUE') AND j.isBoxTruckOrVan = false")
    List<JobRepository.DockTurnAroundTimeProjection> findDockTurnAroundTimeForClient(
            @Param("clientId") String clientId);

    // Used only for migrating data
    @Modifying
    @Query(value = "UPDATE jobs SET fleet_status_id = :fleetStatusId WHERE fleet_status_id IS NULL " +
                   "AND fleet_status = :oldStatus", nativeQuery = true)
    int updateFleetStatus(@Param("fleetStatusId") Long fleetStatusId,
                          @Param("oldStatus") String oldStatus);

    @Modifying
    @Query("UPDATE Job j SET j.fleetStatus = NULL WHERE j.fleetStatus.id = :id")
    void updateFleetStatusToNull(@Param("id") Long id);


    interface DwellTimeProjection {

        Long getFleetId();

        Long getLocationId();

        ZonedDateTime getDropTime();

        ZonedDateTime getPickupTime();
    }

    interface DockTurnAroundTimeProjection {

        ZonedDateTime getSpotCreatedTime();

        ZonedDateTime getSpotOccupiedTime();

        Long getLocationId();

    }
}
