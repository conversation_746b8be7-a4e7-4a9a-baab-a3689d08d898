package com.ma.spoton.api.repositories;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import com.ma.spoton.api.entities.ConfigurableTypeEntity;

// TODO: add new Repository interface corresponding to new configurable type

// actually bean will be created with respective Entity classes.
@NoRepositoryBean
public interface ConfigurableTypeRepository<T extends ConfigurableTypeEntity> extends JpaRepository<T, Long> {

    Optional<T> findByValue(String value);

    Set<T> findByValueIn(Set<String> values);

    @Query("SELECT e FROM #{#entityName} e WHERE e.global = false AND e.clients IS EMPTY")
    Set<T> findAllOrphanNonGlobalEntities();

    @Query("SELECT DISTINCT e FROM #{#entityName} e JOIN e.clients c WHERE c.uuid = :clientUuid")
    Set<T> findByClientUuid(@Param("clientUuid") String clientUuid);
}
