package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.TrailerLog;

@Repository
public interface TrailerLogRepository extends JpaRepository<TrailerLog, Long>,
QuerydslPredicateExecutor<TrailerLog>{

	  @Query("select m from TrailerLog m where m.job.id = :jobId")
	  Optional<TrailerLog> findByJobId(Long jobId);
	  
	  @Query("select m from TrailerLog m where m.fleet.id = :fleetId")
	  List<TrailerLog> findByFleetId(Long fleetId);
	  
}
