package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.QMessage;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long>,
    QuerydslPredicateExecutor<Message>, QuerydslBinderCustomizer<QMessage> {

  Optional<Message> findByUuid(String messageId);

  @Query("select m from Message m where m.uuid = :messageId and m.isActive=true")
  Optional<Message> findActiveByUuid(String messageId);

  Optional<Message> findByUuidAndFromUserUuid(String messageId, String fromUserId);

  Optional<Message> findByUuidAndToUserUuid(String messageId, String toUserId);

  @Query("select m from Message m where m.uuid = :messageId "
      + "and (m.fromUser.uuid = :userId or m.toUser.uuid = :userId)")
  Optional<Message> findByUuidAndUserUuid(String messageId, String userId);
  
  @Query("select m from Message m where m.fleet.id in :fleetIds order by m.createdDate desc")
  List<Message> findAllLatestByFleetIds(Set<Long> fleetIds);
  
  @Query("select m from Message m where m.job.id = :jobId")
  Optional<Message> findByJobId(Long jobId);

  @Override
  public default void customize(QuerydslBindings bindings, QMessage root) {
    bindings.bind(String.class)
        .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    bindings.excluding(root.id, root.fromUser.id, root.toUser.id, root.pickupLocation.id,
        root.pickupSpot.id, root.dropLocation.id, root.dropSpot.id, root.createdBy,
        root.lastModifiedBy);
  }

}
