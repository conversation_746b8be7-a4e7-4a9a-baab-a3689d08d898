package com.ma.spoton.api.repositories;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.entities.UserAvailabilityException.Type;

public interface UserAvailabilityExceptionRepository extends JpaRepository<UserAvailabilityException, Long>
        , QuerydslPredicateExecutor<UserAvailabilityException> {


    @Query("select uae from UserAvailabilityException uae join uae.user u join u.roles r join u.clients c where uae.date = :date and uae.startingTime < :presentTime and uae.endingTime > :presentTime and uae.type= 'DAY_OFF' and r.value = :roleName and c.uuid = :clientId and u.isActive='1'")
    List<UserAvailabilityException> getOnLeaveUsers(LocalDate date, String roleName, LocalTime presentTime,
                                                    String clientId);

    @Query("select uae from UserAvailabilityException uae join uae.user u join u.roles r join u.clients c where uae.date = :date and uae.startingTime < :presentTime and uae.endingTime > :presentTime and uae.type= 'WORKING_DAY' and r.value = :roleName and c.uuid = :clientId and u.isActive='1'")
    List<UserAvailabilityException> getAdditionalWorkingUsers(LocalDate date, String roleName, LocalTime presentTime,
                                                              String clientId);

    @Query("select uae from UserAvailabilityException uae join uae.user u where u.uuid = :userId and uae.date = :date")
    List<UserAvailabilityException> getPresentDayAdditionalWorkingUsers(String userId, LocalDate date);

    @Query("select uae from UserAvailabilityException uae join uae.user u where u.uuid = :userId and uae.date = :date and uae.startingTime < :time and uae.endingTime > :time order by uae.createdDate desc")
    List<UserAvailabilityException> getPresentTimeAdditionalWorkingUsers(String userId, LocalDate date, LocalTime time);

    @Query("select uae from UserAvailabilityException uae join uae.user u where u.uuid = :userId and uae.date = :date order by uae.startingTime asc")
    List<UserAvailabilityException> getAllPresentDayAdditionalWorkingUsers(String userId, LocalDate date);

    @Query("select uae from UserAvailabilityException uae where uae.uuid = :userAvailabilityExceptionId")
    UserAvailabilityException findByAvailabilityExceptionId(String userAvailabilityExceptionId);

    @Query("select uae from UserAvailabilityException uae join uae.user u where uae.date = :date and uae.type = :type and u.uuid = :userId")
    List<UserAvailabilityException> findAllByUserDateAndType(String userId, LocalDate date, Type type);

    @Query("select uae from UserAvailabilityException uae join uae.user u where u.uuid = :userId")
    List<UserAvailabilityException> findByUserId(String userId);

    @Query("select uae from UserAvailabilityException uae join uae.user u where u.uuid = :userId and uae.date = :date and uae.startingTime = :startTime and uae.isPreviousDayRemainingWork = '1' and uae.type =:type")
    List<UserAvailabilityException> findPreviousDayRemainingUserAvailabilityException(String userId, LocalDate date,
                                                                                      LocalTime startTime, Type type);
}
