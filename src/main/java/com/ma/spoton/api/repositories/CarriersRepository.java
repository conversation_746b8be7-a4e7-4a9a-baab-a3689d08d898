//package com.ma.spoton.api.repositories;
//
//import java.util.Optional;
//
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.querydsl.QuerydslPredicateExecutor;
//import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
//import org.springframework.data.querydsl.binding.QuerydslBindings;
//import org.springframework.stereotype.Repository;
//import org.springframework.data.querydsl.QuerydslPredicateExecutor;
//import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
//import org.springframework.data.querydsl.binding.QuerydslBindings;
//import org.springframework.data.querydsl.binding.SingleValueBinding;
//
//import com.ma.spoton.api.entities.Bol;
//import com.ma.spoton.api.entities.Carriers;
//import com.ma.spoton.api.entities.Client;
//import com.ma.spoton.api.entities.Fleet;
//import com.ma.spoton.api.entities.QCarriers;
//import com.ma.spoton.api.entities.QFleet;
//
//import com.querydsl.core.types.dsl.StringExpression;
//import com.querydsl.core.types.dsl.StringPath;
//
//@Repository
//public interface CarriersRepository extends JpaRepository<Carriers, Long>,
//		QuerydslPredicateExecutor<Carriers>, QuerydslBinderCustomizer<QCarriers> {
//
//	 Optional<Carriers> findByUuid(String carrierId);
//	 
//	 @Override
//		public default void customize(QuerydslBindings bindings, QCarriers root) {
//			bindings.bind(String.class)
//				.first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
//			bindings.excluding(root.id, root.createdBy, root.lastModifiedBy);
//		}
//
//	 
//}
