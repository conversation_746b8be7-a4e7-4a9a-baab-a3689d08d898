package com.ma.spoton.api.repositories;

import com.ma.spoton.api.entities.UserPreferences;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Optional;

@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Long> {

    Optional<UserPreferences> findByUserId(Long userId);

    @Modifying
    @Transactional
    @Query("DELETE FROM UserPreferences up WHERE up.user.id = :userId")
    void deleteByUserId(@Param("userId") Long userId);

}

