package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.QClient;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface ClientRepository extends JpaRepository<Client, Long>,
    QuerydslPredicateExecutor<Client>, QuerydslBinderCustomizer<QClient> {

  Optional<Client> findByUuid(String clientId);
  
  @Query("select c from Client c where c.uuid = :clientId")
  Client findUsingUuid(String clientId);

  @Query("select c from Client c where c.uuid = :clientId and c.isActive = true")
  Optional<Client> findActiveByUuid(String clientId);

  Set<Client> findAllByUuidIn(List<String> clientIds);
  
  @Query("select c from Client c where c.isActive = true and c.accountDeactivation = true")
  List<Client> findActiveByAccountDeactivation();
  
  @Query("SELECT c FROM Client c JOIN c.fleets f WHERE f.id IN :fleetIds")
  List<Client> findAllByFleetIds(@Param("fleetIds") Set<Long> fleetIds);
  
  @Query("SELECT c FROM Client c JOIN c.users u WHERE u.id IN :userId")
  List<Client> findAllByUserId(@Param("userId") Long userId);
  
  @Query("select c from Client c where c.isActive = true")
  List<Client> findAllActiveClients();
  
  @Query("select c from Client c where c.uuid IN :clientIds")
  List<Client> findAllClientsByClientId(List<String> clientIds);

  @Override
  public default void customize(QuerydslBindings bindings, QClient root) {
    bindings.bind(String.class)
        .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    bindings.excluding(root.id, root.createdBy, root.lastModifiedBy);
  }

}
