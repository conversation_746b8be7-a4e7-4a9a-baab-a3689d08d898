package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.Notification;


@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

  Optional<Notification> findByUuid(String notificationId);

  List<Notification> findAllByDeliveryStatus(Notification.DeliveryStatus deliveryStatus);
}
