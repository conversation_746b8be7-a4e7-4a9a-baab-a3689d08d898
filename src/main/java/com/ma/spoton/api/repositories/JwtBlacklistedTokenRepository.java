package com.ma.spoton.api.repositories;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.JwtBlacklistedToken;

@Repository
public interface JwtBlacklistedTokenRepository extends JpaRepository<JwtBlacklistedToken, Long> {

  Optional<JwtBlacklistedToken> findByAccessToken(String accessToken);

}
