package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.QLocation;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface LocationRepository extends JpaRepository<Location, Long>,
                                            QuerydslPredicateExecutor<Location>, QuerydslBinderCustomizer<QLocation> {

    Optional<Location> findByUuid(String locationId);

    Optional<Location> findByClientAndUuid(Client client, String locationId);

    @Query("select l from Location l where l.uuid = :locationId and l.isActive=true")
    Optional<Location> findActiveByUuid(String locationId);

    @Query("select l from Location l where l.client = :client and l.isDefault=true")
    Optional<Location> findDefaultByClient(Client client);

    @Query("select l from Location l where l.locationName = :locationName and l.client =:client and l.isActive=true")
    Location findByLocationName(String locationName, Client client);

    @Query("SELECT l FROM Location l JOIN l.client c WHERE c.uuid = :clientId")
    List<Location> findActiveLocationsByClientId(@Param("clientId") String clientId);

    Set<Location> findAllByUuidIn(List<String> locationIds);

    // using native query to maintain the order.
    @Query(value = "SELECT l FROM Location l WHERE l.uuid IN :locationIds AND l.isActive=true")
    List<Location> findActiveLocationsByIdsOrdered(@Param("locationIds") Set<String> locationIds);

    List<Location> findAllByLocationNameIn(List<String> locationNames);

    @Override
    public default void customize(QuerydslBindings bindings, QLocation root) {
        bindings.bind(String.class)
                .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
        bindings.excluding(root.id, root.client.id, root.createdBy, root.lastModifiedBy);
    }
}
