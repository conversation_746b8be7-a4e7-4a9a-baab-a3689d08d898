package com.ma.spoton.api.repositories;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.QSpot;
import com.ma.spoton.api.entities.Spot;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface SpotRepository extends JpaRepository<Spot, Long>, QuerydslPredicateExecutor<Spot>,
                                        QuerydslBinderCustomizer<QSpot>, SpotRepositoryCustom {

    Optional<Spot> findByUuid(String spotId);

    @Query("select s from Spot s where s.location.client = :client and s.uuid = :spotId")
    Optional<Spot> findByClientAndUuid(Client client, String spotId);

    @Query("select s from Spot s where s.uuid = :spotId and s.isActive=true")
    Optional<Spot> findActiveByUuid(String spotId);

    List<Spot> findAllByFleet(Fleet fleet);

    @Query(value = "SELECT s FROM Spot s WHERE s.uuid IN :spotIds AND s.isActive=true")
    List<Spot> findActiveSpotsByIdsOrdered(@Param("spotIds") Set<String> spotIds);

    @Query("select s from Spot s where s.fleet.id in :fleetIds order by s.createdDate desc")
    List<Spot> findAllLatestByFleetIds(Set<Long> fleetIds);

    @Query("SELECT s FROM Spot s WHERE s.location.client.uuid = :clientId AND s.lastEmptiedTime IS NOT NULL AND s.lastEmptiedTime BETWEEN :startTime AND :endTime")
    List<Spot> findSpotsByClientIdAndLastEmptiedTimeBetween(
            @Param("clientId") String clientId,
            @Param("startTime") ZonedDateTime startTime,
            @Param("endTime") ZonedDateTime endTime);

    @Query("SELECT s FROM Spot s WHERE s.location.client.uuid = :clientId AND s.lastOccupiedTime IS NOT NULL AND s.lastOccupiedTime BETWEEN :startTime AND :endTime")
    List<Spot> findSpotsByClientIdAndLastOccupiedTimeBetween(
            @Param("clientId") String clientId,
            @Param("startTime") ZonedDateTime startTime,
            @Param("endTime") ZonedDateTime endTime);

    @Query("SELECT COUNT(s) " +
           "FROM Spot s " +
           "LEFT JOIN s.fleet f " +
           "WHERE s.location.uuid = :locationId " +
           "AND s.location.client.uuid = :clientId " +
           "AND (" +
           "  (s.status = 'OCCUPIED' AND f.fleetStatus.value = 'EMPTY') " +
           "  OR (s.status = 'TO_BE_EMPTY' AND f.fleetStatus.value = 'EMPTY') " +
           "  OR (s.status = 'TO_BE_OCCUPIED' AND f.fleetStatus.value = 'EMPTY')" +
           "  OR (s.status = 'EMPTY' AND f.fleetStatus.value = 'EMPTY') " +
           " )")
    Long countEmptyStatus(@Param("clientId") String clientId,
                          @Param("locationId") String locationId);

    @Query("SELECT COUNT(s) " +
           "FROM Spot s " +
           "LEFT JOIN s.fleet f " +
           "WHERE s.location.uuid = :locationId " +
           "AND s.location.client.uuid = :clientId " +
           "AND (" +
           "  (s.status = 'OCCUPIED' AND f.fleetStatus.value = 'FULL') " +
           "  OR (s.status = 'TO_BE_OCCUPIED' AND f.fleetStatus.value = 'FULL')" +
           "  OR (s.status = 'TO_BE_EMPTY' AND f.fleetStatus.value = 'FULL')" +
           "  OR (s.status = 'EMPTY' AND f.fleetStatus.value = 'FULL') " +
           ")")
    Long countFullStatus(@Param("clientId") String clientId,
                         @Param("locationId") String locationId);

    @Modifying
    @Transactional
    @Query(value = "Delete from spots__drop_off_jobs where spot_id = :spotId and job_id = :jobId", nativeQuery = true)
    void deleteDropOffJobsInSpot(@Param("spotId") Long spotId, @Param("jobId") Long jobId);

    @Override
    public default void customize(QuerydslBindings bindings, QSpot root) {
        bindings.bind(String.class)
                .first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
        bindings.excluding(root.id, root.location.id, root.location.client.id, root.createdBy,
                           root.lastModifiedBy);
    }

    public interface LocationAverageProjection {

        Long getLocationId();

        Double getAvgLastEmptiedTime(); // in seconds since epoch
    }
}
