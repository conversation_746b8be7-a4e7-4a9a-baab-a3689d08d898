package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

//import com.ma.spoton.api.entities.Carriers;
//import com.ma.spoton.api.entities.QCarriers;
import com.ma.spoton.api.entities.QSuppliers;
import com.ma.spoton.api.entities.Suppliers;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;

@Repository
public interface SuppliersRepository extends JpaRepository<Suppliers, Long>,
QuerydslPredicateExecutor<Suppliers>, QuerydslBinderCustomizer<QSuppliers>  {

	Optional<Suppliers> findByUuid(String supplierId);
	
	Set<Suppliers> findAllByUuidIn(List<String> supplierUuids);
	
	 @Override
	 public default void customize(QuerydslBindings bindings, QSuppliers root) {
			bindings.bind(String.class)
				.first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
			bindings.excluding(root.id, root.createdBy, root.lastModifiedBy);
	}
	
}
