package com.ma.spoton.api.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.MobileAppVersion;
import com.ma.spoton.api.entities.QMobileAppVersion;

@Repository
public interface MobileAppVersionRepository extends JpaRepository<MobileAppVersion, Long>{

	 
	  @Query("select m from MobileAppVersion m where m.os = :OS")
	  MobileAppVersion findByOS(String OS);;
	
}
