package com.ma.spoton.api.utils;

import static com.ma.spoton.api.constants.ConfigurableType.RolesConstants;
import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import com.ma.spoton.api.dtos.UserAuthDto;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public class RoleUtils {

    public static String getRole(UserAuthDto user) {
        String role = null;
        String loggedinUserRole = user.getAuthorities().get(0);
        switch (loggedinUserRole) {
            case IT:
                role = RolesConstants.IT;
                break;
            case ADMIN:
                role = RolesConstants.ADMIN;
                break;
            case DRIVER:
                role = RolesConstants.DRIVER;
                break;
            case SPOTTER:
                role = RolesConstants.SPOTTER;
                break;
            case SUPERVISOR:
                role = RolesConstants.SUPERVISOR;
                break;
            case GUARD:
                role = RolesConstants.GUARD;
                break;
            case CLIENT:
                role = RolesConstants.CLIENT;
                break;
        }
        return role;
    }

}
