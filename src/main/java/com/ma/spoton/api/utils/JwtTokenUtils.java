package com.ma.spoton.api.utils;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import java.time.Instant;

//This class is used for json tokens functionalities send through email verification while creating new user 


@Component
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Slf4j
public class JwtTokenUtils {

	
	
	  @Value("${application.jwt-secret}")
	  private String jwtSecret;
	  
	  @Value("${application.expiration-time}")
	  private Long expirationTime;
	  
	  
	  public String generateToken(String name, String uuid) {
	        Date now = new Date();
	        Date expiryDate = new Date(now.getTime() + expirationTime);
            // log.info("expiryDate>>{}",expiryDate);

	        return Jwts.builder()
	                .setSubject(name)
	                .claim("uuid", uuid)
	                .setIssuedAt(now)
	                .setExpiration(expiryDate)
	                .signWith(SignatureAlgorithm.HS512, jwtSecret)
	                .compact();
	       
	    }

}
