package com.ma.spoton.api.utils;

import static java.net.URLDecoder.decode;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.join;
import static org.apache.commons.lang3.StringUtils.splitByCharacterTypeCamelCase;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = PRIVATE)
public class Utils {

  public static String humanize(String str) {
    if (isBlank(str)) {
      return "";
    }
    String[] words = null;
    if (str.contains("-")) {
      words = str.split("-");
    } else if (str.contains("_")) {
      words = str.split("_");
    } else {
      words = splitByCharacterTypeCamelCase(str);
    }
    List<String> trimmedWords = asList(words).stream().filter(StringUtils::isNotBlank)
        .map(word -> StringUtils.capitalize(word.trim())).collect(toList());
    return join(trimmedWords, ' ');
  }

  public static Map<String, String> parseRequestQuery(String queryString) {
    final Map<String, String> paramMap = new HashMap<>();
    if (isBlank(queryString)) {
      return paramMap;
    }
    String[] params = queryString.split("&");
    asList(params).forEach(param -> {
      String[] paramParts = param.split("=");
      if (paramParts.length > 1) {
        paramMap.put(paramParts[0], decode(paramParts[1], UTF_8));
      } else if (paramParts.length == 1) {
        paramMap.put(paramParts[0], null);
      }
    });
    return paramMap;
  }

  public static String getCountryNameByCode(String countryCode) {
    try {
      return new Locale("", countryCode).getDisplayCountry();
    } catch (Exception e) {
      log.warn("Invalid country code : {}", countryCode);
      return null;
    }
  }

}
