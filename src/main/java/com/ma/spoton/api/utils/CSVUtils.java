package com.ma.spoton.api.utils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.tomcat.util.http.fileupload.FileItemStream;

import com.ma.spoton.api.dtos.JobExportDto;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CSVUtils {

  public static final String TYPE = "text/csv";

  public static boolean validateCsvFile(FileItemStream file) {
    return TYPE.equals(file.getContentType())
        || "application/vnd.ms-excel".equalsIgnoreCase(file.getContentType());
  }

  public static String toCSV(List<?> objectList, char separator, boolean displayHeader,
      String... headerLables) {

    StringBuilder result = new StringBuilder();
    if (CollectionUtils.isEmpty(objectList)) {
      return result.toString();
    }

    if (displayHeader) {
      if (Objects.isNull(headerLables) || headerLables.length == 0) {
        result.append(getHeaders(objectList.get(0), separator));
      } else {
        result.append(Stream.of(headerLables).collect(Collectors.joining(separator + "")));
      }
      result.append("\n");
    }

    for (Object obj : objectList) {
      result.append(addObjectRow(obj, separator)).append("\n");
    }

    return result.toString();
  }
    
  public static String getHeaders(Object obj, char separator) {
    StringBuilder resultHeader = new StringBuilder();
    boolean firstField = true;
    Field[] fields = obj.getClass().getDeclaredFields();
    for (Field field : fields) {
      String value;
      try {
        field.setAccessible(true);
        value = field.getName();

        if (firstField) {
          resultHeader.append(value);
          firstField = false;
        } else {
          resultHeader.append(separator).append(value);
        }
        field.setAccessible(false);
      } catch (IllegalArgumentException e) {
        log.error("Error occurred while getting headers", e);
      }
    }
    return resultHeader.toString();

  }


  public static String addObjectRow(Object obj, char separator) {

    StringBuilder csvRow = new StringBuilder();
    Field[] fields = obj.getClass().getDeclaredFields();
    boolean firstField = true;
    for (Field field : fields) {
      Object value;
      try {
        field.setAccessible(true);
        value = field.get(obj);
        if (value == null)
          value = "";
        if (value.toString().contains("" + separator))
          value = "\"" + value + "\"";
        if (firstField) {
          csvRow.append(value);
          firstField = false;
        } else {
          csvRow.append(separator).append(value);
        }
        field.setAccessible(false);
      } catch (IllegalArgumentException | IllegalAccessException e) {
        log.error("Error occurred while getting row", e);
      }
    }
    return csvRow.toString();
  }

}
