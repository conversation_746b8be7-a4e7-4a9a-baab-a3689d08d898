package com.ma.spoton.api.utils;

import static com.ma.spoton.api.constants.BusinessConstants.DEFAULT_PAGE_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.PAGE_SIZE_MAX_LIMIT;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.data.domain.PageRequest.of;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PageUtils {

  public static PageRequest getPageRequest(Integer page, Integer pageSize) {
    int localPage = isNull(page) || page <= 0 ? 0 : page - 1;

    int localPageSize;
    if (isNull(pageSize) || pageSize <= 0) {
      localPageSize = DEFAULT_PAGE_SIZE;
    } else if (pageSize > PAGE_SIZE_MAX_LIMIT) {
      localPageSize = PAGE_SIZE_MAX_LIMIT;
    } else {
      localPageSize = pageSize;
    }

    return of(localPage, localPageSize);
  }

  public static PageRequest getPageRequest(Integer page, Integer pageSize, String orderBy,
      Direction sortOrder) {
    int localPage = isNull(page) || page <= 0 ? 0 : page - 1;

    int localPageSize;
    if (isNull(pageSize) || pageSize <= 0) {
      localPageSize = DEFAULT_PAGE_SIZE;
    } else if (pageSize > PAGE_SIZE_MAX_LIMIT) {
      localPageSize = PAGE_SIZE_MAX_LIMIT;
    } else {
      localPageSize = pageSize;
    }
    if (isNotBlank(orderBy) && nonNull(sortOrder)) {
      Order order = Direction.ASC.equals(sortOrder) ? Order.asc(orderBy) : Order.desc(orderBy);
      return of(localPage, localPageSize, Sort.by(order));
    } else {
      return of(localPage, localPageSize);
    }

  }

}
