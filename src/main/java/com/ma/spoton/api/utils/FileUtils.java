package com.ma.spoton.api.utils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

public class FileUtils {

  private FileUtils() {}

  private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);
 
  public static byte[] convertMultipartFileToByteArray(MultipartFile file) throws IOException {
    if (file.isEmpty()) {
        return new byte[0];
    }
    try (InputStream inputStream = file.getInputStream()) {
        return inputStream.readAllBytes();
    }
}
  public static void uploadBase64File(String base64Data, String fileAbsolutePath)
      throws IOException {
    LOGGER.info(">> uploadBase64File({}, {})", base64Data, fileAbsolutePath);
    if (StringUtils.isNotBlank(base64Data)) {
      if (base64Data.contains(",")) {
        base64Data = base64Data.split(",")[1];
      }
      byte[] data = Base64.getDecoder().decode(base64Data);
      try (OutputStream stream = new FileOutputStream(fileAbsolutePath)) {
        stream.write(data);
      }
    }
  }

  public static byte[] convertBase64DataIntoByteArray(String base64Data) {
    LOGGER.info(">> convertBase64DataIntoByteArray({})", base64Data);
    byte[] data = null;
    if (StringUtils.isNotBlank(base64Data)) {
      if (base64Data.contains(",")) {
        base64Data = base64Data.split(",")[1];
      }
      base64Data = base64Data.replaceAll("[()]", "");
      data = Base64.getDecoder().decode(base64Data.getBytes(StandardCharsets.UTF_8));
    }
    return data;
  }

  public static byte[] getImageBytesFromUrl(String url) throws IOException {
    byte[] imageBytes = null;
    InputStream is = null;
    URL javaUrl = null;
    try {
      javaUrl = new URL(url);
      is = javaUrl.openStream();

      imageBytes = IOUtils.toByteArray(is);
    } finally {
      if (is != null) {
        is.close();
      }
    }
    return imageBytes;
  }

  public static String getContentTypeFromBase64(String base64Data) {
    return base64Data.split(";")[0].split(":")[1];
  }

}
