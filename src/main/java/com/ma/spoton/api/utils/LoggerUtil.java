package com.ma.spoton.api.utils;

import org.slf4j.Logger;
import org.slf4j.MDC;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

public class LoggerUtil {
	
	private static final String OPERATION_NAME = "operation";
	private static final String SLA = "sla";
	public static Marker MARKER_SLA_LOGS = MarkerFactory.getMarker("SLA_LOGGER");
	
	public static long logSLA(Logger log, String operationName, long startTime, String message) {
		long end = System.currentTimeMillis();
		MDC.put(OPERATION_NAME, operationName);
		MDC.put(SLA, String.valueOf(end - startTime));
		log.info(MARKER_SLA_LOGS,message);
		MDC.remove(OPERATION_NAME);
		MDC.remove(SLA);
		return end;
	}

}
