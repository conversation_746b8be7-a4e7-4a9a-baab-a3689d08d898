package com.ma.spoton.api.utils;

import static com.ma.spoton.api.services.FleetServiceImpl.PETERBILT_ONSITE_YARDS;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ma.spoton.api.entities.BaseEntity;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.TrailerAudit;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.TrailerAuditRepository;

@Component
public class ReportUtils {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private TrailerAuditRepository trailerAuditRepository;

    @Autowired
    private LocationRepository locationRepository;

    private List<Long> peterbiltLocIds;

    @PostConstruct
    public void postConstruct() {
        peterbiltLocIds = locationRepository.findAllByLocationNameIn(PETERBILT_ONSITE_YARDS)
                .stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
    }

    /**
     * Find any spots associated with the fleet. If there are no spots (jobs), it must be an unused trailer. Search
     * createdDate for the trailer in TrailerAudit and calculate age. If there are spots involved, go back in reverse
     * order of spots till a pickup location which is not in report eligible
     */
    public String calculateFleetAge(Fleet fleet, ZonedDateTime currentTime) {

        if (fleet.getLastFleetAgeResetTime() != null) {
            return calculateAge(fleet.getLastFleetAgeResetTime(), currentTime);
        }

        List<Job> jobs = jobRepository.findAllLatestCompletedOrInTransitByFleetIds(
                Stream.of(fleet)
                        .map(Fleet::getId)
                        .collect(Collectors.toSet()));

        if (CollectionUtils.isEmpty(jobs)) {
            List<TrailerAudit> trailerAudits = trailerAuditRepository.findAllLatestByFleetIds(
                    Stream.of(fleet)
                            .map(Fleet::getId)
                            .collect(Collectors.toSet()));

            TrailerAudit oldestAuditWithEligibleLoc = null;
            for (TrailerAudit trailerAudit : trailerAudits) {
                if (peterbiltLocIds.contains(trailerAudit.getLocation().getId())) {
                    oldestAuditWithEligibleLoc = trailerAudit;
                } else {
                    break; // break out of loop when inEligible location is found.
                }
            }

            if (oldestAuditWithEligibleLoc != null) {
                ZonedDateTime startDateTime = oldestAuditWithEligibleLoc.getCreatedDate();
                return calculateAge(startDateTime, currentTime);
            } else {
                ZonedDateTime startDateTime = fleet.getCreatedDate();
                return calculateAge(startDateTime, currentTime);
            }
        } else {

            Job oldestJobWithEligibleLoc = null;
            for (Job job : jobs) {
                oldestJobWithEligibleLoc = job;
                if (!peterbiltLocIds.contains(job.getPickupLocation().getId())) {
                    break; // break out of loop when inEligible pickUplocation is found.
                }
            }

            assert oldestJobWithEligibleLoc != null;
            ZonedDateTime startDateTime = oldestJobWithEligibleLoc.getDropDateTime();
            return calculateAge(startDateTime, currentTime);
        }
    }

    private String calculateAge(ZonedDateTime createdDateTime, ZonedDateTime currentTime) {
        Duration duration = Duration.between(createdDateTime, currentTime);

        long totalSeconds = Math.max(duration.getSeconds(), 0); // avoid negatives

        long days = totalSeconds / (24 * 3600);
        long remainder = totalSeconds % (24 * 3600);

        long hours = remainder / 3600;
        remainder %= 3600;

        long minutes = remainder / 60;
        long seconds = remainder % 60;

        return String.format(
                "%d %s: %d %s: %d %s: %d %s",
                days, days == 1 ? "day" : "days",
                hours, hours == 1 ? "hr" : "hrs",
                minutes, minutes == 1 ? "min" : "mins",
                seconds, seconds == 1 ? "sec" : "secs"
        );
    }
}
