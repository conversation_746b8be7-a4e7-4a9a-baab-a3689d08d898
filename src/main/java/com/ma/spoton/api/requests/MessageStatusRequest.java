package com.ma.spoton.api.requests;

import javax.validation.constraints.NotNull;
import com.ma.spoton.api.entities.Message.Status;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageStatusRequest {

  @NotNull(message = "NotNull.messageStatusRequest.status")
  private Status status;

}
