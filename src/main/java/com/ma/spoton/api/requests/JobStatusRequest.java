package com.ma.spoton.api.requests;

import java.util.Objects;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.utils.DateTimeUtils;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Builder
@Data
@Slf4j
public class JobStatusRequest {

    @NotNull(message = "NotNull.jobStatusRequest.status")
    private Status status;

    private String statusDateTime;

    private String notes;

    private String lat;

    private String lng;

    @AssertTrue(message = "NotValid.jobStatusRequest.statusDateTime(::)"
                          + BusinessConstants.FORM_DATE_TIME_FORMAT)
    private boolean isStatusDateTimeValid() {
        if (StringUtils.isNotBlank(statusDateTime)) {
            try {
                DateTimeUtils.convertStringToLocalDateTime(statusDateTime,
                                                           BusinessConstants.FORM_DATE_TIME_FORMAT);
            } catch (Exception e) {
                log.error("Status Date time cannot be parsed!", e.getMessage());
                return false;
            }
        }
        return true;
    }

    @AssertTrue(message = "NotValid.jobStatusRequest.status")
    private boolean isStatusValid() {
        return Objects.isNull(status) || Status.IN_TRANSIT.equals(status) || Status.COMPLETED.equals(status)
               || Status.IN_PROGRESS.equals(status); // IN_PROGRESS is used only for JobRouteStep
    }

}
