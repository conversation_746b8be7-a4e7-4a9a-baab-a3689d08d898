package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.EMAIL_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.PHONE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.TIME_ZONE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_FIRST_NAME_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_LAST_NAME_MAX_SIZE;
import java.util.Arrays;
import java.util.List;
import java.util.TimeZone;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;
import com.ma.spoton.api.utils.ValidationUtils;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class UserRequest {

  @NotBlank(message = "NotBlank.userRequest.firstName")
  @Size(max = USER_FIRST_NAME_MAX_SIZE,
      message = "Size.userRequest.firstName(::)" + USER_FIRST_NAME_MAX_SIZE)
  private String firstName;

  @NotBlank(message = "NotBlank.userRequest.lastName")
  @Size(max = USER_LAST_NAME_MAX_SIZE,
      message = "Size.userRequest.lastName(::)" + USER_LAST_NAME_MAX_SIZE)
  private String lastName;

  @NotBlank(message = "NotBlank.userRequest.email")
  @Size(max = EMAIL_MAX_SIZE, message = "Size.userRequest.email(::)" + EMAIL_MAX_SIZE)
  @Pattern(regexp = ValidationUtils.EMAIL_PATTERN, message = "Email.userRequest.email")
  private String email;

  @Size(max = PHONE_MAX_SIZE, message = "Size.userRequest.phone(::)" + PHONE_MAX_SIZE)
  private String phone;

  @Size(max = TIME_ZONE_MAX_SIZE, message = "Size.userRequest.timeZone(::)" + TIME_ZONE_MAX_SIZE)
  private String timeZone;

  private List<String> roleIds;
  private List<String> clientIds;
  
  private List<String> locationIds;

//  @AssertTrue(message = "NotValid.userRequest.timeZone")
//  private boolean isTimeZoneValid() {
//    return StringUtils.isBlank(timeZone)
//        || Arrays.asList(TimeZone.getAvailableIDs()).contains(timeZone);
//  }

}
