package com.ma.spoton.api.requests;


import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.entities.Notification.Channel;
import com.ma.spoton.api.entities.Notification.DeliveryStatus;
import com.ma.spoton.api.utils.DateTimeUtils;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@Builder
@Data
public class NotificationRequest {

  private String fromUserId;
  private String toUserId;

  @NotNull(message = "NotNull.notificationRequest.channel")
  private Channel channel;

  private String messageTitle;

  @NotBlank(message = "NotBlank.notificationRequest.messageBody")
  private String messageBody;

  private String messageReferrenceId;

  private String startTime;

  private DeliveryStatus deliveryStatus;

  private String failureReason;
  
  private String toUserEmail;

  @AssertTrue(message = "NotBlank.notificationRequest.messageTitle")
  private boolean isMessageTitle() {
    if (nonNull(channel) && channel.equals(Channel.EMAIL)) {
      return isNotBlank(messageTitle);
    }
    return true;
  }

  @AssertTrue(message = "NotValid.notificationRequest.startTime(::)"
      + BusinessConstants.FORM_DATE_TIME_FORMAT)
  private boolean isStartTimeValid() {
    if (StringUtils.isNotBlank(startTime)) {
      try {
        DateTimeUtils.convertStringToLocalDateTime(startTime,
            BusinessConstants.FORM_DATE_TIME_FORMAT);
      } catch (Exception e) {
        log.error("Start Time cannot be parsed!", e.getMessage());
        return false;
      }
    }
    return true;
  }
}
