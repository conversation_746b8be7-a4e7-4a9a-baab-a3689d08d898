package com.ma.spoton.api.requests;

import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.web.multipart.MultipartFile;

import com.ma.spoton.api.entities.GuardEntryExit.Type;
import lombok.Builder;
import lombok.Data;

//import java.io.File;
//import java.time.LocalDate;
//import java.time.LocalDateTime;

@Builder
@Data
public class FleetEntryExitRequest {

  @NotBlank(message = "NotBlank.fleetEntryExitRequest.fleetId")
  private String fleetId;

  @NotNull(message = "NotNull.fleetEntryExitRequest.type")
  private Type type;

  private String notes;
  
  private String spotId;

  private String proNumber;
  
  private String carrier;
  
  private String sub;
  
  private String loadStatus;
  
  private String sequenceNumber;
  
  private String tractorNumber;
  
  private String driver;

  private List<String> suppliers;

  private String billOfLandingType;
  
  private MultipartFile billOfLandingImage;
  
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate dateOfPickup;
  
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate dueOfPlant;
  
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate dateOfArrival;
  
}
