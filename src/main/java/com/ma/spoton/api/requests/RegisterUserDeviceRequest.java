package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.USER_DEVICE_MODEL_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_DEVICE_NAME_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_DEVICE_REGISTRATION_ID_MAX_SIZE;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ma.spoton.api.entities.UserDevice.DeviceType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RegisterUserDeviceRequest {

  @NotBlank(message = "NotBlank.registerUserDeviceRequest.deviceRegistrationId")
  @Size(max = USER_DEVICE_REGISTRATION_ID_MAX_SIZE,
      message = "Size.registerUserDeviceRequest.deviceRegistrationId(::)"
          + USER_DEVICE_REGISTRATION_ID_MAX_SIZE)
  private String deviceRegistrationId;

  @Size(max = USER_DEVICE_NAME_MAX_SIZE,
      message = "Size.registerUserDeviceRequest.deviceName(::)" + USER_DEVICE_NAME_MAX_SIZE)
  private String deviceName;

  @Size(max = USER_DEVICE_MODEL_MAX_SIZE,
      message = "Size.registerUserDeviceRequest.deviceModel(::)" + USER_DEVICE_MODEL_MAX_SIZE)
  private String deviceModel;

  @NotNull(message = "NotNull.registerUserDeviceRequest.deviceType")
  private DeviceType deviceType;
  
 // @NotNull(message = "NotNull.registerUserDeviceRequest.uuid")
  private String deviceUuid;

}
