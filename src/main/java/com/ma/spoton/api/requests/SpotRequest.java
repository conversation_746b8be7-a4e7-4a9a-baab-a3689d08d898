package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.SPOT_NAME_MAX_SIZE;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ma.spoton.api.entities.Spot.SpotType;
import com.ma.spoton.api.entities.Spot.Status;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class SpotRequest {

  @NotBlank(message = "NotBlank.spotRequest.locationId")
  private String locationId;

  @NotBlank(message = "NotBlank.spotRequest.spotName")
  @Size(max = SPOT_NAME_MAX_SIZE, message = "Size.spotRequest.spotName(::)" + SPOT_NAME_MAX_SIZE)
  private String spotName;

  @NotNull(message = "NotNull.spotRequest.type")
  private SpotType type;

  @NotNull(message = "NotNull.spotRequest.status")
  private Status status;

  @NotNull(message = "NotNull.spotRequest.latitude")
  private Double latitude;

  @NotNull(message = "NotNull.spotRequest.longitude")
  private Double longitude;

  private String remarks;
  
  private String locationName;
}
