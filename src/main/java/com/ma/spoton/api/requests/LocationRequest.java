package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_CITY_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_COUNTRY_CODE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_STATE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_STREET_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_ZIP_CODE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.LOCATION_NAME_MAX_SIZE;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class LocationRequest {

  @NotBlank(message = "NotBlank.locationRequest.locationName")
  @Size(max = LOCATION_NAME_MAX_SIZE,
      message = "Size.locationRequest.locationName(::)" + LOCATION_NAME_MAX_SIZE)
  private String locationName;

  @NotBlank(message = "NotBlank.addressRequest.street")
  @Size(max = ADDRESS_STREET_MAX_SIZE,
      message = "Size.clientRequest.street(::)" + ADDRESS_STREET_MAX_SIZE)
  private String street;

  @NotBlank(message = "NotBlank.addressRequest.city")
  @Size(max = ADDRESS_CITY_MAX_SIZE,
      message = "Size.clientRequest.city(::)" + ADDRESS_CITY_MAX_SIZE)
  private String city;

  @NotBlank(message = "NotBlank.addressRequest.state")
  @Size(max = ADDRESS_STATE_MAX_SIZE,
      message = "Size.addressRequest.state(::)" + ADDRESS_STATE_MAX_SIZE)
  private String state;

  @NotBlank(message = "NotBlank.addressRequest.zip")
  @Size(max = ADDRESS_ZIP_CODE_MAX_SIZE,
      message = "Size.clientRequest.zip(::)" + ADDRESS_ZIP_CODE_MAX_SIZE)
  private String zip;

  @NotBlank(message = "NotBlank.addressRequest.country")
  @Size(max = ADDRESS_COUNTRY_CODE_MAX_SIZE,
      message = "Size.clientRequest.country(::)" + ADDRESS_COUNTRY_CODE_MAX_SIZE)
  private String country;

  @NotNull(message = "NotNull.locationRequest.latitude")
  private Double latitude;

  @NotNull(message = "NotNull.locationRequest.longitude")
  private Double longitude;

  private String locationMapJson;
  private String remarks;
  private String mapImageBase64;
  private String pieChartColor;
  
}
