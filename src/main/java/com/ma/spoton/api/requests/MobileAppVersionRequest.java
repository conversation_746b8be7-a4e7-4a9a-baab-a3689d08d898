package com.ma.spoton.api.requests;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder 
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MobileAppVersionRequest {
	
	@NotNull(message = "NotNull.mobileAppVersionRequest.currentVersion")
	private float currentVersion;
	
	@NotNull(message = "NotNull.mobileAppVersionRequest.os")
	private String os;
}
