package com.ma.spoton.api.requests;


import static com.ma.spoton.api.utils.ValidationUtils.EMAIL_PATTERN;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ForgotPasswordRequest {

  @NotBlank(message = "NotBlank.forgotPasswordRequest.email")
  @Pattern(regexp = EMAIL_PATTERN, message = "Email.forgotPasswordRequest.email")
  private String email;

}
