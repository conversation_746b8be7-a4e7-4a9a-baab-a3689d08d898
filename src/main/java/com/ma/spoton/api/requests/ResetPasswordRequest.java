package com.ma.spoton.api.requests;


import static com.ma.spoton.api.constants.BusinessConstants.USER_PASSWORD_MAX_SIZE;
import static io.micrometer.core.instrument.util.StringUtils.isNotBlank;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ResetPasswordRequest {

  @NotBlank(message = "NotBlank.resetPasswordRequest.token")
  private String token;

  @NotBlank(message = "NotBlank.resetPasswordRequest.newPassword")
  @Size(max = USER_PASSWORD_MAX_SIZE,
      message = "Size.resetPasswordRequest.newPassword(::)" + USER_PASSWORD_MAX_SIZE)
  private String newPassword;

  @NotBlank(message = "NotBlank.resetPasswordRequest.confirmNewPassword")
  @Size(max = USER_PASSWORD_MAX_SIZE,
      message = "Size.resetPasswordRequest.confirmNewPassword(::)" + USER_PASSWORD_MAX_SIZE)
  private String confirmNewPassword;

  @AssertTrue(message = "NotMatch.resetPasswordRequest.newPassword")
  private boolean isConfirmNewPasswordValid() {
    if (isNotBlank(newPassword) && isNotBlank(confirmNewPassword)) {
      return newPassword.equals(confirmNewPassword);
    }
    return true;
  }

}
