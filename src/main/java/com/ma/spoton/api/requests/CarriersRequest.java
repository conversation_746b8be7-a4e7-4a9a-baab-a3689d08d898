//package com.ma.spoton.api.requests;
//
//import java.util.List;
//
//import javax.validation.constraints.NotBlank;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//@Builder
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class CarriersRequest {
//	
//	@NotBlank(message = "NotBlank.carrierRequest.carrier")
//	private String carrier;
//		
//}
