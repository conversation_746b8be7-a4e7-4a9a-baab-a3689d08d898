package com.ma.spoton.api.requests;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientFleetRequest {

  @NotEmpty(message = "NotEmpty.clientFleetRequest.fleetIds")
  private List<String> fleetIds;

}
