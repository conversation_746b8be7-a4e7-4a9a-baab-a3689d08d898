package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.EMAIL_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.PHONE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.TIME_ZONE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_FIRST_NAME_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.USER_LAST_NAME_MAX_SIZE;
import static com.ma.spoton.api.utils.ValidationUtils.EMAIL_PATTERN;
import static java.util.Arrays.asList;
import static java.util.TimeZone.getAvailableIDs;
import static org.apache.commons.lang3.StringUtils.isBlank;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Builder
public class UpdateProfileRequest {

  @NotBlank(message = "NotBlank.updateProfileRequest.firstName")
  @Size(max = USER_FIRST_NAME_MAX_SIZE,
      message = "Size.updateProfileRequest.firstName(::)" + USER_FIRST_NAME_MAX_SIZE)
  private String firstName;

  @NotBlank(message = "NotBlank.updateProfileRequest.lastName")
  @Size(max = USER_LAST_NAME_MAX_SIZE,
      message = "Size.updateProfileRequest.lastName(::)" + USER_LAST_NAME_MAX_SIZE)
  private String lastName;

  @Size(max = EMAIL_MAX_SIZE, message = "Size.updateProfileRequest.email(::)" + EMAIL_MAX_SIZE)
  @Pattern(regexp = EMAIL_PATTERN, message = "Email.updateProfileRequest.email")
  private String email;

  @Size(max = PHONE_MAX_SIZE, message = "Size.updateProfileRequest.phone(::)" + PHONE_MAX_SIZE)
  private String phone;

  @Size(max = TIME_ZONE_MAX_SIZE,
      message = "Size.updateProfileRequest.name(::)" + TIME_ZONE_MAX_SIZE)
  private String timeZone;

  private String newPassword;

  @AssertTrue(message = "NotValid.updateProfileRequest.timeZone")
  private boolean isTimeZoneValid() {
    return isBlank(timeZone) || asList(getAvailableIDs()).contains(timeZone);
  }

}
