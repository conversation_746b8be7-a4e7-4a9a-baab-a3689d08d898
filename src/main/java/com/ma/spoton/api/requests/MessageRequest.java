package com.ma.spoton.api.requests;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import com.ma.spoton.api.entities.Message.Type;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class MessageRequest {

  @NotEmpty(message = "NotEmpty.messageRequest.toUserIds")
  private List<String> toUserIds;

  @NotBlank(message = "NotBlank.messageRequest.messageBody")
  private String messageBody;

  private String clientLocationId;

  private String fleetId;

  private String pickupLocationId;

  private String pickupSpotId;

  private String dropLocationId;

  private String dropSpotId;
  
  private String sequenceAsn;

  @NotNull(message = "NotNull.messageRequest.type")
  private Type type;

}
