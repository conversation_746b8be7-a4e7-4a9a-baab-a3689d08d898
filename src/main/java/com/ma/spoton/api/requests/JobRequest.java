package com.ma.spoton.api.requests;

import static com.ma.spoton.api.entities.JobRouteStep.StepStatus;
import static com.ma.spoton.api.entities.JobRouteStep.StepType;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.entities.Job.Priority;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class JobRequest {

    private String assignedToUserId;

    private Bucket bucket;

    @NotBlank(message = "NotBlank.jobRequest.fleetId")
    private String fleetId;

    private String fleetStatus;

    @NotBlank(message = "NotBlank.jobRequest.pickupLocationId")
    private String pickupLocationId;

    @NotBlank(message = "NotBlank.jobRequest.pickupSpotId")
    private String pickupSpotId;

    @NotNull(message = "NotNull.jobRequest.priority")
    private Priority priority;

    private String dropLocationId;
    private String dropSpotId;
    private String description;

    private Boolean isEdit;

    private String sequenceAsn;

    private String messageId;
    private String replyUserId;

    private List<IntermediateRoute> routeList = new ArrayList<>();
    private boolean isBoxTruckOrVan;

    @Data
    @Builder
    public static class IntermediateRoute {

        private String locationId;
        private String spotId;
        private StepType type;
        private Optional<StepStatus> status; // updated as and when completed, in_transit.
        private Optional<String> notes;
    }
}
