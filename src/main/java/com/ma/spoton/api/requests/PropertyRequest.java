package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.PROPERTY_REQUEST_KEY_MAX_SIZE;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ma.spoton.api.entities.Property.AccessType;
import com.ma.spoton.api.entities.Property.PropertyType;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Builder
@Data
public class PropertyRequest {

  @NotBlank(message = "NotBlank.propertyRequest.key")
  @Size(max = PROPERTY_REQUEST_KEY_MAX_SIZE,
      message = "Size.propertyRequest.key(::)" + PROPERTY_REQUEST_KEY_MAX_SIZE)
  private String key;

  @NotBlank(message = "NotBlank.propertyRequest.value")
  private String value;

  @NotNull(message = "NotNull.propertyRequest.accessType")
  private AccessType accessType;

  @NotNull(message = "NotNull.propertyRequest.propertyType")
  private PropertyType type;

}
