package com.ma.spoton.api.requests;

import com.ma.spoton.api.entities.Job.Bucket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder 
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChangeQueueOrderRequest {

	String clientId;
	Bucket bucket;
    Long dragIndex;
    Long dropIndex;
}
