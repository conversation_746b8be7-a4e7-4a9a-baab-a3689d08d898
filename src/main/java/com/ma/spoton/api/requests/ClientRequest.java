package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_CITY_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_COUNTRY_CODE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_STATE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_STREET_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.ADDRESS_ZIP_CODE_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.CLIENT_NAME_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.CONTACT_PERSON_EMAIL_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.CONTACT_PERSON_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.CONTACT_PERSON_PHONE_MAX_SIZE;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ClientRequest {

  @NotBlank(message = "NotBlank.clientRequest.clientName")
  @Size(max = CLIENT_NAME_MAX_SIZE,
      message = "Size.clientRequest.clientName(::)" + CLIENT_NAME_MAX_SIZE)
  private String clientName;

  @NotBlank(message = "NotBlank.addressRequest.street")
  @Size(max = ADDRESS_STREET_MAX_SIZE,
      message = "Size.addressRequest.street(::)" + ADDRESS_STREET_MAX_SIZE)
  private String street;

  @NotBlank(message = "NotBlank.addressRequest.city")
  @Size(max = ADDRESS_CITY_MAX_SIZE,
      message = "Size.addressRequest.city(::)" + ADDRESS_CITY_MAX_SIZE)
  private String city;

  @NotBlank(message = "NotBlank.addressRequest.state")
  @Size(max = ADDRESS_STATE_MAX_SIZE,
      message = "Size.addressRequest.state(::)" + ADDRESS_STATE_MAX_SIZE)
  private String state;

  @NotBlank(message = "NotBlank.addressRequest.zip")
  @Size(max = ADDRESS_ZIP_CODE_MAX_SIZE,
      message = "Size.addressRequest.zip(::)" + ADDRESS_ZIP_CODE_MAX_SIZE)
  private String zip;

  @NotBlank(message = "NotBlank.addressRequest.country")
  @Size(max = ADDRESS_COUNTRY_CODE_MAX_SIZE,
      message = "Size.addressRequest.country(::)" + ADDRESS_COUNTRY_CODE_MAX_SIZE)
  private String country;

  @NotBlank(message = "NotBlank.clientRequest.contactPerson")
  @Size(max = CONTACT_PERSON_MAX_SIZE,
      message = "Size.clientRequest.contactPerson(::)" + CONTACT_PERSON_MAX_SIZE)
  private String contactPerson;

  @NotBlank(message = "NotBlank.clientRequest.contactEmail")
  @Size(max = CONTACT_PERSON_EMAIL_MAX_SIZE,
      message = "Size.clientRequest.contactEmail(::)" + CONTACT_PERSON_EMAIL_MAX_SIZE)
  private String contactEmail;

//  @NotBlank(message = "NotBlank.clientRequest.contactPhone")
  @Size(max = CONTACT_PERSON_PHONE_MAX_SIZE,
      message = "Size.clientRequest.contactPhone(::)" + CONTACT_PERSON_PHONE_MAX_SIZE)
  private String contactPhone;

  private String remarks;
  
  @NotBlank(message = "NotBlank.clientRequest.timeZone")
  private String timeZone;
  
  private float overTime;
  
  private long driverJobReAssignAfter;
  
  private long spotterJobReAssignAfter;
}
