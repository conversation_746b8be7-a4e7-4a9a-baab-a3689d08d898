package com.ma.spoton.api.requests;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.ma.spoton.api.entities.Message.Type;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ContactUsRequest {
	
	  @NotBlank(message = "NotBlank.contactUsRequest.subject")
	  private String subject;

	  @NotBlank(message = "NotBlank.contactUsRequest.messageBody")
	  private String messageBody;

	  private String type;
	  
}
