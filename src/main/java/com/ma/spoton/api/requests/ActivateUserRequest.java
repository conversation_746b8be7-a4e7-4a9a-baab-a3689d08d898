package com.ma.spoton.api.requests;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder 
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActivateUserRequest {
	 
	@NotBlank(message = "NotBlank.ActivateUserRequest.uuid")
	String userId;
	
	@NotBlank(message = "NotBlank.ActivateUserRequest.password")
	String password;
	
}
