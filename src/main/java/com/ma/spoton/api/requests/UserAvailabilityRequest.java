package com.ma.spoton.api.requests;

import java.time.DayOfWeek;
import java.time.LocalTime;

import javax.validation.constraints.NotBlank;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserAvailabilityRequest {
	
	@NotBlank(message = "NotBlank.userAvailabilityRequest.userId")
	private String userId;
	
	private DayOfWeek dayOfWeek;
	private LocalTime startingTime;	
	private LocalTime endingTime;
	private Boolean active;
	private LocalTime breakStartingTime;
	private LocalTime breakEndingTime;
	
}
