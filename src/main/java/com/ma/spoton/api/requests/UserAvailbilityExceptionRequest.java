package com.ma.spoton.api.requests;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.validation.constraints.NotBlank;

import com.ma.spoton.api.entities.UserAvailabilityException.Type;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserAvailbilityExceptionRequest {

	@NotBlank(message = "NotBlank.userAvailabilityRequest.userId")
	private String userId;
	
	private LocalDate date;
	private Type type;
	private LocalTime startingTime;
    private LocalTime endingTime;
    private LocalTime breakStartingTime;
	private LocalTime breakEndingTime;
}
