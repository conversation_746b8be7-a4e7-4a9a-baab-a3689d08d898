package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.MessageDto;
import com.ma.spoton.api.entities.ContactUs;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.requests.ContactUsRequest;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
uses = {UserMapper.class, LocationMapper.class, SpotMapper.class, FleetMapper.class, JobMapper.class, AuditMapper.class},
imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface ContactUsMapper {
	ContactUs mapToEntity(ContactUsRequest contactUsRequest);

//	@Mapping(target = "contactUsId", source = "contactUs.uuid")
//	MessageDto mapToDto(Message message, String timeZone);
}
