package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.OverTimeDto;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
imports = { Objects.class,DateTimeUtils.class, BusinessConstants.class})
public interface OverTimeMapper {

	@Mapping(target = "userId", source = "overTime.user.uuid")
	@Mapping(target = "firstName", source = "overTime.user.firstName")
	@Mapping(target = "lastName", source = "overTime.user.lastName")
	@Mapping(target = "email", source = "overTime.user.email")
	@Mapping(target = "phone", source = "overTime.user.phone")
	OverTimeDto mapToDto(OverTime overTime);
}
