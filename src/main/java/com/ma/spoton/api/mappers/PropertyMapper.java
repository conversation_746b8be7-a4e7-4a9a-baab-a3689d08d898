package com.ma.spoton.api.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.dtos.PropertyDto;
import com.ma.spoton.api.entities.Property;
import com.ma.spoton.api.requests.PropertyRequest;

@Mapper(componentModel = "spring", imports = {AuditMapperImpl.class, UserMapperImpl.class},
    implementationName = "CCPropertyMapperImpl")
public interface PropertyMapper {

  @Mapping(target = "key", expression = "java(propertyRequest.getKey().replace(\" \", \"_\") )")
  Property mapToEntity(PropertyRequest propertyRequest);

  @Mapping(target = "propertyId", source = "property.uuid")
  @Mapping(target = "audit",
      expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(property, timeZone) )")
  PropertyDto mapToDto(Property property, String timeZone);

  @Mapping(target = "key", expression = "java(propertyRequest.getKey().replace(\" \", \"_\") )")
  void updateEntity(PropertyRequest propertyRequest, @MappingTarget Property property);

  @Mapping(target = "propertyId", source = "property.uuid")
  PropertyDto mapToPropertyDto(Property property);
}
