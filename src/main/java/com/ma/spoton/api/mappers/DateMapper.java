package com.ma.spoton.api.mappers;

import java.time.ZonedDateTime;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring")
public interface DateMapper {

    @Named("toFormattedDate")
    static String toFormattedDate(ZonedDateTime date, @Context String timeZone) {
        if (date == null) {
            return "";
        }
        return DateTimeUtils.convertZonedDateTimeToString(
                date, BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone);
    }

    static String toUserFormattedDate(ZonedDateTime date, User user) {
        if (date == null) {
            return "";
        }
        return DateTimeUtils.convertZonedDateTimeToString(
                date, BusinessConstants.DISPLAY_DATE_TIME_FORMAT, user.getTimeZone());
    }
}
