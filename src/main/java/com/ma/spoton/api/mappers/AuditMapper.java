package com.ma.spoton.api.mappers;

import org.mapstruct.Context;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import com.ma.spoton.api.dtos.AuditDto;
import com.ma.spoton.api.entities.BaseEntity;

@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        uses = {UserMapper.class, DateMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuditMapper {

    @Mapping(target = "createdDate", source = "baseEntity.createdDate", qualifiedByName = "toFormattedDate")
    @Mapping(target = "lastModifiedDate", source = "baseEntity.lastModifiedDate", qualifiedByName = "toFormattedDate")
    AuditDto mapToDto(BaseEntity baseEntity, @Context String timeZone);

}
