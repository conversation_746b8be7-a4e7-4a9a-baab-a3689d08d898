package com.ma.spoton.api.mappers;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.dtos.UserExportDto;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.requests.UpdateProfileRequest;
import com.ma.spoton.api.requests.UserRequest;

@Mapper(componentModel = "spring", imports = {DateMapper.class})
public interface UserMapper {

    @Named("mapFullName")
    static String mapFullName(User assignedTo) {
        if (assignedTo == null) {
            return "";
        }
        String first = StringUtils.defaultIfBlank(assignedTo.getFirstName(), "");
        String last = StringUtils.defaultIfBlank(assignedTo.getLastName(), "");
        return (first + " " + last).trim();
    }

    @Mapping(target = "userId", source = "uuid")
    UserAuthDto mapToAuthDto(User user);

    @Mapping(target = "uuid", source = "userId")
    User mapToUser(UserAuthDto userauthdto);

    @Mapping(target = "userId", source = "user.uuid")
    @Mapping(target = "lastLoginTime", expression = "java( DateMapper.toUserFormattedDate(user.getLastLoginTime(), user) )")
    @Mapping(target = "clients", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "locations", ignore = true)
    UserDto mapToDto(User user);

    User mapToEntity(UserRequest userRequest);

    User updateEntity(UserRequest userRequest, @MappingTarget User user);

    User updateEntity(UpdateProfileRequest updateProfileRequest, @MappingTarget User user);

    @Mapping(target = "userId", source = "user.uuid")
    @Mapping(target = "lastLoginTime", expression = "java( DateMapper.toUserFormattedDate(user.getLastLoginTime(), user) )")
    @Mapping(target = "roles", ignore = true)
    UserExportDto mapToExportDto(User user, @Context String timeZone);
}
