package com.ma.spoton.api.mappers;

import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.LocationDto;
import com.ma.spoton.api.dtos.LocationExportDto;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.requests.LocationRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
    imports = {StringUtils.class, Objects.class, DateTimeUtils.class, BusinessConstants.class})
public abstract class LocationMapper {

  @Autowired
  protected AuditMapper auditMapper;

  @Value("${application.cdn.base.url}")
  protected String cdnBaseUrl;

  @Value("${application.location.map-image.path}")
  protected String mapImagePath;

  public abstract Location mapToEntity(LocationRequest locationRequest);

  public abstract Location updateEntity(LocationRequest locationRequest,
      @MappingTarget Location location);

  @Mapping(target = "clientId", source = "location.client.uuid")
  @Mapping(target = "locationId", source = "location.uuid")
  @Mapping(target = "mapImageUrl",
      expression = "java( StringUtils.isNotBlank(location.getMapImagePath()) "
          + "? (cdnBaseUrl + \"/\" + mapImagePath + \"/\" + location.getMapImagePath()) : \"\" )")
  @Mapping(target = "audit", expression = "java( auditMapper.mapToDto(location, timeZone) )")
  public abstract LocationDto mapToDto(Location location, String timeZone);

  @Mapping(target = "clientId", source = "location.client.uuid")
  @Mapping(target = "locationId", source = "location.uuid")
  @Mapping(target = "mapImageUrl",
      expression = "java( StringUtils.isNotBlank(location.getMapImagePath()) "
          + "? (cdnBaseUrl + \"/\" + mapImagePath + \"/\" + location.getMapImagePath()) : \"\" )")
  @Mapping(target = "audit", ignore = true)
  public abstract LocationDto mapToDto(Location location);

  @Named("mapToBasicDto")
  @Mapping(target = "clientId", source = "location.client.uuid")
  @Mapping(target = "locationId", source = "location.uuid")
  @Mapping(target = "mapImageUrl", ignore = true)
  @Mapping(target = "locationMapJson", ignore = true)
  @Mapping(target = "audit", ignore = true)
  public abstract LocationDto mapToBasicDto(Location location);

  @Mapping(target = "locationId", source = "location.uuid")
  @Mapping(target = "clientName", source = "location.client.clientName")
  @Mapping(target = "mapImageUrl",
      expression = "java( StringUtils.isNotBlank(location.getMapImagePath()) "
          + "? (cdnBaseUrl + \"/\" + mapImagePath + \"/\" + location.getMapImagePath()) : \"\" )")
  @Mapping(target = "createdBy", source = "location.createdBy.email")
  @Mapping(target = "lastModifiedBy", source = "location.lastModifiedBy.email")
  @Mapping(target = "createdDate",
      expression = "java( Objects.nonNull(location.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(location.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "lastModifiedDate",
      expression = "java( Objects.nonNull(location.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(location.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  public abstract LocationExportDto mapToExportDto(Location location, String timeZone);

}
