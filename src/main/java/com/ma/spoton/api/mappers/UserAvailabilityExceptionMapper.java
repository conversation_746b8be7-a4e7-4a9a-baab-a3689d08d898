package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.UserAvailabilityExceptionDto;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.requests.UserAvailbilityExceptionRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
imports = { Objects.class,DateTimeUtils.class, BusinessConstants.class})
public interface UserAvailabilityExceptionMapper {

	
	UserAvailabilityException mapToEntity(UserAvailbilityExceptionRequest userAvailabilityExceptionRequest);
	
	@Mapping(target = "userAvailabilityExceptionId", source = "uuid")
	@Mapping(target = "firstName", source = "userAvailabilityException.user.firstName")
	@Mapping(target = "lastName", source = "userAvailabilityException.user.lastName")
	@Mapping(target = "email", source = "userAvailabilityException.user.email")
	@Mapping(target = "phone", source = "userAvailabilityException.user.phone")
	UserAvailabilityExceptionDto mapToDto(UserAvailabilityException userAvailabilityException);
}
