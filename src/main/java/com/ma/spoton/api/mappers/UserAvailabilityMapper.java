package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.UserAvailabilityDto;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.requests.UserAvailabilityRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
imports = { Objects.class,DateTimeUtils.class, BusinessConstants.class})
public interface UserAvailabilityMapper {

	UserAvailability mapToEntity(UserAvailabilityRequest userAvailabilityRequest);
    
	@Mapping(target = "userAvailabilityId", source = "uuid")
	@Mapping(target = "firstName", source = "userAvailability.user.firstName")
	@Mapping(target = "lastName", source = "userAvailability.user.lastName")
	@Mapping(target = "email", source = "userAvailability.user.email")
	@Mapping(target = "phone", source = "userAvailability.user.phone")
	UserAvailabilityDto mapToDto(UserAvailability userAvailability);
}
