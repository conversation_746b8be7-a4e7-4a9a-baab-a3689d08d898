package com.ma.spoton.api.mappers;

import java.time.temporal.ChronoUnit;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.JobExportDto;
import com.ma.spoton.api.dtos.JobExportWithoutAsnDto;
import com.ma.spoton.api.dtos.JobRouteStepDTO;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.JobRouteStep;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        uses = {FleetMapper.class, LocationMapper.class, SpotMapper.class, UserMapper.class,
                JobRouteStepMapper.class, DateMapper.class},
        imports = {Objects.class, ChronoUnit.class, DateTimeUtils.class})
public abstract class JobMapper {

    @Autowired
    protected JobRouteStepMapper jobRouteStepMapper;

    @Named("customRouteMapper")
    protected LinkedHashSet<JobRouteStepDTO> mapJobRouteSteps(Set<JobRouteStep> steps, @Context String timeZone) {
        if (steps == null) {
            return null;
        }
        return steps.stream()
                .map(s -> jobRouteStepMapper.mapToDto(s, timeZone))
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    @Mapping(target = "assignedTo", ignore = true)
    @Mapping(target = "fleet", ignore = true)
    @Mapping(target = "pickupLocation", ignore = true)
    @Mapping(target = "pickupSpot", ignore = true)
    @Mapping(target = "dropLocation", ignore = true)
    @Mapping(target = "dropSpot", ignore = true)
    @Mapping(target = "fleetStatus", ignore = true)
    public abstract Job mapToEntity(JobRequest jobRequest);

    @Mapping(target = "assignedTo", ignore = true)
    @Mapping(target = "fleet", ignore = true)
    @Mapping(target = "pickupLocation", ignore = true)
    @Mapping(target = "pickupSpot", ignore = true)
    @Mapping(target = "dropLocation", ignore = true)
    @Mapping(target = "dropSpot", ignore = true)
    @Mapping(target = "bucket", ignore = true)
    @Mapping(target = "fleetStatus", ignore = true)
    public abstract Job updateEntity(JobRequest jobRequest, @MappingTarget Job job);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "jobId", source = "job.uuid")
    @Mapping(target = "audit", expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(job, timeZone) )")
    @Mapping(target = "jobCompletionSeconds",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                         + " ? ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime()) : null )")
    @Mapping(target = "pickupDateTime", source = "job.pickupDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "dropDateTime", source = "job.dropDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "pickupLocation", qualifiedByName = {"mapToBasicDto"})
    @Mapping(target = "dropLocation", qualifiedByName = {"mapToBasicDto"})
    @Mapping(target = "createdDate", source = "job.createdDate", qualifiedByName = "toFormattedDate")
    @Mapping(target = "scheduleDateTime", source = "job.scheduleDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "isScheduled", expression = "java(job.getIsScheduled() != null && job.getIsScheduled())")
    @Mapping(target = "isBoxTruckOrVan", expression = "java(job.isBoxTruckOrVan())")
    @Mapping(target = "jobRouteSteps", source = "jobRouteSteps", qualifiedByName = "customRouteMapper")
    @Mapping(target = "fleetStatus", source = "job.fleet.fleetStatus.value")
    public abstract JobDto mapToDto(Job job, @Context String timeZone);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "spotCompletionTime",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                         + " ? DateTimeUtils.formatSeconds(ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime()))"
                         + " : null )")
    @Mapping(target = "fleetCarrier", source = "job.fleet.carrier")
    @Mapping(target = "fleetType", source = "job.fleet.type")
    @Mapping(target = "fleetStatus", source = "job.fleet.fleetStatus.value")
    @Mapping(target = "fleetUnitNumber", source = "job.fleet.unitNumber")
    @Mapping(target = "pickupLocationName", source = "job.pickupLocation.locationName")
    @Mapping(target = "pickupSpotName", source = "job.pickupSpot.spotName")
    @Mapping(target = "pickupSpotType", source = "job.pickupSpot.type")
    @Mapping(target = "pickupDateTime", source = "job.pickupDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "dropLocationName", source = "job.dropLocation.locationName")
    @Mapping(target = "dropSpotName", source = "job.dropSpot.spotName")
    @Mapping(target = "dropSpotType", source = "job.dropSpot.type")
    @Mapping(target = "dropDateTime", source = "job.dropDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "createdDate", source = "job.createdDate", qualifiedByName = "toFormattedDate")
    @Mapping(target = "assignedName", source = "job.assignedTo", qualifiedByName = "mapFullName")
    @Mapping(target = "bolUnsigned", expression = "java(Objects.nonNull(job.getUnsignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "bolSigned", expression = "java(Objects.nonNull(job.getSignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "createdByUser", source = "job.createdBy", qualifiedByName = "mapFullName")
    public abstract JobExportDto mapToExportDto(Job job, @Context String timeZone);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "spotCompletionTime",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                         + " ? DateTimeUtils.formatSeconds(ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime())) "
                         + " : null )")
    @Mapping(target = "fleetStatus", source = "job.fleet.fleetStatus.value")
    @Mapping(target = "fleetCarrier", source = "job.fleet.carrier")
    @Mapping(target = "fleetType", source = "job.fleet.type")
    @Mapping(target = "fleetUnitNumber", source = "job.fleet.unitNumber")
    @Mapping(target = "pickupLocationName", source = "job.pickupLocation.locationName")
    @Mapping(target = "pickupSpotName", source = "job.pickupSpot.spotName")
    @Mapping(target = "pickupSpotType", source = "job.pickupSpot.type")
    @Mapping(target = "pickupDateTime", source = "job.pickupDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "dropLocationName", source = "job.dropLocation.locationName")
    @Mapping(target = "dropSpotName", source = "job.dropSpot.spotName")
    @Mapping(target = "dropSpotType", source = "job.dropSpot.type")
    @Mapping(target = "dropDateTime", source = "job.dropDateTime", qualifiedByName = "toFormattedDate")
    @Mapping(target = "createdDate", source = "job.createdDate", qualifiedByName = "toFormattedDate")
    @Mapping(target = "assignedName", source = "job.assignedTo", qualifiedByName = "mapFullName")
    @Mapping(target = "bolUnsigned", expression = "java(Objects.nonNull(job.getUnsignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "bolSigned", expression = "java(Objects.nonNull(job.getSignedBol()) ? \"yes\" : \"no\")")
    public abstract JobExportWithoutAsnDto mapToExportWithoutAsnDto(Job job, @Context String timeZone);
}
