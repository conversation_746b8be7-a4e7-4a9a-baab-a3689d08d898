//package com.ma.spoton.api.mappers;
//
//import java.util.Objects;
//
//import org.mapstruct.Mapper;
//import org.mapstruct.Mapping;
//
//import com.ma.spoton.api.constants.BusinessConstants;
//import com.ma.spoton.api.dtos.CarrierDto;
//import com.ma.spoton.api.dtos.FleetDto;
//import com.ma.spoton.api.entities.Carriers;
//import com.ma.spoton.api.entities.Client;
//import com.ma.spoton.api.entities.Fleet;
//import com.ma.spoton.api.requests.CarriersRequest;
//import com.ma.spoton.api.requests.ClientRequest;
//import com.ma.spoton.api.utils.DateTimeUtils;
//
//@Mapper(componentModel = "spring",
//imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
//public interface CarriersMapper {
//
//	Carriers mapToEntity(CarriersRequest carriersRequest);
//	
//	@Mapping(target = "carrierId", source = "carriers.uuid")
//	@Mapping(target = "Id", source = "carriers.id")
//	CarrierDto mapToDto(Carriers carriers);
//}
