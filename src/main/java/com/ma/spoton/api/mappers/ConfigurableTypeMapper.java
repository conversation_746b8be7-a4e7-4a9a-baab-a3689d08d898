package com.ma.spoton.api.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.dtos.ConfigurableEntityDTO;
import com.ma.spoton.api.entities.ConfigurableTypeEntity;

// TODO : Add custom mapper for any new configurable Type, if needed.

@Mapper(componentModel = "spring")
public interface ConfigurableTypeMapper {

    @Mapping(source = "global", target = "global")
    @Mapping(source = "uuid", target = "id")
    ConfigurableEntityDTO mapToDto(ConfigurableTypeEntity entity);
}
