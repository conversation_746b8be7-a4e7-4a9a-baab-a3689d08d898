package com.ma.spoton.api.mappers;

import java.util.Arrays;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.dtos.RoleDto;
import com.ma.spoton.api.entities.Role;

@Mapper(componentModel = "spring")
public interface RoleMapper {

    @Mapping(target = "roleId", expression = "java(role.getUuid() != null ? role.getUuid().toLowerCase() : null)")
    @Mapping(target = "nameString", expression = "java(formatRoleName(role.getRoleName()))")
    @Mapping(target = "roleName", expression = "java(role.getRoleName())")
    RoleDto mapToDto(Role role);

    default String formatRoleName(String roleName) {
        if (roleName == null) {
            return null;
        }

        // Special cases
        switch (roleName) {
            case "ADMIN":
                return "Admin";
            case "CLIENT":
                return "Client";
            case "SUPERVISOR":
                return "Supervisor";
            case "DRIVER":
                return "Driver";
            case "GUARD":
                return "Guard";
            case "SPOTTER":
                return "Yard Spotter";
            case "IT":
                return "IT";
            case "TRAFFIC":
                return "Traffic";
            case "MATERIAL_HANDLER":
                return "Material Handler";
            case "TEAM_LEAD":
                return "Team Lead";
            case "SECURITY":
                return "Security";
            case "PLANNING":
                return "Planning";
        }

        // Default formatting: replace _ with space, capitalize first letters
        return Arrays.stream(roleName.replace("_", " ").toLowerCase().split(" "))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
                .collect(Collectors.joining(" "));
    }
}
