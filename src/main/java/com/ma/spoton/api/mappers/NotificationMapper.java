package com.ma.spoton.api.mappers;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.entities.Notification;
import com.ma.spoton.api.requests.NotificationRequest;
import com.ma.spoton.api.utils.DateTimeUtils;


@Mapper(componentModel = "spring",
    imports = {DateTimeUtils.class, BusinessConstants.class, AuditMapperImpl.class,
        UserMapperImpl.class, ZoneId.class, ZonedDateTime.class, Objects.class},
    uses = {UserMapper.class, AuditMapper.class, ZoneId.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotificationMapper {


  @Mapping(target = "startTime",
      expression = "java( Objects.nonNull(notificationRequest.getStartTime()) ? DateTimeUtils.convertStringIntoLocalDateTime(notificationRequest.getStartTime(), BusinessConstants.FORM_DATE_TIME_FORMAT, ZoneId.of(timeZone), ZoneId.systemDefault() ) : ZonedDateTime.now() )")
  Notification mapToEntity(NotificationRequest notificationRequest, String timeZone);


}
