package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.MessageDto;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        uses = {UserMapper.class, LocationMapper.class, SpotMapper.class, FleetMapper.class, JobMapper.class,
                AuditMapper.class},
        imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface MessageMapper {

    Message mapToEntity(MessageRequest messageRequest);

    @Mapping(target = "messageId", source = "message.uuid")
    @Mapping(target = "job.fleetStatus", source = "message.job.fleet.fleetStatus.value")
    MessageDto mapToDto(Message message, String timeZone);
}
