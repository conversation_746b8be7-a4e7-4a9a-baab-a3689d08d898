package com.ma.spoton.api.mappers;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.dtos.JobRouteStepDTO;
import com.ma.spoton.api.entities.JobRouteStep;

@Mapper(componentModel = "spring", uses = {LocationMapper.class, SpotMapper.class})
public interface JobRouteStepMapper {

    @Mapping(target = "routeId", source = "jobRouteStep.uuid")
    @Mapping(target = "audit", expression = "java(new AuditMapperImpl(new UserMapperImpl()).mapToDto(jobRouteStep, timeZone))")
    JobRouteStepDTO mapToDto(JobRouteStep jobRouteStep, @Context String timeZone);
}
