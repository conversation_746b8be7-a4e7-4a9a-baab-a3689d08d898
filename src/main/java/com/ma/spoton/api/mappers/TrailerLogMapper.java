package com.ma.spoton.api.mappers;

import java.time.temporal.ChronoUnit;
import java.util.Objects;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.JobExportDto;
import com.ma.spoton.api.dtos.TrailerLogDto;
import com.ma.spoton.api.dtos.TrailerLogExportDto;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.TrailerLog;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
		uses = {UserMapper.class, JobMapper.class, FleetMapper.class},
		imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class}
	)
public interface TrailerLogMapper {

	@Mapping(target = "audit",
		 expression = "java(new AuditMapperImpl(new UserMapperImpl()).mapToDto(trailerLogs, timeZone))")
	TrailerLogDto mapToDto(TrailerLog trailerLogs, @Context String timeZone);

	@Mapping(target = "createdDate",
		 expression = "java( Objects.nonNull(trailerLog.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(trailerLog.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
	@Mapping(target = "pickupDateTime", 
		 expression = "java( Objects.nonNull(trailerLog.getJob()) && Objects.nonNull(trailerLog.getJob().getPickupDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(trailerLog.getJob().getPickupDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
	@Mapping(target = "dropDateTime", 
		 expression = "java( Objects.nonNull(trailerLog.getJob()) && Objects.nonNull(trailerLog.getJob().getDropDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(trailerLog.getJob().getDropDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
	@Mapping(target = "jobNumber", source = "trailerLog.job.id")
	@Mapping(target = "priority", source = "trailerLog.job.priority")
	@Mapping(target = "pickupLocationName", source = "trailerLog.job.pickupLocation.locationName")
	@Mapping(target = "pickupSpotName", source = "trailerLog.job.pickupSpot.spotName")
	@Mapping(target = "dropLocationName", source = "trailerLog.job.dropLocation.locationName")
	@Mapping(target = "dropSpotName", source = "trailerLog.job.dropSpot.spotName")
	@Mapping(target = "description", source = "trailerLog.job.description")
	@Mapping(target = "fleetUnitNumber", source = "trailerLog.fleet.unitNumber")
	@Mapping(target = "status", source = "trailerLog.job.status")
	@Mapping(target = "assignedTo",
		 expression = "java(Objects.nonNull(trailerLog.getJob()) && Objects.nonNull(trailerLog.getJob().getAssignedTo()) && Objects.nonNull(trailerLog.getJob().getAssignedTo().getFirstName()) && Objects.nonNull(trailerLog.getJob().getAssignedTo().getLastName()) ? trailerLog.getJob().getAssignedTo().getFirstName() + \" \" + trailerLog.getJob().getAssignedTo().getLastName() : \"\" )")
	@Mapping(target = "createdBy",
		 expression = "java(Objects.nonNull(trailerLog.getCreatedBy()) && Objects.nonNull(trailerLog.getCreatedBy().getFirstName()) && Objects.nonNull(trailerLog.getCreatedBy().getLastName()) ? trailerLog.getCreatedBy().getFirstName() + \" \" + trailerLog.getCreatedBy().getLastName() : \"\" )")
	@Mapping(target = "lastModifiedDate",
		 expression = "java( Objects.nonNull(trailerLog.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(trailerLog.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
	TrailerLogExportDto mapToExportDto(TrailerLog trailerLog, String timeZone);
	
}
