package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.FleetAgeReportDto;
import com.ma.spoton.api.dtos.FleetCsvExportDto;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.FleetExportDto;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Suppliers;
import com.ma.spoton.api.requests.FleetRequest;
import com.ma.spoton.api.services.FleetService;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class,
                   StringUtils.class, FleetService.class, Suppliers.class},
        uses = {SpotMapper.class, ClientMapper.class, SuppliersMapper.class})
public interface FleetMapper {

    @Mapping(target = "fleetStatus", ignore = true)
    Fleet mapToEntity(FleetRequest fleetRequest);

    @Mapping(target = "fleetStatus", ignore = true)
    Fleet updateEntity(FleetRequest fleetRequest, @MappingTarget Fleet fleet);

    @Mapping(target = "fleetId", source = "fleet.uuid")
    @Mapping(target = "clients", source = "fleet.clients")
    @Mapping(target = "audit",
            expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(fleet, timeZone) )")
    @Mapping(target = "fleetStatus", source = "fleet.fleetStatus.value")
    FleetDto mapToDto(Fleet fleet, String timeZone);

    @Mapping(target = "fleetId", source = "fleet.uuid")
    @Mapping(target = "spot", ignore = true)
    @Mapping(target = "audit", ignore = true)
    @Mapping(target = "clients", ignore = true)
    @Mapping(target = "fleetStatus", source = "fleet.fleetStatus.value")
    FleetDto mapToDto(Fleet fleet);

    @Mapping(target = "createdDate",
            expression = "java( Objects.nonNull(fleet.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleet.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "createdBy", expression = "java(fleet.getCreatedBy().getFirstName() + \" \" + fleet.getCreatedBy().getLastName())")
    @Mapping(target = "lastModifiedDate",
            expression = "java( Objects.nonNull(fleet.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleet.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "fleetStatus", source = "fleet.fleetStatus.value")
    FleetExportDto mapToExportDto(Fleet fleet, String timeZone);

    FleetCsvExportDto mapToFleetCsvExportDto(FleetExportDto fleetExportDto);

    @Mapping(target = "trailerStatus",
            expression = "java( Objects.nonNull(fleet.getFleetStatus()) ?"
                         + "\"FULL\".equals(fleet.getFleetStatus().getValue()) ?"
                         + "\"LOADED\" : StringUtils.defaultIfBlank(fleet.getFleetStatus().getValue(), \"\")"
                         + " : \"\" )")
    @Mapping(target = "location", source = "fleet.spot.location.locationName")
    @Mapping(target = "suppliers",
            expression = "java( StringUtils.join(fleet.getSupplier().stream().map(Suppliers::getSupplier).toArray(), \",\"))")
    @Mapping(target = "trailerAge", ignore = true)
    FleetAgeReportDto mapToAgeReportDto(Fleet fleet);
}
