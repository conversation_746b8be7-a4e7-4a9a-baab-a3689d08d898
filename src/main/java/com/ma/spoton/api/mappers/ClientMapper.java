package com.ma.spoton.api.mappers;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.ClientConfigDto;
import com.ma.spoton.api.dtos.ClientDto;
import com.ma.spoton.api.dtos.ClientExportDto;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.requests.ClientRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
    imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface ClientMapper {

  Client mapToEntity(ClientRequest clientRequest);

  Client updateEntity(ClientRequest clientRequest, @MappingTarget Client client);

  @Mapping(target = "clientId", source = "client.uuid")
  @Mapping(target = "audit",
      expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(client, timeZone) )")
  ClientDto mapToDto(Client client, String timeZone);

  @Mapping(target = "clientId", source = "client.uuid")
  @Mapping(target = "audit", ignore = true)
  ClientDto mapToDto(Client client);

  @Mapping(target = "clientId", source = "client.uuid")
  @Mapping(target = "createdBy", source = "client.createdBy.email")
  @Mapping(target = "lastModifiedBy", source = "client.lastModifiedBy.email")
  @Mapping(target = "createdDate",
      expression = "java( Objects.nonNull(client.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(client.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "lastModifiedDate",
      expression = "java( Objects.nonNull(client.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(client.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  ClientExportDto mapToExportDto(Client client, String timeZone);

  
  ClientConfigDto maptoConfig(Client client);
}
