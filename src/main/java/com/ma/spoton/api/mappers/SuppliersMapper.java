package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
//import com.ma.spoton.api.dtos.CarrierDto;
import com.ma.spoton.api.dtos.SuppliersDto;
//import com.ma.spoton.api.entities.Carriers;
import com.ma.spoton.api.entities.Suppliers;
import com.ma.spoton.api.requests.SuppliersRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface SuppliersMapper {

	Suppliers mapToEntity(SuppliersRequest suppliersRequest);
	
	@Mapping(target = "supplierId", source = "suppliers.uuid")
	// @Mapping(target = "Id", source = "suppliers.id")
	SuppliersDto mapToDto(Suppliers suppliers);
	
}
