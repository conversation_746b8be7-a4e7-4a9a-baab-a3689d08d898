package com.ma.spoton.api.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.entities.UserDevice;
import com.ma.spoton.api.requests.RegisterUserDeviceRequest;

@Mapper(componentModel = "spring")
public interface UserDeviceMapper {

  UserDevice mapToEntity(RegisterUserDeviceRequest userDeviceRequest);

  UserDevice updateEntity(RegisterUserDeviceRequest userDeviceRequest,
      @MappingTarget UserDevice userDevice);

}
