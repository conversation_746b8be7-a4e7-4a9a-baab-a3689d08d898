package com.ma.spoton.api.mappers;

import static com.ma.spoton.api.repositories.DashboardRepository.WeeklyTotalSpots;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.ma.spoton.api.dtos.WeeklyTotalSpotsDTO;

@Mapper(componentModel = "spring")
public interface WeeklyTotalSpotsMapper {

    WeeklyTotalSpotsMapper INSTANCE = Mappers.getMapper(WeeklyTotalSpotsMapper.class);

    WeeklyTotalSpotsDTO mapToDTO(WeeklyTotalSpots weeklyTotalSpots);
}
