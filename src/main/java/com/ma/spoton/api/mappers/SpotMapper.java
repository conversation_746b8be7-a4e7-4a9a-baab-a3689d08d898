package com.ma.spoton.api.mappers;

import static com.ma.spoton.api.repositories.DashboardRepository.InTransitSpot;
import static com.ma.spoton.api.repositories.DashboardRepository.SpotCounts;
import static com.ma.spoton.api.repositories.DashboardRepository.SpotIdName;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.FleetSpotExportDto;
import com.ma.spoton.api.dtos.InTransitSpotDTO;
import com.ma.spoton.api.dtos.SpotCountsDTO;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.dtos.SpotExportDto;
import com.ma.spoton.api.dtos.SpotIdNameDTO;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.requests.SpotRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class},
        uses = {FleetMapper.class})
public interface SpotMapper {

    Spot mapToEntity(SpotRequest locationRequest);

    Spot updateEntity(SpotRequest locationRequest, @MappingTarget Spot spot);

    @Mapping(target = "locationName", source = "spot.location.locationName")
    @Mapping(target = "locationId", source = "spot.location.uuid")
    @Mapping(target = "spotId", source = "spot.uuid")
    @Mapping(target = "audit",
            expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(spot, timeZone) )")
    @Mapping(target = "pickUpTrailers", expression = "java(getPickUpTrailerUnitNumbers(spot))")
    @Mapping(target = "dropOffTrailers", expression = "java(getDropOffTrailerUnitNumbers(spot))")
    SpotDto mapToDto(Spot spot, String timeZone);

    @Mapping(target = "locationName", source = "spot.location.locationName")
    @Mapping(target = "locationId", source = "spot.location.uuid")
    @Mapping(target = "spotId", source = "spot.uuid")
    SpotDto mapToDto(Spot spot);

    @Mapping(target = "spotId", source = "spot.uuid")
    @Mapping(target = "locationName", source = "spot.location.locationName")
    @Mapping(target = "createdBy", source = "spot.createdBy.email")
    @Mapping(target = "lastModifiedBy", source = "spot.lastModifiedBy.email")
    @Mapping(target = "fleetCarrier", source = "spot.fleet.carrier")
    @Mapping(target = "fleetStatus", source = "spot.fleet.fleetStatus.value")
    @Mapping(target = "fleetType", source = "spot.fleet.type")
    @Mapping(target = "fleetUnitNumber", source = "spot.fleet.unitNumber")
    @Mapping(target = "createdDate",
            expression = "java( Objects.nonNull(spot.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(spot.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "lastModifiedDate",
            expression = "java( Objects.nonNull(spot.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(spot.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    SpotExportDto mapToExportDto(Spot spot, String timeZone);

    FleetSpotExportDto mapToFleetSpotExportDto(Spot spot);

    default List<String> getPickUpTrailerUnitNumbers(Spot spot) {
        if (spot.getPickUpJobs() == null) {
            return Collections.emptyList();
        }
        return spot.getPickUpJobs().stream()
                .map(Job::getFleet)
                .filter(Objects::nonNull)
                .map(Fleet::getUnitNumber)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    default List<String> getDropOffTrailerUnitNumbers(Spot spot) {
        if (spot.getDropOffJobs() == null) {
            return Collections.emptyList();
        }
        return spot.getDropOffJobs().stream()
                .map(Job::getFleet)
                .filter(Objects::nonNull)
                .map(Fleet::getUnitNumber)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    InTransitSpotDTO mapToInTransitSpotDto(InTransitSpot spot, @Context String timeZone);

    List<InTransitSpotDTO> mapToInTransitSpotDtos(List<InTransitSpot> spots, @Context String timeZone);

    SpotIdNameDTO mapToDto(SpotIdName spot);

    List<SpotIdNameDTO> mapToDto(List<SpotIdName> spots);

    SpotCountsDTO mapToDto(SpotCounts spotCounts);
}
