package com.ma.spoton.api.mappers;

import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.TrailerAuditDto;
import com.ma.spoton.api.dtos.TrailerAuditExportDto;
import com.ma.spoton.api.dtos.TrailerAuditPdfExportDto;
import com.ma.spoton.api.entities.TrailerAudit;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface TrailerAuditMapper {

    TrailerAudit mapToEntity(TrailerAuditRequest trailerAuditRequest);

    @Mapping(target = "audit",
            expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(trailerAudit, timeZone) )")
    @Mapping(target = "carrier", source = "trailerAudit.fleet.carrier")
    @Mapping(target = "trailerNumber", source = "trailerAudit.fleet.unitNumber")
    TrailerAuditDto mapToDto(TrailerAudit trailerAudit, String timeZone);

    @Mapping(target = "dateAndTimeEntered",
            expression = "java( Objects.nonNull(trailerAudit.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(trailerAudit.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "carrier", source = "trailerAudit.fleet.carrier")
    @Mapping(target = "trailerNumber", source = "trailerAudit.fleet.unitNumber")
    TrailerAuditExportDto mapToExportDto(TrailerAudit trailerAudit, String timeZone);

    @Mapping(target = "dateAndTimeEntered",
            expression = "java( Objects.nonNull(trailerAudit.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(trailerAudit.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "carrier", source = "trailerAudit.fleet.carrier")
    @Mapping(target = "trailerNumber", source = "trailerAudit.fleet.unitNumber")
    TrailerAuditPdfExportDto mapToPdfExportDto(TrailerAudit trailerAudit, String timeZone);
}
