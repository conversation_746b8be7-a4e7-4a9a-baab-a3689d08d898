package com.ma.spoton.api.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.google.common.base.Objects;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.MobileAppVersionDto;
import com.ma.spoton.api.entities.MobileAppVersion;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class})
public interface MobileAppVersionMapper {
	
	
    @Mapping(target = "appVersion", source = "mobileAppVersion.version")
	MobileAppVersionDto maptoDto(MobileAppVersion mobileAppVersion);
}
