package com.ma.spoton.api.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

@JsonInclude(NON_EMPTY)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {

	private String userId;
	private String firstName;
	private String lastName;
	private String email;
	private String phone;
	private String timeZone;
	private String lastLoginTime;
	private Boolean isActive;
	private Boolean isADUser;

	private List<RoleDto> roles;
	private List<ClientDto> clients;
	private ZonedDateTime idleSince;
	private Long idleTime;
	private Long pendingOverTime;
	private Boolean isOnOverTime;
	private Long pendingBreakTime;
	private Boolean isOnBreak;
	private List<LocationDto> locations;
}
