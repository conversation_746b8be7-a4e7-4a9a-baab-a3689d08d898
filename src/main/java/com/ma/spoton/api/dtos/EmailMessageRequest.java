package com.ma.spoton.api.dtos;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EmailMessageRequest {

  private List<MailRecipient> recipients;
  private List<MailRecipient> ccRecipients;
  private List<MailRecipient> bccRecipients;
  private String replyTo;
  private String subject;
  private Object content;
  private boolean isAttachment;
  private Map<String, Attachment> attachmentMap;

}
