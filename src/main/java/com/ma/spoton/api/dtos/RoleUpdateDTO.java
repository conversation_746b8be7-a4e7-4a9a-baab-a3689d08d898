package com.ma.spoton.api.dtos;


import com.ma.spoton.api.entities.Role;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RoleUpdateDTO {

    private String uuid; // Corresponding to BaseEntity's id or UUID
    private String roleName;

    public RoleUpdateDTO(Role role) {
        this.uuid = role.getUuid(); // Assuming BaseEntity has a getUuid() method
        this.roleName = role.getRoleName();
    }
}
