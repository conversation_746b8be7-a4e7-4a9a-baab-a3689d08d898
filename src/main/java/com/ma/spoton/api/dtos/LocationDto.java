package com.ma.spoton.api.dtos;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder
@ToString
public class LocationDto {

  private String clientId;
  private String locationId;
  private String locationName;
  private String street;
  private String city;
  private String state;
  private String zip;
  private String country;
  private Double latitude;
  private Double longitude;
  private String locationMapJson;
  private String remarks;
  private Boolean isActive;
  private Boolean isDefault;
  private String mapImageUrl;
  private String pieChartColor;

  private AuditDto audit;

}
