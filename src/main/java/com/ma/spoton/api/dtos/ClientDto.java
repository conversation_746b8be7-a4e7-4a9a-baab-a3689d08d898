package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientDto {

  private String clientId;
  private String clientName;
  private String street;
  private String city;
  private String state;
  private String zip;
  private String country;
  private String contactPerson;
  private String contactEmail;
  private String contactPhone;
  private String remarks;
  private Boolean isActive;
  private Boolean dvir;
  private Boolean bol;
  private Boolean accountDeactivation;
  private Boolean trailerAudit;
  private AuditDto audit;
  private String timeZone;
  private float overTime;
  private long driverJobReAssignAfter;
  private long spotterJobReAssignAfter;
  private Boolean jobReAssign;
}
