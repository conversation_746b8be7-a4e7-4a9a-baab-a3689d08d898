package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.GuardEntryExit.Type;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardEntryExitExportDto {

  private String guardEntryExitId;
  private String locationName;
  private String fleetCarrier;
  private com.ma.spoton.api.entities.Fleet.Type fleetType;
  private String fleetUnitNumber;
  private Type type;
  private String notes;
  private String proNumber;
  private String spotName;
  private String driverName;
  private String billOfLading;
  private String carrier;
  private String suppliers;
  private String sub;
  private String dateOfPickup;
  private String loadStatus;
  private String dueAtPlant;
  private String sequenceNumber;
  private String dateOfArrival;
  private String tractorNumber;

  private String createdDate;
  private String lastModifiedDate;
  private String createdBy;
  private String lastModifiedBy;

  private String billOfLandingImage;
  
}
