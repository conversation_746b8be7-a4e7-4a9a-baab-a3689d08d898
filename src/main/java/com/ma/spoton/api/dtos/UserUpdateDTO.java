package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.User;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_EMPTY)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateDTO {

	private String uuid;
    private Set<ClientUpdateDTO> clients;
    private Set<RoleUpdateDTO> roles; // Use RoleUpdateDTO instead of Role

    public UserUpdateDTO(User user) {
        this.uuid = user.getUuid();

        // Convert clients to ClientDTOs
        if (user.getClients() != null) {
            this.clients = user.getClients().stream()
                .map(ClientUpdateDTO::new)
                .collect(Collectors.toSet());
        }

        // Convert roles to RoleUpdateDTOs
        if (user.getRoles() != null) {
            this.roles = user.getRoles().stream()
                .map(RoleUpdateDTO::new)
                .collect(Collectors.toSet());
        } else {
            this.roles = Set.of(); // or Collections.emptySet()
        }
    }
}
