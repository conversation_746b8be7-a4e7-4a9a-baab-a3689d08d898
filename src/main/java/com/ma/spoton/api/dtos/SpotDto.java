package com.ma.spoton.api.dtos;

import java.time.ZonedDateTime;
import java.util.List;

import com.ma.spoton.api.entities.Spot.SpotType;
import com.ma.spoton.api.entities.Spot.Status;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder
@ToString
public class SpotDto {

    private String locationId;
    private String spotId;
    private String spotName;
    private SpotType type;
    private Status status;
    private Double latitude;
    private Double longitude;
    private String remarks;
    private Boolean isActive;
    private Long emptiedSinceSeconds;
    private Long occupiedSinceSeconds;
    private FleetDto fleet;
    private String locationName;
    private ZonedDateTime lastOccupiedTime;
    private ZonedDateTime lastEmptiedTime;
    private Boolean isOccupied;
    private AuditDto audit;
    private List<String> pickUpTrailers;
    private List<String> dropOffTrailers;
    private String nextTrailer;
    private Status nextJobStatus;
    private String notes;

}
