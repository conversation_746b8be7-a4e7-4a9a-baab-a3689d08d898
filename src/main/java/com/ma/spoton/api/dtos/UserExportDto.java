package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_EMPTY)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExportDto {

  private String userId;
  private String firstName;
  private String lastName;
  private String email;
  private String phone;
  private String timeZone;
  private String lastLoginTime;
  private Boolean isActive;
  private String roles;

}
