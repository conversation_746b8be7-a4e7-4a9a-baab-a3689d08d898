package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientExportDto {

  private String clientId;
  private String clientName;
  private String street;
  private String city;
  private String state;
  private String zip;
  private String country;
  private String contactPerson;
  private String contactEmail;
  private String contactPhone;
  private String remarks;
  private Boolean isActive;

  private String createdDate;
  private String lastModifiedDate;
  private String createdBy;
  private String lastModifiedBy;

}
