package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import java.time.LocalDate;
import java.util.List;

//import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.GuardEntryExit.Type;
import com.ma.spoton.api.entities.Suppliers;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardEntryExitDto {

  private String guardEntryExitId;
  private LocationDto location;
  private FleetDto fleet;
  private Type type;
  private String notes;
  private String tractorNumber;
  private String carrier;
  private String supplier;
  private List<SuppliersDto> suppliers;
  private String sequenceNumber;
  private String loadStatus;
  private String dateOfArrival;
  private String dateOfPickup;
  private String driver;
  private SpotDto spot;
  
  private String billOfLandingType;
  private String billOfLandingImage;
  
  private AuditDto audit;
}
