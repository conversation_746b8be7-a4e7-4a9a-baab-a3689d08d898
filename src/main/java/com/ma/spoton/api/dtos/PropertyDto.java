package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Property.AccessType;
import com.ma.spoton.api.entities.Property.PropertyType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PropertyDto {

  private String propertyId;
  private String key;
  private String value;
  private AccessType accessType;
  private Boolean isActive;
  private AuditDto audit;
  private PropertyType type;

}
