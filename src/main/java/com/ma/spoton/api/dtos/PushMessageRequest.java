package com.ma.spoton.api.dtos;

import java.util.Set;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Component
public class PushMessageRequest {

  private String messageId;
  private String messageTitle;
  private String messageBody;
 private Set<String> userDeviceIds;
  private String topicId;

}