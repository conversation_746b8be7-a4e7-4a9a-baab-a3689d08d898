package com.ma.spoton.api.dtos;

import static com.ma.spoton.api.entities.JobRouteStep.StepStatus;
import static com.ma.spoton.api.entities.JobRouteStep.StepType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JobRouteStepDTO {

    private String routeId;
    private LocationDto location;
    private SpotDto spot;
    private StepType stepType; // PICK_UP or DROP
    private int stepOrder;
    private StepStatus stepStatus;
    private String notes;
    private AuditDto audit;
}
