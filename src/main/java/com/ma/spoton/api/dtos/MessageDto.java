package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Message.Status;
import com.ma.spoton.api.entities.Message.Type;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageDto {

  private String messageId;
  private String messageBody;
  private Type type;
  private Status status;

  private UserDto fromUser;
  private UserDto toUser;

  private LocationDto clientLocation;
  private FleetDto fleet;

  private LocationDto pickupLocation;
  private SpotDto pickupSpot;
  private LocationDto dropLocation;
  private SpotDto dropSpot;
  
  private String sequenceAsn;

  private AuditDto audit;
  
  private JobDto job;

}
