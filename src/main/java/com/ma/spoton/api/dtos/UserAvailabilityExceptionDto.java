package com.ma.spoton.api.dtos;

import java.time.LocalDate;
import java.time.LocalTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.UserAvailabilityException.Type;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAvailabilityExceptionDto {
	
	    private String userAvailabilityExceptionId;
	    private String firstName;
		private String lastName;
		private String email;
		private String phone;
	    private LocalDate date;
	    private Type type;
	    private LocalTime startingTime;
	    private LocalTime endingTime;
	    private LocalTime breakStartingTime;
		private LocalTime breakEndingTime;
}
