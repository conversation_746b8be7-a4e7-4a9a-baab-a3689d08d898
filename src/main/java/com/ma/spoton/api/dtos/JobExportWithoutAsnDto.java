package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.Priority;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.entities.Spot.SpotType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobExportWithoutAsnDto {

    private String jobNumber;

    private String createdDate;
    private String scheduleDateTime;
    private Boolean isScheduled;

    private String fleetCarrier;
    private Type fleetType;
    private String fleetUnitNumber;
    private String fleetStatus;
    private String description;
    private Priority priority;
    private Status status;

    // pickup
    private String pickupLocationName;
    private String pickupSpotName;
    private SpotType pickupSpotType;
    private String pickupDateTime;
    private String pickupNotes;

    // drop
    private String dropLocationName;
    private String dropSpotName;
    private SpotType dropSpotType;
    private String dropDateTime;
    private String dropNotes;

    private String spotCompletionTime;


    /*private String createdDate;
    private String lastModifiedDate;
    private String assigneeFirstName;
    private String assigneeLastName;
    */
    private String assignedName;
    private String bolUnsigned;
    private String bolSigned;

    private Double temperature;

    private String climate;

}
