package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import java.util.Set;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.entities.Job.Priority;
import com.ma.spoton.api.entities.Job.Status;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobDto {

    private String jobId;
    private String jobNumber;

    private UserDto assignedTo;
    private FleetDto fleet;
    private String fleetStatus;
    private String description;
    private Priority priority;
    private Status status;

    // pickup
    private LocationDto pickupLocation;
    private SpotDto pickupSpot;
    private String pickupDateTime;
    private String pickupNotes;

    // drop
    private LocationDto dropLocation;
    private SpotDto dropSpot;
    private String dropDateTime;
    private String dropNotes;

    private Long jobCompletionSeconds;

    private AuditDto audit;

    private Boolean signedBol;

    private Boolean unsignedBol;

    private Set<BolDto> bols;

    private Double temperature;

    private String climate;

    private String sequenceAsn;

    private Bucket bucket;
    private String createdDate;
    private String scheduleDateTime;
    private Boolean isScheduled;

    private Set<JobRouteStepDTO> jobRouteSteps;
    private boolean isBoxTruckOrVan;
}
