package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import java.util.Set;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Fleet.Type;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetExportDto {

    private String carrier;
    private Type type;
    private String unitNumber;
    private String remarks;
    private Boolean isActive;
    private String owner;
    private String fleetStatus;
    private String createdDate;
    private String createdBy;
    private FleetSpotExportDto spot;
    private String lastModifiedDate;

    private String locationName;
    private String spotName;

    private String sequenceNumber;
    private Set<SuppliersDto> supplier;

}
