package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import static com.ma.spoton.api.repositories.DashboardRepository.InTransitSpot;

import lombok.Data;

@Data
@JsonInclude(NON_EMPTY)
public class InTransitSpots {
    List<InTransitSpotDTO> inTransitSpots = new ArrayList<>();
}
