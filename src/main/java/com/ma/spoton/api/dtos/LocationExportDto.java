package com.ma.spoton.api.dtos;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder
@ToString
public class LocationExportDto {

  private String locationId;
  private String locationName;
  private String clientName;
  private String street;
  private String city;
  private String state;
  private String zip;
  private String country;
  private String remarks;
  private Boolean isActive;
  private Boolean isDefault;
  private String mapImageUrl;

  private String createdDate;
  private String lastModifiedDate;
  private String createdBy;
  private String lastModifiedBy;

}
