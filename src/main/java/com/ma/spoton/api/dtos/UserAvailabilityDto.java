package com.ma.spoton.api.dtos;

import java.time.DayOfWeek;
import java.time.LocalTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAvailabilityDto {

	private String userAvailabilityId;
	private DayOfWeek dayOfWeek;
	private LocalTime startingTime;	
	private LocalTime endingTime;
	private String firstName;
	private String lastName;
	private String email;
	private String phone;
	private Boolean isActive;
	private LocalTime breakStartingTime;
	private LocalTime breakEndingTime;
}
