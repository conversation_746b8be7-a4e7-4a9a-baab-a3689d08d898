package com.ma.spoton.api.services;

import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.utils.SecurityUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class FleetServiceImplTest {
	@InjectMocks
	FleetServiceImpl fleetServiceImpl;

	@Mock
	FleetRepository fleetRepository;

	List<String> carriers = List.of("ABC", "Pegasus", "Leon", "   ", "Qwerty");

	@Test
	void getUniqueCarriers() {
		Mockito.when(fleetRepository.findUniqueCarrier()).thenReturn(carriers);
		List<String> uniqueCarriers = fleetServiceImpl.getUniqueCarrier();
		assertTrue(!uniqueCarriers.contains("   "));
	}

	@Test
	void findUniqueCarrier() {
		String searchCarrier = "rty";
		Mockito.when(fleetRepository.findUniqueCarrier(searchCarrier)).thenReturn(List.of("Qwerty"));
		List<String> uniqueCarriers = fleetServiceImpl.getUniqueCarrier(searchCarrier);
		assertEquals(List.of("Qwerty"), uniqueCarriers);
	}

	@Test
	void encryptPassword() {
		String pwd = "test@123";
		System.out.println(SecurityUtils.encryptPassword(pwd));
	}
}
