
#!/bin/bash

#give permission for everything in the spoton-api directory
 sudo chmod -R 777 /home/<USER>/spoton-api

 DIR="/home/<USER>/spoton-api/"
 if [ -d "$DIR" ]; then
   echo "${DIR} exists"
 else
   echo "Creating ${DIR} directory"
   mkdir ${DIR}
 fi

mvn -version

cp /home/<USER>/application.yml /home/<USER>/spoton-api/src/main/resources/

cd /home/<USER>/spoton-api

pm2 start scripts/spoton-jar_start.sh --name spoton --no-autorestart

#pm2 start "mvn spring-boot:run" --name SERVER --no-autorestart

sleep 60

free -h && sudo sysctl -w vm.drop_caches=3 && sudo sync && echo 3 | sudo tee /proc/sys/vm/drop_caches && free -h